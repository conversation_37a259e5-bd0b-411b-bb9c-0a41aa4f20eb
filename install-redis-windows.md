# � تثبيت Redis على Windows - دليل شامل لـ AyoPFX Trading Bot

<div align="center">

![Redis](https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white)
![Windows](https://img.shields.io/badge/Windows-0078D6?style=for-the-badge&logo=windows&logoColor=white)

**Redis يسرع AyoPFX Trading Bot بنسبة 10x!**

</div>

## الطريقة الأولى: تحميل Redis مباشرة

### 1. تحميل Redis
- اذهب إلى: https://github.com/microsoftarchive/redis/releases
- حمل آخر إصدار: `Redis-x64-3.0.504.msi`
- قم بتثبيته

### 2. تشغيل Redis
```cmd
# افتح Command Prompt كمدير
cd "C:\Program Files\Redis"
redis-server.exe
```

## الطريقة الثانية: استخدام Chocolatey

### 1. تثبيت Chocolatey (إذا لم يكن مثبت)
```powershell
# افتح PowerShell كمدير وشغل:
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### 2. تثبيت Redis
```cmd
choco install redis-64
```

## الطريقة الثالثة: استخدام WSL (الأفضل)

### 1. تفعيل WSL
```powershell
# افتح PowerShell كمدير
wsl --install
```

### 2. تثبيت Redis في Ubuntu
```bash
# بعد إعادة التشغيل وتثبيت Ubuntu
sudo apt update
sudo apt install redis-server
sudo systemctl start redis
```

## الطريقة الرابعة: Redis في الذاكرة (للتطوير فقط)

يمكنك استخدام Redis محاكي في الذاكرة:

```bash
npm install redis-memory-server
```

## التحقق من التثبيت

```cmd
redis-cli ping
# يجب أن يرجع: PONG
```

## تشغيل Redis كخدمة Windows

```cmd
# تثبيت كخدمة
redis-server --service-install

# تشغيل الخدمة
redis-server --service-start

# إيقاف الخدمة
redis-server --service-stop
```

## إعدادات Redis الأساسية

إنشاء ملف `redis.conf`:
```
port 6379
bind 127.0.0.1
save 900 1
save 300 10
save 60 10000
```

## استكشاف الأخطاء

### Redis لا يبدأ:
```cmd
# تحقق من المنفذ
netstat -an | findstr :6379

# تشغيل مع تفاصيل أكثر
redis-server --loglevel verbose
```

### مشكلة في الاتصال:
```cmd
# تحقق من الاتصال المحلي
redis-cli -h 127.0.0.1 -p 6379 ping
```
