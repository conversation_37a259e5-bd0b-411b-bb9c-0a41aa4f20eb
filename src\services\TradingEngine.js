import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import IndicatorEngine from '../indicators/IndicatorEngine.js';
import RiskManager from '../strategies/RiskManager.js';
import MarketAnalyzer from './MarketAnalyzer.js';
import { cacheUtils } from '../database/redis.js';

class TradingEngine extends EventEmitter {
  constructor({ dataService, aiService, notificationService, io }) {
    super();
    
    this.dataService = dataService;
    this.aiService = aiService;
    this.notificationService = notificationService;
    this.io = io;
    
    this.indicatorEngine = new IndicatorEngine();
    this.riskManager = new RiskManager();
    this.marketAnalyzer = new MarketAnalyzer();
    
    this.isRunning = false;
    this.analysisInterval = null;
    this.subscriptions = new Map();
    this.activeSignals = new Map();
    this.performanceMetrics = {
      totalSignals: 0,
      successfulSignals: 0,
      failedSignals: 0,
      totalAnalyses: 0,
      averageConfidence: 0,
      lastAnalysis: null
    };
  }

  // Initialize the trading engine
  async initialize() {
    try {
      logger.info('Initializing Trading Engine...');
      
      // Validate dependencies
      if (!this.dataService || !this.notificationService) {
        throw new Error('Required services not provided');
      }
      
      // Set up event listeners
      this.setupEventListeners();
      
      logger.info('✅ Trading Engine initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Trading Engine initialization failed:', error);
      throw error;
    }
  }

  // Start the trading engine
  async start() {
    try {
      if (this.isRunning) {
        logger.warn('Trading Engine is already running');
        return;
      }

      logger.info('🚀 Starting Trading Engine...');

      // Subscribe to all configured trading pairs
      await this.subscribeToTradingPairs();

      // Start periodic market analysis
      this.startPeriodicAnalysis();

      // Start real-time data processing
      this.startRealTimeProcessing();

      this.isRunning = true;
      
      logger.info('✅ Trading Engine started successfully');
      this.emit('engineStarted');

      // Send startup notification
      await this.notificationService.sendSystemAlert({
        type: 'engine_start',
        message: 'Trading Engine started successfully',
        level: 'info'
      });

    } catch (error) {
      logger.error('❌ Trading Engine start failed:', error);
      throw error;
    }
  }

  // Stop the trading engine
  async stop() {
    try {
      if (!this.isRunning) {
        logger.warn('Trading Engine is not running');
        return;
      }

      logger.info('🛑 Stopping Trading Engine...');

      // Stop periodic analysis
      if (this.analysisInterval) {
        clearInterval(this.analysisInterval);
        this.analysisInterval = null;
      }

      // Unsubscribe from all data feeds
      await this.unsubscribeFromAllPairs();

      this.isRunning = false;
      
      logger.info('✅ Trading Engine stopped successfully');
      this.emit('engineStopped');

      // Send shutdown notification
      await this.notificationService.sendSystemAlert({
        type: 'engine_stop',
        message: 'Trading Engine stopped',
        level: 'info'
      });

    } catch (error) {
      logger.error('❌ Trading Engine stop failed:', error);
      throw error;
    }
  }

  // Subscribe to trading pairs
  async subscribeToTradingPairs() {
    try {
      const pairs = config.trading.supportedPairs;
      
      for (const pair of pairs) {
        await this.subscribeToSymbol(pair);
      }
      
      logger.info(`Subscribed to ${pairs.length} trading pairs`);
    } catch (error) {
      logger.error('Trading pairs subscription error:', error);
      throw error;
    }
  }

  // Subscribe to a specific symbol
  async subscribeToSymbol(symbol) {
    try {
      // Subscribe to real-time data
      const unsubscribe = this.dataService.subscribeToRealTimeData(symbol, (symbol, priceData) => {
        this.handleRealTimePrice(symbol, priceData);
      });
      
      this.subscriptions.set(symbol, unsubscribe);
      
      logger.info(`Subscribed to real-time data for ${symbol}`);
    } catch (error) {
      logger.error(`Symbol subscription error for ${symbol}:`, error);
    }
  }

  // Unsubscribe from all pairs
  async unsubscribeFromAllPairs() {
    try {
      for (const [symbol, unsubscribe] of this.subscriptions) {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      }
      
      this.subscriptions.clear();
      logger.info('Unsubscribed from all trading pairs');
    } catch (error) {
      logger.error('Unsubscription error:', error);
    }
  }

  // Start periodic market analysis
  startPeriodicAnalysis() {
    const interval = config.trading.updateInterval || 60000; // 1 minute default
    
    this.analysisInterval = setInterval(async () => {
      try {
        await this.performPeriodicAnalysis();
      } catch (error) {
        logger.error('Periodic analysis error:', error);
      }
    }, interval);
    
    logger.info(`Periodic analysis started with ${interval}ms interval`);
  }

  // Perform periodic analysis for all pairs
  async performPeriodicAnalysis() {
    try {
      const pairs = config.trading.supportedPairs;
      const timeframes = config.trading.analysisTimeframes;
      
      for (const pair of pairs) {
        for (const timeframe of timeframes) {
          try {
            await this.analyzeSymbol(pair, timeframe);
          } catch (error) {
            logger.error(`Analysis error for ${pair} ${timeframe}:`, error);
          }
        }
      }
      
      this.performanceMetrics.totalAnalyses++;
      this.performanceMetrics.lastAnalysis = new Date().toISOString();
      
    } catch (error) {
      logger.error('Periodic analysis error:', error);
    }
  }

  // Analyze a specific symbol and timeframe
  async analyzeSymbol(symbol, timeframe) {
    try {
      // Get historical data
      const historicalData = await this.dataService.getHistoricalData(symbol, timeframe);
      
      if (!historicalData || historicalData.length < 50) {
        logger.warn(`Insufficient data for ${symbol} ${timeframe}`);
        return null;
      }

      // Prepare market data
      const marketData = this.prepareMarketData(historicalData);
      
      // Perform comprehensive analysis
      const analysis = await this.indicatorEngine.analyzeMarket(marketData, symbol, timeframe);
      
      if (!analysis) {
        logger.warn(`Analysis failed for ${symbol} ${timeframe}`);
        return null;
      }

      // Analyze market state
      const marketState = this.marketAnalyzer.analyzeMarketState(marketData, symbol);
      
      // Combine analyses
      const combinedAnalysis = {
        ...analysis,
        marketState: marketState,
        timestamp: new Date().toISOString()
      };

      // Generate trading signals if conditions are met
      await this.processAnalysisResults(combinedAnalysis);
      
      // Emit analysis update
      this.emit('analysisUpdate', combinedAnalysis);
      
      // Send to WebSocket clients
      if (this.io) {
        this.io.to(`data:${symbol}`).emit('analysis', combinedAnalysis);
      }
      
      // Cache the analysis
      await this.cacheAnalysis(symbol, timeframe, combinedAnalysis);
      
      return combinedAnalysis;
      
    } catch (error) {
      logger.error(`Symbol analysis error for ${symbol} ${timeframe}:`, error);
      return null;
    }
  }

  // Process analysis results and generate signals
  async processAnalysisResults(analysis) {
    try {
      const { symbol, confluence, sentiment, marketState } = analysis;
      
      // Check if we should generate a signal
      if (!this.shouldGenerateSignal(analysis)) {
        return;
      }

      // Generate trade setup
      const tradeSetup = this.riskManager.generateTradeSetup(analysis, 10000); // $10,000 default account
      
      if (!tradeSetup || tradeSetup.action !== 'trade') {
        logger.info(`No trade setup generated for ${symbol}: ${tradeSetup?.reason || 'Unknown reason'}`);
        return;
      }

      // Enhance with AI predictions if available
      if (this.aiService) {
        try {
          const aiPrediction = await this.aiService.predictPrice(analysis);
          if (aiPrediction) {
            tradeSetup.aiPrediction = aiPrediction;
            tradeSetup.confidence = Math.min(tradeSetup.confidence + (aiPrediction.confidence * 0.2), 1);
          }
        } catch (error) {
          logger.warn('AI prediction error:', error);
        }
      }

      // Validate signal quality
      if (!this.validateSignalQuality(tradeSetup, analysis)) {
        logger.info(`Signal quality validation failed for ${symbol}`);
        return;
      }

      // Check for duplicate signals
      if (this.isDuplicateSignal(tradeSetup)) {
        logger.info(`Duplicate signal detected for ${symbol}, skipping`);
        return;
      }

      // Store active signal
      this.storeActiveSignal(tradeSetup);

      // Send notifications
      await this.sendSignalNotifications(tradeSetup);

      // Update performance metrics
      this.updatePerformanceMetrics(tradeSetup);

      // Emit signal event
      this.emit('tradingSignal', tradeSetup);

      logger.trade('Trading signal generated', {
        symbol: tradeSetup.metadata.symbol,
        direction: tradeSetup.direction,
        confidence: tradeSetup.confidence,
        riskReward: tradeSetup.riskReward
      });

    } catch (error) {
      logger.error('Analysis processing error:', error);
    }
  }

  // Handle real-time price updates
  handleRealTimePrice(symbol, priceData) {
    try {
      // Emit real-time price update
      this.emit('priceUpdate', { symbol, data: priceData });
      
      // Send to WebSocket clients
      if (this.io) {
        this.io.to(`data:${symbol}`).emit('price', { symbol, data: priceData });
      }
      
      // Check for price alerts
      this.checkPriceAlerts(symbol, priceData);
      
      // Update active signals
      this.updateActiveSignals(symbol, priceData);
      
    } catch (error) {
      logger.error('Real-time price handling error:', error);
    }
  }

  // Start real-time data processing
  startRealTimeProcessing() {
    // Set up data service event listeners
    this.dataService.on('priceUpdate', ({ symbol, data }) => {
      this.handleRealTimePrice(symbol, data);
    });
    
    this.dataService.on('connectionError', ({ provider, error }) => {
      logger.error(`Data connection error for ${provider}:`, error);
      this.emit('dataConnectionError', { provider, error });
    });
    
    logger.info('Real-time data processing started');
  }

  // Setup event listeners
  setupEventListeners() {
    // Handle analysis engine events
    this.indicatorEngine.on('error', (error) => {
      logger.error('Indicator engine error:', error);
    });
    
    // Handle risk manager events
    this.on('tradingSignal', (signal) => {
      logger.signal('New trading signal', {
        symbol: signal.metadata.symbol,
        direction: signal.direction,
        confidence: signal.confidence
      });
    });
    
    // Handle errors
    this.on('error', (error) => {
      logger.error('Trading engine error:', error);
    });
  }

  // Prepare market data for analysis
  prepareMarketData(historicalData) {
    const opens = historicalData.map(d => d.open);
    const highs = historicalData.map(d => d.high);
    const lows = historicalData.map(d => d.low);
    const closes = historicalData.map(d => d.close);
    const volumes = historicalData.map(d => d.volume);
    const timestamps = historicalData.map(d => d.timestamp);
    
    return {
      opens,
      highs,
      lows,
      closes,
      volumes,
      timestamps
    };
  }

  // Check if we should generate a signal
  shouldGenerateSignal(analysis) {
    const { confluence, sentiment, marketState } = analysis;
    
    // Minimum confluence score
    if (!confluence || confluence.score < 0.4) {
      return false;
    }
    
    // Market conditions check
    if (marketState && marketState.marketScore.overall < 0.3) {
      return false;
    }
    
    // Time-based restrictions
    if (marketState && marketState.timeAnalysis.activityLevel === 'closed') {
      return false;
    }
    
    return true;
  }

  // Validate signal quality
  validateSignalQuality(tradeSetup, analysis) {
    // Minimum confidence threshold
    if (tradeSetup.confidence < 0.5) {
      return false;
    }
    
    // Risk-reward ratio check
    if (tradeSetup.riskReward < config.risk.minRiskRewardRatio) {
      return false;
    }
    
    // Market volatility check
    if (analysis.marketState && 
        analysis.marketState.volatility.current?.level === 'high' && 
        tradeSetup.confidence < 0.7) {
      return false;
    }
    
    return true;
  }

  // Check for duplicate signals
  isDuplicateSignal(tradeSetup) {
    const symbol = tradeSetup.metadata.symbol;
    const activeSignal = this.activeSignals.get(symbol);
    
    if (!activeSignal) {
      return false;
    }
    
    // Check if signal is in same direction and recent
    const timeDiff = Date.now() - new Date(activeSignal.timestamp).getTime();
    const isRecent = timeDiff < 30 * 60 * 1000; // 30 minutes
    const sameDirection = activeSignal.direction === tradeSetup.direction;
    
    return isRecent && sameDirection;
  }

  // Store active signal
  storeActiveSignal(tradeSetup) {
    const symbol = tradeSetup.metadata.symbol;
    this.activeSignals.set(symbol, {
      ...tradeSetup,
      timestamp: Date.now()
    });
    
    // Clean up old signals (keep only last 24 hours)
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000;
    for (const [sym, signal] of this.activeSignals) {
      if (signal.timestamp < cutoffTime) {
        this.activeSignals.delete(sym);
      }
    }
  }

  // Send signal notifications
  async sendSignalNotifications(tradeSetup) {
    try {
      await this.notificationService.sendTradeSignal(tradeSetup);
    } catch (error) {
      logger.error('Signal notification error:', error);
    }
  }

  // Update performance metrics
  updatePerformanceMetrics(tradeSetup) {
    this.performanceMetrics.totalSignals++;
    
    // Update average confidence
    const totalConfidence = this.performanceMetrics.averageConfidence * (this.performanceMetrics.totalSignals - 1) + tradeSetup.confidence;
    this.performanceMetrics.averageConfidence = totalConfidence / this.performanceMetrics.totalSignals;
  }

  // Check price alerts
  checkPriceAlerts(symbol, priceData) {
    // Implementation for price alert checking
    // This would check user-defined price levels and send alerts
  }

  // Update active signals with current price
  updateActiveSignals(symbol, priceData) {
    const activeSignal = this.activeSignals.get(symbol);
    if (!activeSignal) return;
    
    // Update signal with current price information
    activeSignal.currentPrice = priceData.price;
    activeSignal.lastUpdate = Date.now();
    
    // Check if signal targets are hit
    this.checkSignalTargets(activeSignal, priceData);
  }

  // Check if signal targets are hit
  checkSignalTargets(signal, priceData) {
    const currentPrice = priceData.price;
    
    // Check stop loss
    if ((signal.direction === 'buy' && currentPrice <= signal.stopLoss.price) ||
        (signal.direction === 'sell' && currentPrice >= signal.stopLoss.price)) {
      this.handleSignalStopLoss(signal, currentPrice);
    }
    
    // Check take profit levels
    signal.takeProfits.forEach((tp, index) => {
      if (!tp.hit &&
          ((signal.direction === 'buy' && currentPrice >= tp.price) ||
           (signal.direction === 'sell' && currentPrice <= tp.price))) {
        this.handleSignalTakeProfit(signal, tp, index, currentPrice);
      }
    });
  }

  // Handle signal stop loss hit
  handleSignalStopLoss(signal, currentPrice) {
    logger.trade('Stop loss hit', {
      symbol: signal.metadata.symbol,
      direction: signal.direction,
      entryPrice: signal.entry.price,
      stopLossPrice: signal.stopLoss.price,
      currentPrice: currentPrice
    });
    
    this.performanceMetrics.failedSignals++;
    this.emit('signalStopLoss', { signal, currentPrice });
  }

  // Handle signal take profit hit
  handleSignalTakeProfit(signal, tp, index, currentPrice) {
    tp.hit = true;
    tp.hitTime = Date.now();
    
    logger.trade('Take profit hit', {
      symbol: signal.metadata.symbol,
      direction: signal.direction,
      level: tp.level,
      targetPrice: tp.price,
      currentPrice: currentPrice
    });
    
    if (index === 0) { // First TP hit
      this.performanceMetrics.successfulSignals++;
    }
    
    this.emit('signalTakeProfit', { signal, tp, index, currentPrice });
  }

  // Cache analysis results
  async cacheAnalysis(symbol, timeframe, analysis) {
    try {
      const cacheKey = `analysis_${symbol}_${timeframe}`;
      await cacheUtils.set(cacheKey, analysis, 300); // Cache for 5 minutes
    } catch (error) {
      logger.error('Analysis caching error:', error);
    }
  }

  // Get cached analysis
  async getCachedAnalysis(symbol, timeframe) {
    try {
      const cacheKey = `analysis_${symbol}_${timeframe}`;
      return await cacheUtils.get(cacheKey);
    } catch (error) {
      logger.error('Analysis cache retrieval error:', error);
      return null;
    }
  }

  // Get engine status
  getStatus() {
    return {
      isRunning: this.isRunning,
      subscribedPairs: Array.from(this.subscriptions.keys()),
      activeSignals: this.activeSignals.size,
      performanceMetrics: this.performanceMetrics,
      lastAnalysis: this.performanceMetrics.lastAnalysis
    };
  }

  // Get performance metrics
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      successRate: this.performanceMetrics.totalSignals > 0 
        ? (this.performanceMetrics.successfulSignals / this.performanceMetrics.totalSignals) * 100 
        : 0
    };
  }

  // Get active signals
  getActiveSignals() {
    return Array.from(this.activeSignals.values());
  }
}

export default TradingEngine;
