import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

const config = {
  // Server Configuration
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  },

  // Database Configuration
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/ayopfx_trading_bot',
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      }
    },
    redis: {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      options: {
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      }
    }
  },

  // TradingView Configuration
  tradingView: {
    apiKey: process.env.TRADINGVIEW_API_KEY,
    secret: process.env.TRADINGVIEW_SECRET,
    webhookUrl: process.env.TRADINGVIEW_WEBHOOK_URL,
    baseUrl: 'https://api.tradingview.com',
    chartingLibraryPath: './node_modules/tradingview-charting-library'
  },

  // Telegram Configuration
  telegram: {
    botToken: process.env.TELEGRAM_BOT_TOKEN,
    chatId: process.env.TELEGRAM_CHAT_ID,
    enabled: process.env.ENABLE_TELEGRAM_NOTIFICATIONS === 'true'
  },

  // Email Configuration
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    from: process.env.EMAIL_FROM || 'AyoPFX Trading Bot',
    enabled: process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true'
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'default_secret_change_in_production',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  },

  // API Keys
  apiKeys: {
    alphaVantage: process.env.ALPHA_VANTAGE_API_KEY,
    finnhub: process.env.FINNHUB_API_KEY,
    polygon: process.env.POLYGON_API_KEY,
    twelveData: process.env.TWELVE_DATA_API_KEY,
    forexFactory: process.env.FOREX_FACTORY_API_KEY
  },

  // Risk Management
  risk: {
    defaultRiskPercentage: parseFloat(process.env.DEFAULT_RISK_PERCENTAGE) || 2,
    maxRiskPercentage: parseFloat(process.env.MAX_RISK_PERCENTAGE) || 5,
    minRiskRewardRatio: parseFloat(process.env.MIN_RISK_REWARD_RATIO) || 1.5
  },

  // AI Configuration
  ai: {
    modelPath: process.env.AI_MODEL_PATH || './models/trading_model.json',
    retrainIntervalHours: parseInt(process.env.RETRAIN_INTERVAL_HOURS) || 24,
    predictionConfidenceThreshold: parseFloat(process.env.PREDICTION_CONFIDENCE_THRESHOLD) || 0.75
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/trading_bot.log'
  },

  // Rate Limiting
  rateLimiting: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    enabled: process.env.RATE_LIMITING_ENABLED === 'true'
  },

  // Trading Configuration
  trading: {
    supportedPairs: (process.env.SUPPORTED_PAIRS || 'EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,USDCHF,NZDUSD,XAUUSD,BTCUSD,ETHUSD').split(','),
    defaultTimeframe: process.env.DEFAULT_TIMEFRAME || '1h',
    analysisTimeframes: (process.env.ANALYSIS_TIMEFRAMES || '1m,5m,15m,1h,4h,1d').split(','),
    updateInterval: 60000, // 1 minute in milliseconds
    maxHistoryBars: 1000
  },

  // Notification Settings
  notifications: {
    cooldownMinutes: parseInt(process.env.NOTIFICATION_COOLDOWN_MINUTES) || 5,
    maxNotificationsPerHour: 20
  },

  // Security
  security: {
    helmetEnabled: process.env.HELMET_ENABLED === 'true',
    corsEnabled: true,
    rateLimitingEnabled: process.env.RATE_LIMITING_ENABLED === 'true'
  },

  // Trading Sessions
  tradingSessions: {
    london: { start: '08:00', end: '17:00', timezone: 'Europe/London' },
    newYork: { start: '13:00', end: '22:00', timezone: 'America/New_York' },
    tokyo: { start: '00:00', end: '09:00', timezone: 'Asia/Tokyo' },
    sydney: { start: '22:00', end: '07:00', timezone: 'Australia/Sydney' }
  }
};

export default config;
