console.log('🚀 بدء تشغيل AyoPFX Trading Bot...');

try {
  const express = require('express');
  console.log('✅ Express تم تحميله بنجاح');
  
  const app = express();
  const PORT = 3000;
  
  app.get('/', (req, res) => {
    res.send(`
      <h1 style="text-align: center; color: #2c3e50; font-family: Arial;">
        🤖 AyoPFX Trading Bot
      </h1>
      <p style="text-align: center; font-size: 18px;">
        ✅ البوت يعمل بنجاح!
      </p>
      <div style="text-align: center; margin-top: 30px;">
        <a href="/health" style="margin: 10px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px;">فحص الصحة</a>
        <a href="/api/status" style="margin: 10px; padding: 10px 20px; background: #27ae60; color: white; text-decoration: none; border-radius: 5px;">حالة النظام</a>
      </div>
    `);
  });
  
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      message: 'AyoPFX Trading Bot يعمل بنجاح',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });
  
  app.get('/api/status', (req, res) => {
    res.json({
      status: 'success',
      data: {
        botName: 'AyoPFX Trading Bot',
        version: '1.0.0',
        isRunning: true,
        timestamp: new Date().toISOString()
      }
    });
  });
  
  app.listen(PORT, () => {
    console.log(`✅ الخادم يعمل على المنفذ ${PORT}`);
    console.log(`🌐 افتح المتصفح على: http://localhost:${PORT}`);
    console.log('🎉 البوت جاهز للاستخدام!');
  });
  
} catch (error) {
  console.error('❌ خطأ:', error.message);
  process.exit(1);
}
