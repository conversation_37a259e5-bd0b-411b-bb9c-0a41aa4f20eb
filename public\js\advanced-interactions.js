// AyoPFX Trading Bot - Advanced Interactions

class AdvancedInteractions {
    constructor() {
        this.notifications = [];
        this.charts = {};
        this.websocket = null;
        this.isConnected = false;
        this.animationQueue = [];
    }

    // تهيئة التفاعلات المتقدمة
    initialize() {
        this.setupWebSocket();
        this.setupNotifications();
        this.setupAnimations();
        this.setupKeyboardShortcuts();
        this.setupThemeToggle();
        this.setupAdvancedCharts();
        this.setupRealTimeUpdates();
        
        console.log('✅ Advanced Interactions initialized');
    }

    // إعداد WebSocket للتحديثات المباشرة
    setupWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                this.isConnected = true;
                this.showNotification('متصل بالخادم', 'success');
                console.log('WebSocket connected');
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                this.isConnected = false;
                this.showNotification('انقطع الاتصال بالخادم', 'warning');
                console.log('WebSocket disconnected');
                
                // إعادة المحاولة بعد 5 ثوان
                setTimeout(() => this.setupWebSocket(), 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.showNotification('خطأ في الاتصال', 'error');
            };
        } catch (error) {
            console.warn('WebSocket not available:', error);
        }
    }

    // معالجة رسائل WebSocket
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'signal_update':
                this.updateSignalInRealTime(data.signal);
                break;
            case 'price_update':
                this.updatePriceDisplay(data.prices);
                break;
            case 'notification':
                this.showNotification(data.message, data.level);
                break;
            case 'portfolio_update':
                this.updatePortfolioInRealTime(data.portfolio);
                break;
            default:
                console.log('Unknown WebSocket message:', data);
        }
    }

    // إعداد نظام التنبيهات المتقدم
    setupNotifications() {
        // إنشاء حاوي التنبيهات
        if (!document.getElementById('notifications-container')) {
            const container = document.createElement('div');
            container.id = 'notifications-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }

        // طلب إذن التنبيهات من المتصفح
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    // عرض تنبيه متقدم
    showNotification(message, type = 'info', duration = 5000) {
        const id = 'notif_' + Date.now();
        const notification = document.createElement('div');
        notification.id = id;
        notification.className = `notification glass-card ${type}`;
        
        const icon = this.getNotificationIcon(type);
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${icon} me-3"></i>
                <div class="flex-grow-1">
                    <div class="fw-bold">${message}</div>
                    <small class="text-muted">${new Date().toLocaleTimeString('ar-EG')}</small>
                </div>
                <button class="btn btn-sm btn-outline-light ms-2" onclick="advancedInteractions.closeNotification('${id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        const container = document.getElementById('notifications-container');
        container.appendChild(notification);

        // تأثير الظهور
        setTimeout(() => notification.classList.add('show'), 100);

        // إغلاق تلقائي
        if (duration > 0) {
            setTimeout(() => this.closeNotification(id), duration);
        }

        // تنبيه المتصفح
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('AyoPFX Trading Bot', {
                body: message,
                icon: '/favicon.ico'
            });
        }

        this.notifications.push({ id, message, type, timestamp: Date.now() });
    }

    // الحصول على أيقونة التنبيه
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // إغلاق تنبيه
    closeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }
    }

    // إعداد الأنيميشن المتقدم
    setupAnimations() {
        // مراقب التقاطع للأنيميشن عند الظهور
        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        // مراقبة العناصر القابلة للأنيميشن
        document.querySelectorAll('.glass-card, .signal-card').forEach(el => {
            this.intersectionObserver.observe(el);
        });
    }

    // أنيميشن العدادات
    animateCounters() {
        document.querySelectorAll('.counter').forEach(counter => {
            const target = parseFloat(counter.dataset.target);
            const prefix = counter.dataset.prefix || '';
            const suffix = counter.dataset.suffix || '';
            const duration = 2000;
            const step = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                current += step;
                if (current < target) {
                    counter.textContent = prefix + Math.floor(current).toLocaleString() + suffix;
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = prefix + target.toLocaleString() + suffix;
                }
            };

            updateCounter();
        });
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + D: لوحة التحكم
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                this.switchTab('dashboard');
            }
            
            // Ctrl/Cmd + S: الإشارات
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.switchTab('signals');
            }
            
            // Ctrl/Cmd + A: التحليل
            if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
                e.preventDefault();
                this.switchTab('analysis');
            }
            
            // Escape: إغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    // تبديل التبويبات
    switchTab(tabName) {
        const tabButton = document.querySelector(`[data-bs-target="#${tabName}"]`);
        if (tabButton) {
            tabButton.click();
        }
    }

    // إغلاق جميع النوافذ المنبثقة
    closeAllModals() {
        document.querySelectorAll('.modal.show').forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }

    // إعداد تبديل الثيم
    setupThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.className = 'btn btn-outline-light btn-sm position-fixed';
        themeToggle.style.cssText = 'top: 20px; left: 20px; z-index: 1000;';
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        themeToggle.title = 'تبديل الثيم';
        
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            themeToggle.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });

        document.body.appendChild(themeToggle);

        // تطبيق الثيم المحفوظ
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        }
    }

    // إعداد الرسوم البيانية المتقدمة
    setupAdvancedCharts() {
        // إعداد Chart.js مع إعدادات متقدمة
        Chart.defaults.color = '#ffffff';
        Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
        Chart.defaults.backgroundColor = 'rgba(102, 126, 234, 0.1)';
    }

    // إنشاء رسم بياني متقدم
    createAdvancedChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff',
                        usePointStyle: true
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: 'rgba(102, 126, 234, 0.5)',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ffffff'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#ffffff'
                    }
                }
            }
        };

        const mergedOptions = { ...defaultOptions, ...options };
        
        const chart = new Chart(ctx, {
            type: data.type || 'line',
            data: data,
            options: mergedOptions
        });

        this.charts[canvasId] = chart;
        return chart;
    }

    // تحديث الرسم البياني
    updateChart(canvasId, newData) {
        const chart = this.charts[canvasId];
        if (!chart) return;

        chart.data = newData;
        chart.update('active');
    }

    // إعداد التحديثات المباشرة
    setupRealTimeUpdates() {
        // تحديث الوقت كل ثانية
        setInterval(() => {
            document.querySelectorAll('.real-time-clock').forEach(clock => {
                clock.textContent = new Date().toLocaleString('ar-EG');
            });
        }, 1000);

        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            if (this.isConnected) {
                this.requestDataUpdate();
            }
        }, 30000);
    }

    // طلب تحديث البيانات
    requestDataUpdate() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'request_update',
                timestamp: Date.now()
            }));
        }
    }

    // تحديث الإشارة في الوقت الفعلي
    updateSignalInRealTime(signal) {
        const signalElement = document.querySelector(`[data-signal-id="${signal.id}"]`);
        if (signalElement) {
            // تأثير الوميض للتحديث
            signalElement.classList.add('signal-updated');
            setTimeout(() => signalElement.classList.remove('signal-updated'), 1000);
            
            // تحديث البيانات
            this.updateSignalData(signalElement, signal);
        }
    }

    // تحديث عرض الأسعار
    updatePriceDisplay(prices) {
        Object.entries(prices).forEach(([symbol, price]) => {
            const priceElements = document.querySelectorAll(`[data-symbol="${symbol}"] .price`);
            priceElements.forEach(element => {
                const oldPrice = parseFloat(element.textContent);
                const newPrice = parseFloat(price);
                
                element.textContent = newPrice.toFixed(5);
                
                // تأثير لوني للتغيير
                if (newPrice > oldPrice) {
                    element.classList.add('price-up');
                } else if (newPrice < oldPrice) {
                    element.classList.add('price-down');
                }
                
                setTimeout(() => {
                    element.classList.remove('price-up', 'price-down');
                }, 1000);
            });
        });
    }

    // تحديث المحفظة في الوقت الفعلي
    updatePortfolioInRealTime(portfolio) {
        // تحديث الأرقام مع أنيميشن
        this.animateValueChange('.balance-value', portfolio.balance);
        this.animateValueChange('.equity-value', portfolio.equity);
        this.animateValueChange('.profit-value', portfolio.totalProfit);
    }

    // أنيميشن تغيير القيمة
    animateValueChange(selector, newValue) {
        const element = document.querySelector(selector);
        if (!element) return;

        const oldValue = parseFloat(element.textContent.replace(/[^0-9.-]/g, ''));
        const difference = newValue - oldValue;
        
        if (Math.abs(difference) < 0.01) return; // تجاهل التغييرات الصغيرة

        element.style.transform = 'scale(1.1)';
        element.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            element.textContent = '$' + newValue.toLocaleString();
            element.style.transform = 'scale(1)';
        }, 150);
    }

    // تصدير البيانات
    exportData(type, format = 'json') {
        const data = this.gatherExportData(type);
        const filename = `ayopfx_${type}_${new Date().toISOString().split('T')[0]}.${format}`;
        
        if (format === 'json') {
            this.downloadJSON(data, filename);
        } else if (format === 'csv') {
            this.downloadCSV(data, filename);
        }
    }

    // تحميل JSON
    downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        this.downloadBlob(blob, filename);
    }

    // تحميل CSV
    downloadCSV(data, filename) {
        const csv = this.convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv' });
        this.downloadBlob(blob, filename);
    }

    // تحميل Blob
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // تحويل إلى CSV
    convertToCSV(data) {
        if (!Array.isArray(data) || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header]).join(','))
        ].join('\n');
        
        return csvContent;
    }

    // جمع بيانات التصدير
    gatherExportData(type) {
        switch (type) {
            case 'signals':
                return this.getSignalsData();
            case 'portfolio':
                return this.getPortfolioData();
            case 'analysis':
                return this.getAnalysisData();
            default:
                return {};
        }
    }

    // الحصول على بيانات الإشارات
    getSignalsData() {
        // جمع بيانات الإشارات من DOM أو API
        return [];
    }

    // الحصول على بيانات المحفظة
    getPortfolioData() {
        // جمع بيانات المحفظة
        return {};
    }

    // الحصول على بيانات التحليل
    getAnalysisData() {
        // جمع بيانات التحليل
        return {};
    }

    // تنظيف الموارد
    cleanup() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
    }
}

// إنشاء مثيل عام
const advancedInteractions = new AdvancedInteractions();

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    advancedInteractions.initialize();
});

// تنظيف عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    advancedInteractions.cleanup();
});
