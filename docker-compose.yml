version: '3.8'

services:
  # خدمة بوت التداول الرئيسية
  ayopfx-trading-bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ayopfx-trading-bot
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
      - ./backups:/app/backups
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # خدمة Redis لقاعدة البيانات
  redis:
    image: redis:7-alpine
    container_name: ayopfx-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5

  # خدمة مراقبة Redis (اختيارية)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ayopfx-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    depends_on:
      - redis
    networks:
      - ayopfx-network
    profiles:
      - monitoring

  # خدمة مراقبة النظام (اختيارية)
  prometheus:
    image: prom/prometheus:latest
    container_name: ayopfx-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ayopfx-network
    profiles:
      - monitoring

  # خدمة Grafana للمراقبة (اختيارية)
  grafana:
    image: grafana/grafana:latest
    container_name: ayopfx-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - ayopfx-network
    profiles:
      - monitoring

  # خدمة النسخ الاحتياطي (اختيارية)
  backup:
    image: alpine:latest
    container_name: ayopfx-backup
    restart: "no"
    volumes:
      - ./backups:/backups
      - redis-data:/redis-data:ro
      - ./logs:/logs:ro
    command: |
      sh -c "
        apk add --no-cache tar gzip &&
        cd /backups &&
        tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz -C / redis-data logs &&
        find /backups -name '*.tar.gz' -mtime +7 -delete
      "
    networks:
      - ayopfx-network
    profiles:
      - backup

# الشبكات
networks:
  ayopfx-network:
    driver: bridge
    name: ayopfx-network

# الأحجام المستمرة
volumes:
  redis-data:
    driver: local
    name: ayopfx-redis-data
  prometheus-data:
    driver: local
    name: ayopfx-prometheus-data
  grafana-data:
    driver: local
    name: ayopfx-grafana-data
