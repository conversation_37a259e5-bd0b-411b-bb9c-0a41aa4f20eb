// AyoPFX Trading Bot Frontend Application
class TradingBotApp {
    constructor() {
        this.socket = null;
        this.charts = {};
        this.currentSymbol = 'EURUSD';
        this.updateInterval = null;
        this.countdownInterval = null;
        
        this.init();
    }

    // Initialize the application
    init() {
        console.log('🚀 Initializing AyoPFX Trading Bot...');
        
        // Set Arabic locale for moment.js
        moment.locale('ar');
        
        // Initialize Socket.IO connection
        this.initializeSocket();
        
        // Initialize event listeners
        this.initializeEventListeners();
        
        // Initialize charts
        this.initializeCharts();
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        // Load initial data
        this.loadInitialData();
        
        console.log('✅ AyoPFX Trading Bot initialized successfully');
    }

    // Initialize Socket.IO connection
    initializeSocket() {
        try {
            this.socket = io();
            
            this.socket.on('connect', () => {
                console.log('✅ Connected to server');
                this.updateConnectionStatus(true);
                this.subscribeToData();
            });
            
            this.socket.on('disconnect', () => {
                console.log('❌ Disconnected from server');
                this.updateConnectionStatus(false);
            });
            
            this.socket.on('priceUpdate', (data) => {
                this.handlePriceUpdate(data);
            });
            
            this.socket.on('analysis', (data) => {
                this.handleAnalysisUpdate(data);
            });
            
            this.socket.on('tradingSignal', (data) => {
                this.handleNewSignal(data);
            });
            
            this.socket.on('error', (error) => {
                console.error('Socket error:', error);
                this.showNotification('خطأ في الاتصال', 'error');
            });
            
        } catch (error) {
            console.error('Socket initialization error:', error);
            this.updateConnectionStatus(false);
        }
    }

    // Subscribe to real-time data
    subscribeToData() {
        const supportedPairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'XAUUSD'];
        
        this.socket.emit('subscribe', {
            pairs: supportedPairs,
            timeframes: ['1m', '5m', '15m', '1h', '4h', '1d']
        });
    }

    // Initialize event listeners
    initializeEventListeners() {
        // Symbol selection change
        document.getElementById('symbolSelect')?.addEventListener('change', (e) => {
            this.currentSymbol = e.target.value;
            this.updateChart();
            this.loadAnalysis();
        });
        
        // Risk settings form
        document.getElementById('riskSettings')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRiskSettings();
        });
        
        // Notification settings form
        document.getElementById('notificationSettings')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveNotificationSettings();
        });
        
        // Tab change events
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const targetId = e.target.getAttribute('data-bs-target');
                this.handleTabChange(targetId);
            });
        });
    }

    // Initialize charts
    initializeCharts() {
        try {
            const ctx = document.getElementById('priceChart');
            if (!ctx) return;

            this.charts.priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'السعر',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return `السعر: ${context.parsed.y.toFixed(5)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm',
                                    hour: 'HH:mm'
                                }
                            },
                            title: {
                                display: true,
                                text: 'الوقت'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'السعر'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(5);
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
            
            console.log('✅ Charts initialized');
        } catch (error) {
            console.error('Chart initialization error:', error);
        }
    }

    // Start periodic updates
    startPeriodicUpdates() {
        // Update time every second
        this.updateInterval = setInterval(() => {
            this.updateCurrentTime();
        }, 1000);
        
        // Update dashboard data every 30 seconds
        setInterval(() => {
            this.updateDashboard();
        }, 30000);
        
        // Update market status every minute
        setInterval(() => {
            this.updateMarketStatus();
        }, 60000);
    }

    // Load initial data
    async loadInitialData() {
        try {
            await Promise.all([
                this.updateDashboard(),
                this.updateMarketStatus(),
                this.loadSignals(),
                this.loadAnalysis(),
                this.updateChart()
            ]);
            
            console.log('✅ Initial data loaded');
        } catch (error) {
            console.error('Initial data loading error:', error);
            this.showNotification('خطأ في تحميل البيانات الأولية', 'error');
        }
    }

    // Update connection status
    updateConnectionStatus(connected) {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        
        if (statusDot && statusText) {
            if (connected) {
                statusDot.style.background = '#27ae60';
                statusText.textContent = 'متصل';
            } else {
                statusDot.style.background = '#e74c3c';
                statusText.textContent = 'غير متصل';
            }
        }
    }

    // Update current time
    updateCurrentTime() {
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = moment().format('YYYY/MM/DD HH:mm:ss');
        }
    }

    // Update dashboard
    async updateDashboard() {
        try {
            const response = await fetch('/api/status/performance');
            const data = await response.json();
            
            if (data.status === 'success') {
                const metrics = data.data;
                
                document.getElementById('totalSignals').textContent = metrics.totalSignals || 0;
                document.getElementById('successRate').textContent = `${Math.round(metrics.successRate || 0)}%`;
                document.getElementById('activeSignals').textContent = metrics.activeSignals || 0;
                document.getElementById('avgConfidence').textContent = `${Math.round((metrics.averageConfidence || 0) * 100)}%`;
            }
        } catch (error) {
            console.error('Dashboard update error:', error);
        }
    }

    // Update market status
    async updateMarketStatus() {
        try {
            const response = await fetch(`/api/analysis/market-state/${this.currentSymbol}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                const marketState = data.data;
                this.displayMarketStatus(marketState);
            }
        } catch (error) {
            console.error('Market status update error:', error);
        }
    }

    // Display market status
    displayMarketStatus(marketState) {
        // Update current session
        const sessionElement = document.getElementById('currentSession');
        if (sessionElement && marketState.session) {
            sessionElement.textContent = this.translateSession(marketState.session.current);
            sessionElement.className = `session-indicator session-${marketState.session.current.toLowerCase()}`;
        }
        
        // Update market overview
        const overviewElement = document.getElementById('marketOverview');
        if (overviewElement && marketState.summary) {
            overviewElement.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line"></i> الاتجاه العام</h6>
                        <p class="mb-2">${this.translateTrend(marketState.trend?.overall?.direction || 'neutral')}</p>
                        
                        <h6><i class="fas fa-tint"></i> السيولة</h6>
                        <p class="mb-2">${this.translateLiquidity(marketState.liquidity?.level || 'normal')}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-bolt"></i> الزخم</h6>
                        <p class="mb-2">${this.translateMomentum(marketState.momentum?.overall?.direction || 'neutral')}</p>
                        
                        <h6><i class="fas fa-star"></i> نقاط السوق</h6>
                        <p class="mb-2">${Math.round((marketState.marketScore?.overall || 0) * 100)}%</p>
                    </div>
                </div>
                <div class="alert alert-info alert-custom mt-3">
                    <i class="fas fa-info-circle"></i>
                    ${marketState.summary?.headline || 'تحليل السوق غير متوفر'}
                </div>
            `;
        }
    }

    // Load signals
    async loadSignals() {
        try {
            const response = await fetch('/api/signals?limit=10');
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displaySignals(data.data.signals);
            }
        } catch (error) {
            console.error('Signals loading error:', error);
        }
    }

    // Display signals
    displaySignals(signals) {
        const recentSignalsElement = document.getElementById('recentSignals');
        const activeSignalsElement = document.getElementById('activeSignalsList');
        
        if (signals.length === 0) {
            const noSignalsHtml = `
                <div class="alert alert-info alert-custom">
                    <i class="fas fa-info-circle"></i>
                    لا توجد إشارات متاحة حالياً
                </div>
            `;
            
            if (recentSignalsElement) recentSignalsElement.innerHTML = noSignalsHtml;
            if (activeSignalsElement) activeSignalsElement.innerHTML = noSignalsHtml;
            return;
        }
        
        const signalsHtml = signals.map(signal => this.createSignalCard(signal)).join('');
        
        if (recentSignalsElement) {
            recentSignalsElement.innerHTML = signalsHtml;
        }
        
        if (activeSignalsElement) {
            activeSignalsElement.innerHTML = signalsHtml;
        }
    }

    // Create signal card HTML
    createSignalCard(signal) {
        const directionClass = signal.direction === 'buy' ? 'success' : 'danger';
        const directionText = signal.direction === 'buy' ? 'شراء' : 'بيع';
        const directionIcon = signal.direction === 'buy' ? 'fa-arrow-up' : 'fa-arrow-down';
        
        return `
            <div class="card signal-card ${signal.direction} mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h5 class="mb-1">
                                <i class="fas ${directionIcon} text-${directionClass}"></i>
                                ${signal.metadata?.symbol || 'N/A'}
                            </h5>
                            <small class="text-muted">${signal.metadata?.timeframe || 'N/A'}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-${directionClass} fs-6">${directionText}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>الدخول:</strong><br>
                            <span class="text-primary">${signal.entry?.price?.toFixed(5) || 'N/A'}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>وقف الخسارة:</strong><br>
                            <span class="text-danger">${signal.stopLoss?.price?.toFixed(5) || 'N/A'}</span>
                        </div>
                        <div class="col-md-2">
                            <strong>الثقة:</strong><br>
                            <span class="text-info">${Math.round((signal.confidence || 0) * 100)}%</span>
                        </div>
                        <div class="col-md-1">
                            <small class="text-muted">
                                ${moment(signal.metadata?.timestamp).fromNow()}
                            </small>
                        </div>
                    </div>
                    ${signal.takeProfits && signal.takeProfits.length > 0 ? `
                        <div class="row mt-2">
                            <div class="col-12">
                                <strong>الأهداف:</strong>
                                ${signal.takeProfits.map((tp, index) => 
                                    `<span class="badge bg-success me-2">TP${index + 1}: ${tp.price?.toFixed(5)}</span>`
                                ).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Load analysis
    async loadAnalysis() {
        try {
            const response = await fetch(`/api/analysis/${this.currentSymbol}/1h`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displayAnalysis(data.data);
            }
        } catch (error) {
            console.error('Analysis loading error:', error);
        }
    }

    // Display analysis
    displayAnalysis(analysis) {
        const aiAnalysisElement = document.getElementById('aiAnalysis');
        
        if (!aiAnalysisElement || !analysis) return;
        
        const sentiment = analysis.sentiment || {};
        const confluence = analysis.confluence || {};
        const technical = analysis.technical || {};
        
        aiAnalysisElement.innerHTML = `
            <div class="mb-3">
                <h6><i class="fas fa-brain"></i> التحليل العام</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-${sentiment.overall === 'bullish' ? 'success' : sentiment.overall === 'bearish' ? 'danger' : 'warning'}" 
                         style="width: ${Math.round((sentiment.strength || 0) * 100)}%">
                        ${Math.round((sentiment.strength || 0) * 100)}%
                    </div>
                </div>
                <small class="text-muted">${this.translateSentiment(sentiment.overall || 'neutral')}</small>
            </div>
            
            <div class="mb-3">
                <h6><i class="fas fa-crosshairs"></i> مستوى التقارب</h6>
                <div class="progress mb-2">
                    <div class="progress-bar bg-info" style="width: ${Math.round((confluence.score || 0) * 100)}%">
                        ${Math.round((confluence.score || 0) * 100)}%
                    </div>
                </div>
                <small class="text-muted">${confluence.level || 'منخفض'}</small>
            </div>
            
            ${technical.rsi ? `
                <div class="mb-3">
                    <h6><i class="fas fa-chart-bar"></i> مؤشر RSI</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-${technical.rsi.current > 70 ? 'danger' : technical.rsi.current < 30 ? 'success' : 'primary'}" 
                             style="width: ${technical.rsi.current}%">
                            ${Math.round(technical.rsi.current)}
                        </div>
                    </div>
                    <small class="text-muted">${this.translateRSI(technical.rsi.current)}</small>
                </div>
            ` : ''}
            
            <div class="alert alert-light alert-custom">
                <small>
                    <i class="fas fa-clock"></i>
                    آخر تحديث: ${moment(analysis.timestamp).fromNow()}
                </small>
            </div>
        `;
    }

    // Update chart
    async updateChart() {
        try {
            const response = await fetch(`/api/market/history/${this.currentSymbol}/1h?limit=100`);
            const data = await response.json();
            
            if (data.status === 'success' && this.charts.priceChart) {
                const chartData = data.data.data;
                
                const labels = chartData.map(item => new Date(item.timestamp));
                const prices = chartData.map(item => item.close);
                
                this.charts.priceChart.data.labels = labels;
                this.charts.priceChart.data.datasets[0].data = prices;
                this.charts.priceChart.data.datasets[0].label = `${this.currentSymbol} - السعر`;
                
                this.charts.priceChart.update();
            }
        } catch (error) {
            console.error('Chart update error:', error);
        }
    }

    // Handle price update
    handlePriceUpdate(data) {
        // Update live prices display
        this.updateLivePrices(data);
        
        // Update chart if it's the current symbol
        if (data.symbol === this.currentSymbol && this.charts.priceChart) {
            const chart = this.charts.priceChart;
            const now = new Date();
            
            // Add new data point
            chart.data.labels.push(now);
            chart.data.datasets[0].data.push(data.data.price);
            
            // Keep only last 100 points
            if (chart.data.labels.length > 100) {
                chart.data.labels.shift();
                chart.data.datasets[0].data.shift();
            }
            
            chart.update('none');
        }
    }

    // Update live prices
    updateLivePrices(data) {
        const livePricesElement = document.getElementById('livePrices');
        if (!livePricesElement) return;
        
        // This would be implemented to show a list of live prices
        // For now, we'll just update if it's empty
        if (livePricesElement.innerHTML.includes('جاري تحميل')) {
            livePricesElement.innerHTML = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6>${data.symbol}</h6>
                                <div class="price-display">
                                    ${data.data.price?.toFixed(5) || 'N/A'}
                                </div>
                                <small class="price-change ${(data.data.change || 0) >= 0 ? 'positive' : 'negative'}">
                                    ${(data.data.change || 0) >= 0 ? '+' : ''}${(data.data.change || 0).toFixed(5)}
                                    (${(data.data.changePercent || 0).toFixed(2)}%)
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Handle analysis update
    handleAnalysisUpdate(data) {
        if (data.symbol === this.currentSymbol) {
            this.displayAnalysis(data);
        }
    }

    // Handle new signal
    handleNewSignal(signal) {
        // Show notification
        this.showNotification(
            `إشارة جديدة: ${signal.direction === 'buy' ? 'شراء' : 'بيع'} ${signal.metadata?.symbol}`,
            'success'
        );
        
        // Reload signals
        this.loadSignals();
        
        // Update dashboard
        this.updateDashboard();
    }

    // Handle tab change
    handleTabChange(targetId) {
        switch (targetId) {
            case '#analysis':
                this.updateChart();
                this.loadAnalysis();
                break;
            case '#signals':
                this.loadSignals();
                break;
            case '#market':
                this.updateMarketStatus();
                break;
            case '#dashboard':
                this.updateDashboard();
                break;
        }
    }

    // Save risk settings
    async saveRiskSettings() {
        try {
            const riskPercentage = document.getElementById('riskPercentage').value;
            const minRiskReward = document.getElementById('minRiskReward').value;
            
            // Here you would send the settings to the server
            // For now, we'll just show a success message
            
            this.showNotification('تم حفظ إعدادات المخاطر بنجاح', 'success');
        } catch (error) {
            console.error('Risk settings save error:', error);
            this.showNotification('خطأ في حفظ إعدادات المخاطر', 'error');
        }
    }

    // Save notification settings
    async saveNotificationSettings() {
        try {
            const telegramNotifications = document.getElementById('telegramNotifications').checked;
            const emailNotifications = document.getElementById('emailNotifications').checked;
            const cooldownMinutes = document.getElementById('cooldownMinutes').value;
            
            // Here you would send the settings to the server
            // For now, we'll just show a success message
            
            this.showNotification('تم حفظ إعدادات التنبيهات بنجاح', 'success');
        } catch (error) {
            console.error('Notification settings save error:', error);
            this.showNotification('خطأ في حفظ إعدادات التنبيهات', 'error');
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    // Translation helpers
    translateSession(session) {
        const translations = {
            'london': 'لندن',
            'newyork': 'نيويورك',
            'tokyo': 'طوكيو',
            'sydney': 'سيدني',
            'off_hours': 'خارج ساعات التداول'
        };
        return translations[session] || session;
    }

    translateTrend(trend) {
        const translations = {
            'bullish': 'صاعد',
            'bearish': 'هابط',
            'sideways': 'جانبي',
            'neutral': 'محايد'
        };
        return translations[trend] || trend;
    }

    translateLiquidity(liquidity) {
        const translations = {
            'high': 'عالية',
            'normal': 'عادية',
            'low': 'منخفضة',
            'above_normal': 'فوق العادية',
            'below_normal': 'تحت العادية'
        };
        return translations[liquidity] || liquidity;
    }

    translateMomentum(momentum) {
        const translations = {
            'bullish': 'إيجابي',
            'bearish': 'سلبي',
            'neutral': 'محايد'
        };
        return translations[momentum] || momentum;
    }

    translateSentiment(sentiment) {
        const translations = {
            'bullish': 'متفائل',
            'bearish': 'متشائم',
            'neutral': 'محايد'
        };
        return translations[sentiment] || sentiment;
    }

    translateRSI(rsi) {
        if (rsi > 70) return 'تشبع شرائي';
        if (rsi < 30) return 'تشبع بيعي';
        if (rsi > 50) return 'إيجابي';
        return 'سلبي';
    }

    // Cleanup
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }
        
        if (this.socket) {
            this.socket.disconnect();
        }
        
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.tradingBotApp = new TradingBotApp();
});

// Handle page unload
window.addEventListener('beforeunload', () => {
    if (window.tradingBotApp) {
        window.tradingBotApp.destroy();
    }
});
