// إعدادات التداول المتقدمة - AyoPFX Trading Bot
import config from './config.js';

export const tradingConfig = {
  // أزواج العملات المدعومة مع إعدادات مخصصة
  supportedPairs: {
    'EURUSD': {
      name: 'EUR/USD',
      nameAr: 'اليورو/الدولار الأمريكي',
      category: 'major',
      spread: 1.2,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['london', 'newyork'],
      volatility: 'medium',
      riskLevel: 'low',
      preferredTimeframes: ['1h', '4h', '1d'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 70, oversold: 30 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        bollinger: { enabled: true, period: 20, deviation: 2 },
        ema: { enabled: true, periods: [20, 50, 200] },
        stochastic: { enabled: true, k: 14, d: 3 }
      }
    },
    'GBPUSD': {
      name: 'GBP/USD',
      nameAr: 'الجنيه الإسترليني/الدولار الأمريكي',
      category: 'major',
      spread: 1.5,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['london', 'newyork'],
      volatility: 'high',
      riskLevel: 'medium',
      preferredTimeframes: ['1h', '4h'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 75, oversold: 25 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        bollinger: { enabled: true, period: 20, deviation: 2.2 },
        ema: { enabled: true, periods: [20, 50, 200] },
        atr: { enabled: true, period: 14 }
      }
    },
    'USDJPY': {
      name: 'USD/JPY',
      nameAr: 'الدولار الأمريكي/الين الياباني',
      category: 'major',
      spread: 1.0,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['tokyo', 'newyork'],
      volatility: 'medium',
      riskLevel: 'low',
      preferredTimeframes: ['1h', '4h', '1d'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 70, oversold: 30 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        ichimoku: { enabled: true, tenkan: 9, kijun: 26, senkou: 52 },
        ema: { enabled: true, periods: [20, 50, 200] }
      }
    },
    'AUDUSD': {
      name: 'AUD/USD',
      nameAr: 'الدولار الأسترالي/الدولار الأمريكي',
      category: 'major',
      spread: 1.8,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['sydney', 'tokyo'],
      volatility: 'medium',
      riskLevel: 'medium',
      preferredTimeframes: ['4h', '1d'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 70, oversold: 30 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        bollinger: { enabled: true, period: 20, deviation: 2 },
        ema: { enabled: true, periods: [20, 50] }
      }
    },
    'USDCAD': {
      name: 'USD/CAD',
      nameAr: 'الدولار الأمريكي/الدولار الكندي',
      category: 'major',
      spread: 2.0,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['newyork'],
      volatility: 'medium',
      riskLevel: 'medium',
      preferredTimeframes: ['1h', '4h'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 70, oversold: 30 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        ema: { enabled: true, periods: [20, 50, 200] }
      }
    },
    'USDCHF': {
      name: 'USD/CHF',
      nameAr: 'الدولار الأمريكي/الفرنك السويسري',
      category: 'major',
      spread: 2.2,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 100,
      pipValue: 10,
      sessions: ['london', 'newyork'],
      volatility: 'low',
      riskLevel: 'low',
      preferredTimeframes: ['4h', '1d'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 70, oversold: 30 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        ema: { enabled: true, periods: [50, 200] }
      }
    },
    'NZDUSD': {
      name: 'NZD/USD',
      nameAr: 'الدولار النيوزيلندي/الدولار الأمريكي',
      category: 'minor',
      spread: 2.5,
      commission: 7,
      leverage: 100,
      minLotSize: 0.01,
      maxLotSize: 50,
      pipValue: 10,
      sessions: ['sydney', 'tokyo'],
      volatility: 'high',
      riskLevel: 'high',
      preferredTimeframes: ['4h', '1d'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 75, oversold: 25 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        bollinger: { enabled: true, period: 20, deviation: 2.5 }
      }
    },
    'XAUUSD': {
      name: 'XAU/USD',
      nameAr: 'الذهب/الدولار الأمريكي',
      category: 'metal',
      spread: 3.0,
      commission: 10,
      leverage: 50,
      minLotSize: 0.01,
      maxLotSize: 10,
      pipValue: 1,
      sessions: ['london', 'newyork'],
      volatility: 'very_high',
      riskLevel: 'high',
      preferredTimeframes: ['1h', '4h'],
      technicalIndicators: {
        rsi: { enabled: true, period: 14, overbought: 80, oversold: 20 },
        macd: { enabled: true, fast: 12, slow: 26, signal: 9 },
        bollinger: { enabled: true, period: 20, deviation: 3 },
        atr: { enabled: true, period: 14 }
      }
    }
  },

  // إعدادات إدارة المخاطر المتقدمة
  riskManagement: {
    // إعدادات عامة
    general: {
      maxRiskPerTrade: 0.02, // 2% من رأس المال لكل صفقة
      maxDailyRisk: 0.06, // 6% حد أقصى للمخاطرة اليومية
      maxWeeklyRisk: 0.15, // 15% حد أقصى للمخاطرة الأسبوعية
      maxDrawdown: 0.20, // 20% حد أقصى للانخفاض
      maxConcurrentTrades: 5, // عدد الصفقات المتزامنة
      minRiskRewardRatio: 1.5, // نسبة المخاطرة للعائد
      emergencyStopLoss: 0.10 // إيقاف الطوارئ عند خسارة 10%
    },

    // إعدادات حسب فئة الأصول
    byCategory: {
      major: {
        maxRiskPerTrade: 0.025,
        maxConcurrentTrades: 3,
        minRiskRewardRatio: 1.5
      },
      minor: {
        maxRiskPerTrade: 0.015,
        maxConcurrentTrades: 2,
        minRiskRewardRatio: 2.0
      },
      exotic: {
        maxRiskPerTrade: 0.01,
        maxConcurrentTrades: 1,
        minRiskRewardRatio: 2.5
      },
      metal: {
        maxRiskPerTrade: 0.02,
        maxConcurrentTrades: 2,
        minRiskRewardRatio: 2.0
      }
    },

    // إعدادات وقف الخسارة
    stopLoss: {
      types: ['fixed', 'trailing', 'atr_based', 'support_resistance'],
      default: 'atr_based',
      trailingDistance: 20, // نقطة
      atrMultiplier: 2.0,
      maxStopLoss: 100 // نقطة
    },

    // إعدادات أهداف الربح
    takeProfit: {
      strategy: 'multiple', // single, multiple, trailing
      levels: [
        { percentage: 50, ratio: 1.5 },
        { percentage: 30, ratio: 2.0 },
        { percentage: 20, ratio: 3.0 }
      ],
      trailingProfit: true,
      breakEvenAfter: 1.0 // نسبة المخاطرة
    }
  },

  // مستويات الثقة والتصفية
  confidenceLevels: {
    minimum: 0.60, // 60% حد أدنى للثقة
    good: 0.70, // 70% ثقة جيدة
    excellent: 0.80, // 80% ثقة ممتازة
    
    // تعديل حجم المركز حسب الثقة
    positionSizing: {
      0.60: 0.5, // 50% من الحجم العادي
      0.70: 0.75, // 75% من الحجم العادي
      0.80: 1.0, // 100% من الحجم العادي
      0.90: 1.25 // 125% من الحجم العادي (حد أقصى)
    }
  },

  // الإطارات الزمنية المدعومة
  timeframes: {
    '1m': {
      name: 'دقيقة واحدة',
      milliseconds: 60000,
      dataPoints: 1440, // يوم واحد
      suitableFor: ['scalping'],
      riskLevel: 'very_high'
    },
    '5m': {
      name: '5 دقائق',
      milliseconds: 300000,
      dataPoints: 288, // يوم واحد
      suitableFor: ['scalping', 'day_trading'],
      riskLevel: 'high'
    },
    '15m': {
      name: '15 دقيقة',
      milliseconds: 900000,
      dataPoints: 96, // يوم واحد
      suitableFor: ['day_trading'],
      riskLevel: 'medium'
    },
    '1h': {
      name: 'ساعة واحدة',
      milliseconds: 3600000,
      dataPoints: 168, // أسبوع واحد
      suitableFor: ['day_trading', 'swing_trading'],
      riskLevel: 'medium'
    },
    '4h': {
      name: '4 ساعات',
      milliseconds: 14400000,
      dataPoints: 42, // أسبوع واحد
      suitableFor: ['swing_trading'],
      riskLevel: 'low'
    },
    '1d': {
      name: 'يوم واحد',
      milliseconds: 86400000,
      dataPoints: 30, // شهر واحد
      suitableFor: ['position_trading'],
      riskLevel: 'very_low'
    }
  },

  // استراتيجيات التداول
  strategies: {
    'trend_following': {
      name: 'تتبع الاتجاه',
      nameAr: 'استراتيجية تتبع الاتجاه',
      description: 'تعتمد على تحديد الاتجاه العام والتداول معه',
      indicators: ['ema', 'macd', 'atr'],
      timeframes: ['1h', '4h', '1d'],
      riskLevel: 'medium',
      winRate: 0.65,
      enabled: true
    },
    'mean_reversion': {
      name: 'العودة للمتوسط',
      nameAr: 'استراتيجية العودة للمتوسط',
      description: 'تعتمد على انحراف السعر عن المتوسط والعودة إليه',
      indicators: ['rsi', 'bollinger', 'stochastic'],
      timeframes: ['15m', '1h', '4h'],
      riskLevel: 'high',
      winRate: 0.70,
      enabled: true
    },
    'breakout': {
      name: 'الاختراق',
      nameAr: 'استراتيجية الاختراق',
      description: 'تعتمد على اختراق مستويات الدعم والمقاومة',
      indicators: ['bollinger', 'atr', 'volume'],
      timeframes: ['1h', '4h'],
      riskLevel: 'high',
      winRate: 0.60,
      enabled: true
    },
    'scalping': {
      name: 'المضاربة السريعة',
      nameAr: 'استراتيجية المضاربة السريعة',
      description: 'تداول سريع لفترات قصيرة جداً',
      indicators: ['ema', 'rsi', 'stochastic'],
      timeframes: ['1m', '5m'],
      riskLevel: 'very_high',
      winRate: 0.55,
      enabled: false // معطلة افتراضياً
    }
  },

  // إعدادات التحليل المتقدم
  analysis: {
    // تحليل الشموع اليابانية
    candlestickPatterns: {
      enabled: true,
      patterns: [
        'doji', 'hammer', 'shooting_star', 'engulfing',
        'harami', 'morning_star', 'evening_star'
      ],
      minConfidence: 0.7
    },

    // تحليل الحجم
    volumeAnalysis: {
      enabled: true,
      indicators: ['volume_sma', 'volume_weighted_price', 'on_balance_volume'],
      minVolumeThreshold: 1000000
    },

    // تحليل المشاعر
    sentimentAnalysis: {
      enabled: true,
      sources: ['news', 'social_media', 'economic_calendar'],
      weight: 0.3 // 30% من إجمالي التحليل
    },

    // تحليل الارتباط
    correlationAnalysis: {
      enabled: true,
      pairs: ['EURUSD', 'GBPUSD', 'USDJPY'],
      threshold: 0.7
    }
  },

  // إعدادات الأداء والتحسين
  performance: {
    // تحديث البيانات
    dataUpdate: {
      realtime: true,
      interval: 1000, // ميلي ثانية
      batchSize: 100,
      maxRetries: 3
    },

    // التخزين المؤقت
    caching: {
      enabled: true,
      ttl: 300, // ثانية
      maxKeys: 10000,
      compression: true
    },

    // المعالجة المتوازية
    parallel: {
      enabled: true,
      maxWorkers: 4,
      taskTimeout: 30000 // ميلي ثانية
    }
  }
};

export default tradingConfig;
