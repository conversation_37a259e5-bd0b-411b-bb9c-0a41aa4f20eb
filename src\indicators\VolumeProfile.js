import logger from '../utils/logger.js';

class VolumeProfile {
  constructor() {
    this.cache = new Map();
  }

  // Calculate Volume Profile for Visible Range
  calculateVolumeProfile(highs, lows, closes, volumes, bins = 50) {
    try {
      if (!highs || !lows || !closes || !volumes || 
          highs.length !== lows.length || 
          lows.length !== closes.length || 
          closes.length !== volumes.length) {
        throw new Error('Invalid data for Volume Profile calculation');
      }

      const minPrice = Math.min(...lows);
      const maxPrice = Math.max(...highs);
      const priceRange = maxPrice - minPrice;
      const binSize = priceRange / bins;

      // Initialize bins
      const volumeBins = Array(bins).fill(0).map((_, index) => ({
        priceLevel: minPrice + (index * binSize),
        volume: 0,
        buyVolume: 0,
        sellVolume: 0,
        trades: 0
      }));

      // Distribute volume across price levels
      for (let i = 0; i < closes.length; i++) {
        const high = highs[i];
        const low = lows[i];
        const close = closes[i];
        const volume = volumes[i];
        const open = i > 0 ? closes[i - 1] : close;

        // Determine if candle is bullish or bearish
        const isBullish = close > open;
        
        // Calculate which bins this candle affects
        const startBin = Math.max(0, Math.floor((low - minPrice) / binSize));
        const endBin = Math.min(bins - 1, Math.floor((high - minPrice) / binSize));
        
        // Distribute volume proportionally across affected bins
        const affectedBins = endBin - startBin + 1;
        const volumePerBin = volume / affectedBins;
        
        for (let binIndex = startBin; binIndex <= endBin; binIndex++) {
          volumeBins[binIndex].volume += volumePerBin;
          volumeBins[binIndex].trades += 1;
          
          if (isBullish) {
            volumeBins[binIndex].buyVolume += volumePerBin;
          } else {
            volumeBins[binIndex].sellVolume += volumePerBin;
          }
        }
      }

      // Find Point of Control (POC) - highest volume level
      const poc = volumeBins.reduce((max, bin) => 
        bin.volume > max.volume ? bin : max, volumeBins[0]);

      // Calculate Value Area (70% of total volume)
      const totalVolume = volumeBins.reduce((sum, bin) => sum + bin.volume, 0);
      const valueAreaVolume = totalVolume * 0.7;
      
      const { valueAreaHigh, valueAreaLow } = this.calculateValueArea(volumeBins, poc, valueAreaVolume);

      // Identify High Volume Nodes (HVN) and Low Volume Nodes (LVN)
      const avgVolume = totalVolume / bins;
      const hvnThreshold = avgVolume * 1.5;
      const lvnThreshold = avgVolume * 0.5;

      const highVolumeNodes = volumeBins.filter(bin => bin.volume > hvnThreshold);
      const lowVolumeNodes = volumeBins.filter(bin => bin.volume < lvnThreshold);

      return {
        bins: volumeBins,
        poc: poc,
        valueAreaHigh: valueAreaHigh,
        valueAreaLow: valueAreaLow,
        totalVolume: totalVolume,
        highVolumeNodes: highVolumeNodes,
        lowVolumeNodes: lowVolumeNodes,
        buyVolume: volumeBins.reduce((sum, bin) => sum + bin.buyVolume, 0),
        sellVolume: volumeBins.reduce((sum, bin) => sum + bin.sellVolume, 0),
        volumeImbalance: this.calculateVolumeImbalance(volumeBins),
        support: this.identifyVolumeSupport(volumeBins, closes[closes.length - 1]),
        resistance: this.identifyVolumeResistance(volumeBins, closes[closes.length - 1])
      };
    } catch (error) {
      logger.error('Volume Profile calculation error:', error);
      return null;
    }
  }

  // Calculate Value Area (70% of volume around POC)
  calculateValueArea(volumeBins, poc, targetVolume) {
    const pocIndex = volumeBins.indexOf(poc);
    let accumulatedVolume = poc.volume;
    let upperIndex = pocIndex;
    let lowerIndex = pocIndex;

    while (accumulatedVolume < targetVolume && 
           (upperIndex < volumeBins.length - 1 || lowerIndex > 0)) {
      
      const upperVolume = upperIndex < volumeBins.length - 1 ? 
        volumeBins[upperIndex + 1].volume : 0;
      const lowerVolume = lowerIndex > 0 ? 
        volumeBins[lowerIndex - 1].volume : 0;

      if (upperVolume > lowerVolume && upperIndex < volumeBins.length - 1) {
        upperIndex++;
        accumulatedVolume += upperVolume;
      } else if (lowerIndex > 0) {
        lowerIndex--;
        accumulatedVolume += lowerVolume;
      } else if (upperIndex < volumeBins.length - 1) {
        upperIndex++;
        accumulatedVolume += upperVolume;
      } else {
        break;
      }
    }

    return {
      valueAreaHigh: volumeBins[upperIndex].priceLevel,
      valueAreaLow: volumeBins[lowerIndex].priceLevel
    };
  }

  // Calculate volume imbalance
  calculateVolumeImbalance(volumeBins) {
    const totalBuyVolume = volumeBins.reduce((sum, bin) => sum + bin.buyVolume, 0);
    const totalSellVolume = volumeBins.reduce((sum, bin) => sum + bin.sellVolume, 0);
    const totalVolume = totalBuyVolume + totalSellVolume;

    if (totalVolume === 0) return 0;

    const buyPercentage = (totalBuyVolume / totalVolume) * 100;
    const sellPercentage = (totalSellVolume / totalVolume) * 100;

    return {
      buyPercentage: buyPercentage,
      sellPercentage: sellPercentage,
      imbalance: buyPercentage - sellPercentage,
      dominantSide: buyPercentage > sellPercentage ? 'buy' : 'sell'
    };
  }

  // Identify volume-based support levels
  identifyVolumeSupport(volumeBins, currentPrice) {
    const supportLevels = volumeBins
      .filter(bin => bin.priceLevel < currentPrice && bin.volume > 0)
      .sort((a, b) => b.volume - a.volume)
      .slice(0, 5)
      .map(bin => ({
        price: bin.priceLevel,
        volume: bin.volume,
        strength: this.calculateSupportStrength(bin, volumeBins),
        distance: currentPrice - bin.priceLevel
      }));

    return supportLevels;
  }

  // Identify volume-based resistance levels
  identifyVolumeResistance(volumeBins, currentPrice) {
    const resistanceLevels = volumeBins
      .filter(bin => bin.priceLevel > currentPrice && bin.volume > 0)
      .sort((a, b) => b.volume - a.volume)
      .slice(0, 5)
      .map(bin => ({
        price: bin.priceLevel,
        volume: bin.volume,
        strength: this.calculateResistanceStrength(bin, volumeBins),
        distance: bin.priceLevel - currentPrice
      }));

    return resistanceLevels;
  }

  // Calculate support strength based on volume
  calculateSupportStrength(bin, allBins) {
    const maxVolume = Math.max(...allBins.map(b => b.volume));
    const volumeRatio = bin.volume / maxVolume;
    
    // Consider buy/sell ratio
    const buyRatio = bin.buyVolume / (bin.buyVolume + bin.sellVolume);
    
    return (volumeRatio * 0.7) + (buyRatio * 0.3);
  }

  // Calculate resistance strength based on volume
  calculateResistanceStrength(bin, allBins) {
    const maxVolume = Math.max(...allBins.map(b => b.volume));
    const volumeRatio = bin.volume / maxVolume;
    
    // Consider sell/buy ratio for resistance
    const sellRatio = bin.sellVolume / (bin.buyVolume + bin.sellVolume);
    
    return (volumeRatio * 0.7) + (sellRatio * 0.3);
  }

  // Analyze volume distribution
  analyzeVolumeDistribution(volumeProfile) {
    if (!volumeProfile || !volumeProfile.bins) return null;

    const { bins, poc, valueAreaHigh, valueAreaLow } = volumeProfile;
    
    // Calculate distribution metrics
    const volumeArray = bins.map(bin => bin.volume);
    const mean = volumeArray.reduce((sum, vol) => sum + vol, 0) / volumeArray.length;
    const variance = volumeArray.reduce((sum, vol) => sum + Math.pow(vol - mean, 2), 0) / volumeArray.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Determine distribution shape
    const distributionShape = this.determineDistributionShape(bins, poc);
    
    // Calculate market structure
    const marketStructure = this.analyzeMarketStructure(volumeProfile);

    return {
      mean: mean,
      standardDeviation: standardDeviation,
      distributionShape: distributionShape,
      marketStructure: marketStructure,
      volumeConcentration: this.calculateVolumeConcentration(bins),
      tradingRange: {
        high: valueAreaHigh,
        low: valueAreaLow,
        poc: poc.priceLevel
      }
    };
  }

  // Determine if distribution is normal, bimodal, or skewed
  determineDistributionShape(bins, poc) {
    const pocIndex = bins.indexOf(poc);
    const totalBins = bins.length;
    
    // Check for bimodal distribution
    const peaks = this.findVolumePeaks(bins);
    if (peaks.length > 1) {
      return 'bimodal';
    }
    
    // Check for skewness
    const pocPosition = pocIndex / totalBins;
    if (pocPosition < 0.3) {
      return 'left_skewed';
    } else if (pocPosition > 0.7) {
      return 'right_skewed';
    }
    
    return 'normal';
  }

  // Find volume peaks in the profile
  findVolumePeaks(bins, minProminence = 0.1) {
    const peaks = [];
    const avgVolume = bins.reduce((sum, bin) => sum + bin.volume, 0) / bins.length;
    const threshold = avgVolume * (1 + minProminence);
    
    for (let i = 1; i < bins.length - 1; i++) {
      if (bins[i].volume > threshold &&
          bins[i].volume > bins[i - 1].volume &&
          bins[i].volume > bins[i + 1].volume) {
        peaks.push(bins[i]);
      }
    }
    
    return peaks;
  }

  // Analyze market structure based on volume profile
  analyzeMarketStructure(volumeProfile) {
    const { poc, valueAreaHigh, valueAreaLow, volumeImbalance } = volumeProfile;
    
    const range = valueAreaHigh - valueAreaLow;
    const pocPosition = (poc.priceLevel - valueAreaLow) / range;
    
    let structure = 'balanced';
    
    if (volumeImbalance.imbalance > 20) {
      structure = 'bullish_accumulation';
    } else if (volumeImbalance.imbalance < -20) {
      structure = 'bearish_distribution';
    } else if (pocPosition < 0.3) {
      structure = 'support_building';
    } else if (pocPosition > 0.7) {
      structure = 'resistance_building';
    }
    
    return {
      type: structure,
      pocPosition: pocPosition,
      volumeBalance: volumeImbalance.dominantSide,
      strength: Math.abs(volumeImbalance.imbalance) / 100
    };
  }

  // Calculate volume concentration
  calculateVolumeConcentration(bins) {
    const totalVolume = bins.reduce((sum, bin) => sum + bin.volume, 0);
    const sortedBins = [...bins].sort((a, b) => b.volume - a.volume);
    
    // Calculate what percentage of volume is in top 20% of bins
    const top20PercentCount = Math.ceil(bins.length * 0.2);
    const top20PercentVolume = sortedBins.slice(0, top20PercentCount)
      .reduce((sum, bin) => sum + bin.volume, 0);
    
    const concentration = (top20PercentVolume / totalVolume) * 100;
    
    return {
      percentage: concentration,
      level: concentration > 70 ? 'high' : concentration > 50 ? 'medium' : 'low'
    };
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
  }
}

export default VolumeProfile;
