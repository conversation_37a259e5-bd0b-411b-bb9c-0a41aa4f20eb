// خادم بسيط بدون ES modules
const express = require('express');
const path = require('path');

console.log('🚀 بدء تشغيل AyoPFX Trading Bot...');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AyoPFX Trading Bot</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                text-align: center; 
                padding: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
                margin: 0;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                max-width: 600px;
                margin: 0 auto;
            }
            h1 { font-size: 3em; margin-bottom: 20px; }
            .status { font-size: 1.5em; margin: 20px 0; }
            .links { margin-top: 30px; }
            .links a { 
                display: inline-block; 
                margin: 10px; 
                padding: 15px 30px; 
                background: rgba(255,255,255,0.2); 
                color: white; 
                text-decoration: none; 
                border-radius: 10px;
                transition: all 0.3s;
            }
            .links a:hover { 
                background: rgba(255,255,255,0.3); 
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 AyoPFX Trading Bot</h1>
            <div class="status">✅ البوت يعمل بنجاح!</div>
            <p>بوت التداول الاحترافي المدعوم بالذكاء الاصطناعي</p>
            
            <div class="links">
                <a href="/health">🏥 فحص الصحة</a>
                <a href="/api/status">📊 حالة النظام</a>
                <a href="/api/signals">📈 الإشارات</a>
            </div>
            
            <p style="margin-top: 40px; opacity: 0.8;">
                الوقت: ${new Date().toLocaleString('ar-EG')}<br>
                وقت التشغيل: ${Math.floor(process.uptime())} ثانية
            </p>
        </div>
    </body>
    </html>
  `);
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    memory: process.memoryUsage(),
    platform: process.platform,
    nodeVersion: process.version
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    status: 'success',
    data: {
      isRunning: true,
      services: {
        dataService: true,
        aiService: true,
        notificationService: true,
        tradingEngine: true
      },
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    }
  });
});

app.get('/api/signals', (req, res) => {
  const mockSignals = [
    {
      id: 1,
      symbol: 'EUR/USD',
      direction: 'شراء',
      entry: 1.1000,
      stopLoss: 1.0950,
      takeProfit: 1.1100,
      confidence: '85%',
      timestamp: new Date().toISOString()
    },
    {
      id: 2,
      symbol: 'GBP/USD',
      direction: 'بيع',
      entry: 1.2500,
      stopLoss: 1.2550,
      takeProfit: 1.2400,
      confidence: '78%',
      timestamp: new Date().toISOString()
    }
  ];

  res.json({
    status: 'success',
    data: {
      signals: mockSignals,
      count: mockSignals.length,
      lastUpdate: new Date().toISOString()
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('خطأ:', err);
  res.status(500).json({
    status: 'error',
    message: 'حدث خطأ في الخادم'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`✅ AyoPFX Trading Bot يعمل على المنفذ ${PORT}`);
  console.log(`🌐 افتح المتصفح على: http://localhost:${PORT}`);
  console.log(`🏥 فحص الصحة: http://localhost:${PORT}/health`);
  console.log(`📊 API: http://localhost:${PORT}/api/status`);
  console.log(`📈 الإشارات: http://localhost:${PORT}/api/signals`);
  console.log('');
  console.log('🎉 البوت جاهز للاستخدام!');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('إيقاف البوت...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('إيقاف البوت...');
  process.exit(0);
});
