// Mock Redis for development without Redis server
import config from '../config/config.js';
import logger from '../utils/logger.js';

let redisClient = null;
let isConnected = false;
let mockCache = new Map(); // Mock cache for development

// Mock Redis implementation
const mockRedis = {
  async set(key, value, ttl) {
    mockCache.set(key, { value, expires: ttl ? Date.now() + (ttl * 1000) : null });
    return 'OK';
  },

  async get(key) {
    const item = mockCache.get(key);
    if (!item) return null;
    if (item.expires && Date.now() > item.expires) {
      mockCache.delete(key);
      return null;
    }
    return item.value;
  },

  async del(key) {
    return mockCache.delete(key) ? 1 : 0;
  },

  async exists(key) {
    return mockCache.has(key) ? 1 : 0;
  },

  async ping() {
    return 'PONG';
  }
};

export const initializeDatabase = async () => {
  try {
    logger.info('Initializing database (Mock mode for development)...');

    // Use mock Redis for development
    redisClient = mockRedis;
    isConnected = true;

    logger.info('✅ Mock database initialized successfully');
    return true;
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    isConnected = true; // Continue with mock
    return true;
  }
};

export const getRedisClient = () => {
  if (!redisClient || !isConnected) {
    logger.warn('Redis client not initialized, using mock');
    return mockRedis;
  }
  return redisClient;
};

export const disconnectRedis = async () => {
  try {
    if (redisClient && isConnected) {
      // For mock Redis, just clear the cache
      mockCache.clear();
      isConnected = false;
      logger.info('✅ Disconnected from mock database');
    }
  } catch (error) {
    logger.error('❌ Error disconnecting from database:', error);
    // Don't throw error for mock
  }
};

// Cache utilities
export const cacheUtils = {
  // Set data with expiration
  async set(key, value, expireInSeconds = 3600) {
    try {
      const client = getRedisClient();
      const serializedValue = JSON.stringify(value);
      await client.set(key, serializedValue, expireInSeconds);
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  },

  // Get data
  async get(key) {
    try {
      const client = getRedisClient();
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  },

  // Delete data
  async del(key) {
    try {
      const client = getRedisClient();
      await client.del(key);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  },

  // Check if key exists
  async exists(key) {
    try {
      const client = getRedisClient();
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error:', error);
      return false;
    }
  },

  // Set expiration
  async expire(key, seconds) {
    try {
      const client = getRedisClient();
      await client.expire(key, seconds);
      return true;
    } catch (error) {
      logger.error('Redis expire error:', error);
      return false;
    }
  },

  // Get TTL
  async ttl(key) {
    try {
      const client = getRedisClient();
      return await client.ttl(key);
    } catch (error) {
      logger.error('Redis TTL error:', error);
      return -1;
    }
  },

  // Increment counter
  async incr(key) {
    try {
      const client = getRedisClient();
      return await client.incr(key);
    } catch (error) {
      logger.error('Redis increment error:', error);
      return 0;
    }
  },

  // Add to set
  async sadd(key, ...members) {
    try {
      const client = getRedisClient();
      return await client.sAdd(key, members);
    } catch (error) {
      logger.error('Redis sadd error:', error);
      return 0;
    }
  },

  // Get set members
  async smembers(key) {
    try {
      const client = getRedisClient();
      return await client.sMembers(key);
    } catch (error) {
      logger.error('Redis smembers error:', error);
      return [];
    }
  },

  // Remove from set
  async srem(key, ...members) {
    try {
      const client = getRedisClient();
      return await client.sRem(key, members);
    } catch (error) {
      logger.error('Redis srem error:', error);
      return 0;
    }
  },

  // Push to list
  async lpush(key, ...elements) {
    try {
      const client = getRedisClient();
      return await client.lPush(key, elements.map(el => JSON.stringify(el)));
    } catch (error) {
      logger.error('Redis lpush error:', error);
      return 0;
    }
  },

  // Get list range
  async lrange(key, start = 0, stop = -1) {
    try {
      const client = getRedisClient();
      const values = await client.lRange(key, start, stop);
      return values.map(val => JSON.parse(val));
    } catch (error) {
      logger.error('Redis lrange error:', error);
      return [];
    }
  },

  // Trim list
  async ltrim(key, start, stop) {
    try {
      const client = getRedisClient();
      await client.lTrim(key, start, stop);
      return true;
    } catch (error) {
      logger.error('Redis ltrim error:', error);
      return false;
    }
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  await disconnectRedis();
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
});

export default {
  initializeRedis,
  getRedisClient,
  disconnectRedis,
  cacheUtils
};
