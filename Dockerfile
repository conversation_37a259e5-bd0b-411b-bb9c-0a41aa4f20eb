# استخدام Node.js 18 كصورة أساسية
FROM node:18-alpine

# تعيين معلومات المطور
LABEL maintainer="AyoPFX Team <<EMAIL>>"
LABEL description="AyoPFX Trading Bot - Professional AI-powered trading bot"
LABEL version="1.0.0"

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV PORT=3000
ENV TZ=UTC

# إنشاء مستخدم غير جذر للأمان
RUN addgroup -g 1001 -S nodejs && \
    adduser -S ayopfx -u 1001

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات
RUN npm ci --only=production && \
    npm cache clean --force

# نسخ ملفات المشروع
COPY --chown=ayopfx:nodejs . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p logs models backups && \
    chown -R ayopfx:nodejs logs models backups

# تعيين الصلاحيات
RUN chmod +x src/app.js

# التبديل إلى المستخدم غير الجذر
USER ayopfx

# كشف المنفذ
EXPOSE 3000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# تشغيل التطبيق
CMD ["node", "src/app.js"]
