import logger from '../utils/logger.js';

class AdvancedIndicators {
  constructor() {
    this.cache = new Map();
  }

  // Fair Value Gap (FVG) Detection
  detectFairValueGaps(highs, lows, closes, minGapSize = 0.001) {
    try {
      if (!highs || !lows || !closes || highs.length < 3) {
        throw new Error('Insufficient data for FVG detection');
      }

      const fvgs = [];
      
      for (let i = 2; i < highs.length; i++) {
        // Bullish FVG: Current low > Previous high (gap up)
        const bullishGap = lows[i] > highs[i - 2];
        
        // Bearish FVG: Current high < Previous low (gap down)
        const bearishGap = highs[i] < lows[i - 2];
        
        if (bullishGap) {
          const gapSize = lows[i] - highs[i - 2];
          if (gapSize >= minGapSize) {
            fvgs.push({
              type: 'bullish',
              index: i,
              upperLevel: lows[i],
              lowerLevel: highs[i - 2],
              size: gapSize,
              filled: false,
              timestamp: i
            });
          }
        }
        
        if (bearishGap) {
          const gapSize = lows[i - 2] - highs[i];
          if (gapSize >= minGapSize) {
            fvgs.push({
              type: 'bearish',
              index: i,
              upperLevel: lows[i - 2],
              lowerLevel: highs[i],
              size: gapSize,
              filled: false,
              timestamp: i
            });
          }
        }
      }

      // Check if gaps are filled
      this.checkFVGFilled(fvgs, highs, lows);

      return {
        gaps: fvgs,
        activeBullishGaps: fvgs.filter(gap => gap.type === 'bullish' && !gap.filled),
        activeBearishGaps: fvgs.filter(gap => gap.type === 'bearish' && !gap.filled),
        totalGaps: fvgs.length,
        recentGaps: fvgs.slice(-5)
      };
    } catch (error) {
      logger.error('FVG detection error:', error);
      return null;
    }
  }

  // Change of Character (CHoCH) Detection
  detectChangeOfCharacter(highs, lows, closes, lookbackPeriod = 20) {
    try {
      if (!highs || !lows || !closes || highs.length < lookbackPeriod) {
        throw new Error('Insufficient data for CHoCH detection');
      }

      const swingPoints = this.identifySwingPoints(highs, lows, 5);
      const chochSignals = [];
      
      for (let i = 4; i < swingPoints.length; i++) {
        const current = swingPoints[i];
        const previous = swingPoints[i - 1];
        const beforePrevious = swingPoints[i - 2];
        
        // Bullish CHoCH: Break of previous swing high after downtrend
        if (current.type === 'high' && 
            previous.type === 'low' && 
            beforePrevious.type === 'high' &&
            current.value > beforePrevious.value &&
            this.isDowntrend(swingPoints.slice(i - 4, i))) {
          
          chochSignals.push({
            type: 'bullish',
            index: current.index,
            price: current.value,
            brokenLevel: beforePrevious.value,
            strength: this.calculateCHoCHStrength(current, beforePrevious, swingPoints.slice(i - 4, i))
          });
        }
        
        // Bearish CHoCH: Break of previous swing low after uptrend
        if (current.type === 'low' && 
            previous.type === 'high' && 
            beforePrevious.type === 'low' &&
            current.value < beforePrevious.value &&
            this.isUptrend(swingPoints.slice(i - 4, i))) {
          
          chochSignals.push({
            type: 'bearish',
            index: current.index,
            price: current.value,
            brokenLevel: beforePrevious.value,
            strength: this.calculateCHoCHStrength(current, beforePrevious, swingPoints.slice(i - 4, i))
          });
        }
      }

      return {
        signals: chochSignals,
        recentSignals: chochSignals.slice(-3),
        lastSignal: chochSignals[chochSignals.length - 1] || null,
        trendChange: chochSignals.length > 0
      };
    } catch (error) {
      logger.error('CHoCH detection error:', error);
      return null;
    }
  }

  // Break of Structure (BOS) Detection
  detectBreakOfStructure(highs, lows, closes, lookbackPeriod = 15) {
    try {
      if (!highs || !lows || !closes || highs.length < lookbackPeriod) {
        throw new Error('Insufficient data for BOS detection');
      }

      const swingPoints = this.identifySwingPoints(highs, lows, 3);
      const bosSignals = [];
      
      for (let i = 2; i < swingPoints.length; i++) {
        const current = swingPoints[i];
        const previous = swingPoints[i - 1];
        
        // Bullish BOS: Break of recent swing high in uptrend
        if (current.type === 'high' && 
            previous.type === 'low' &&
            current.value > this.getRecentHigh(swingPoints.slice(0, i)) &&
            this.isUptrend(swingPoints.slice(Math.max(0, i - 4), i))) {
          
          bosSignals.push({
            type: 'bullish',
            index: current.index,
            price: current.value,
            brokenLevel: this.getRecentHigh(swingPoints.slice(0, i)),
            strength: this.calculateBOSStrength(current, swingPoints.slice(Math.max(0, i - 4), i))
          });
        }
        
        // Bearish BOS: Break of recent swing low in downtrend
        if (current.type === 'low' && 
            previous.type === 'high' &&
            current.value < this.getRecentLow(swingPoints.slice(0, i)) &&
            this.isDowntrend(swingPoints.slice(Math.max(0, i - 4), i))) {
          
          bosSignals.push({
            type: 'bearish',
            index: current.index,
            price: current.value,
            brokenLevel: this.getRecentLow(swingPoints.slice(0, i)),
            strength: this.calculateBOSStrength(current, swingPoints.slice(Math.max(0, i - 4), i))
          });
        }
      }

      return {
        signals: bosSignals,
        recentSignals: bosSignals.slice(-3),
        lastSignal: bosSignals[bosSignals.length - 1] || null,
        momentum: this.calculateMomentum(bosSignals)
      };
    } catch (error) {
      logger.error('BOS detection error:', error);
      return null;
    }
  }

  // Support and Resistance Levels
  identifySupportResistance(highs, lows, closes, touchThreshold = 3, proximityPercent = 0.1) {
    try {
      if (!highs || !lows || !closes || highs.length < 20) {
        throw new Error('Insufficient data for support/resistance identification');
      }

      const levels = [];
      const swingPoints = this.identifySwingPoints(highs, lows, 5);
      
      // Group swing points by price level
      const priceGroups = new Map();
      
      swingPoints.forEach(point => {
        const key = Math.round(point.value / (proximityPercent / 100)) * (proximityPercent / 100);
        if (!priceGroups.has(key)) {
          priceGroups.set(key, []);
        }
        priceGroups.get(key).push(point);
      });

      // Identify significant levels
      priceGroups.forEach((points, price) => {
        if (points.length >= touchThreshold) {
          const highs = points.filter(p => p.type === 'high').length;
          const lows = points.filter(p => p.type === 'low').length;
          
          levels.push({
            price: price,
            touches: points.length,
            type: highs > lows ? 'resistance' : 'support',
            strength: this.calculateLevelStrength(points),
            lastTouch: Math.max(...points.map(p => p.index)),
            points: points
          });
        }
      });

      // Sort by strength
      levels.sort((a, b) => b.strength - a.strength);

      return {
        levels: levels,
        support: levels.filter(l => l.type === 'support'),
        resistance: levels.filter(l => l.type === 'resistance'),
        strongLevels: levels.filter(l => l.strength > 0.7),
        recentLevels: levels.filter(l => l.lastTouch > highs.length - 50)
      };
    } catch (error) {
      logger.error('Support/Resistance identification error:', error);
      return null;
    }
  }

  // Order Blocks Detection
  detectOrderBlocks(opens, highs, lows, closes, volumes, minBlockSize = 0.002) {
    try {
      if (!opens || !highs || !lows || !closes || opens.length < 10) {
        throw new Error('Insufficient data for Order Blocks detection');
      }

      const orderBlocks = [];
      
      for (let i = 3; i < opens.length - 1; i++) {
        const currentCandle = {
          open: opens[i],
          high: highs[i],
          low: lows[i],
          close: closes[i],
          volume: volumes ? volumes[i] : 1
        };

        // Bullish Order Block: Strong bullish candle followed by continuation
        if (this.isBullishOrderBlock(opens, highs, lows, closes, i)) {
          orderBlocks.push({
            type: 'bullish',
            index: i,
            high: currentCandle.high,
            low: currentCandle.low,
            open: currentCandle.open,
            close: currentCandle.close,
            volume: currentCandle.volume,
            strength: this.calculateOrderBlockStrength(opens, highs, lows, closes, i),
            tested: false
          });
        }

        // Bearish Order Block: Strong bearish candle followed by continuation
        if (this.isBearishOrderBlock(opens, highs, lows, closes, i)) {
          orderBlocks.push({
            type: 'bearish',
            index: i,
            high: currentCandle.high,
            low: currentCandle.low,
            open: currentCandle.open,
            close: currentCandle.close,
            volume: currentCandle.volume,
            strength: this.calculateOrderBlockStrength(opens, highs, lows, closes, i),
            tested: false
          });
        }
      }

      return {
        blocks: orderBlocks,
        bullishBlocks: orderBlocks.filter(b => b.type === 'bullish'),
        bearishBlocks: orderBlocks.filter(b => b.type === 'bearish'),
        recentBlocks: orderBlocks.slice(-5),
        activeBlocks: orderBlocks.filter(b => !b.tested)
      };
    } catch (error) {
      logger.error('Order Blocks detection error:', error);
      return null;
    }
  }

  // Helper Methods
  identifySwingPoints(highs, lows, lookback = 5) {
    const swingPoints = [];
    
    for (let i = lookback; i < highs.length - lookback; i++) {
      const isSwingHigh = highs.slice(i - lookback, i + lookback + 1)
        .every((val, idx) => idx === lookback || val <= highs[i]);
      
      const isSwingLow = lows.slice(i - lookback, i + lookback + 1)
        .every((val, idx) => idx === lookback || val >= lows[i]);
      
      if (isSwingHigh) {
        swingPoints.push({ type: 'high', value: highs[i], index: i });
      }
      
      if (isSwingLow) {
        swingPoints.push({ type: 'low', value: lows[i], index: i });
      }
    }
    
    return swingPoints.sort((a, b) => a.index - b.index);
  }

  checkFVGFilled(fvgs, highs, lows) {
    fvgs.forEach(gap => {
      for (let i = gap.index + 1; i < highs.length; i++) {
        if (gap.type === 'bullish' && lows[i] <= gap.lowerLevel) {
          gap.filled = true;
          gap.filledAt = i;
          break;
        }
        if (gap.type === 'bearish' && highs[i] >= gap.upperLevel) {
          gap.filled = true;
          gap.filledAt = i;
          break;
        }
      }
    });
  }

  isUptrend(swingPoints) {
    if (swingPoints.length < 4) return false;
    const highs = swingPoints.filter(p => p.type === 'high');
    const lows = swingPoints.filter(p => p.type === 'low');
    
    return highs.length >= 2 && lows.length >= 2 &&
           highs[highs.length - 1].value > highs[highs.length - 2].value &&
           lows[lows.length - 1].value > lows[lows.length - 2].value;
  }

  isDowntrend(swingPoints) {
    if (swingPoints.length < 4) return false;
    const highs = swingPoints.filter(p => p.type === 'high');
    const lows = swingPoints.filter(p => p.type === 'low');
    
    return highs.length >= 2 && lows.length >= 2 &&
           highs[highs.length - 1].value < highs[highs.length - 2].value &&
           lows[lows.length - 1].value < lows[lows.length - 2].value;
  }

  getRecentHigh(swingPoints) {
    const highs = swingPoints.filter(p => p.type === 'high');
    return highs.length > 0 ? Math.max(...highs.map(h => h.value)) : 0;
  }

  getRecentLow(swingPoints) {
    const lows = swingPoints.filter(p => p.type === 'low');
    return lows.length > 0 ? Math.min(...lows.map(l => l.value)) : Infinity;
  }

  calculateCHoCHStrength(current, broken, context) {
    const breakSize = Math.abs(current.value - broken.value);
    const contextRange = Math.max(...context.map(p => p.value)) - Math.min(...context.map(p => p.value));
    return Math.min(breakSize / contextRange, 1);
  }

  calculateBOSStrength(current, context) {
    const momentum = context.length > 1 ? 
      Math.abs(current.value - context[context.length - 2].value) : 0;
    const avgRange = context.reduce((sum, p, i, arr) => 
      i > 0 ? sum + Math.abs(p.value - arr[i-1].value) : sum, 0) / Math.max(context.length - 1, 1);
    return Math.min(momentum / avgRange, 1);
  }

  calculateLevelStrength(points) {
    const touches = points.length;
    const timeSpread = Math.max(...points.map(p => p.index)) - Math.min(...points.map(p => p.index));
    return Math.min((touches * 0.3) + (timeSpread * 0.001), 1);
  }

  calculateMomentum(signals) {
    if (signals.length < 2) return 0;
    const recent = signals.slice(-3);
    return recent.reduce((sum, signal) => sum + signal.strength, 0) / recent.length;
  }

  isBullishOrderBlock(opens, highs, lows, closes, index) {
    const bodySize = Math.abs(closes[index] - opens[index]);
    const candleRange = highs[index] - lows[index];
    const isBullish = closes[index] > opens[index];
    const strongBody = bodySize / candleRange > 0.6;
    
    // Check for continuation in next candles
    const continuation = index < closes.length - 2 && 
      closes[index + 1] > closes[index] && 
      closes[index + 2] > closes[index + 1];
    
    return isBullish && strongBody && continuation;
  }

  isBearishOrderBlock(opens, highs, lows, closes, index) {
    const bodySize = Math.abs(closes[index] - opens[index]);
    const candleRange = highs[index] - lows[index];
    const isBearish = closes[index] < opens[index];
    const strongBody = bodySize / candleRange > 0.6;
    
    // Check for continuation in next candles
    const continuation = index < closes.length - 2 && 
      closes[index + 1] < closes[index] && 
      closes[index + 2] < closes[index + 1];
    
    return isBearish && strongBody && continuation;
  }

  calculateOrderBlockStrength(opens, highs, lows, closes, index) {
    const bodySize = Math.abs(closes[index] - opens[index]);
    const candleRange = highs[index] - lows[index];
    const bodyRatio = bodySize / candleRange;
    
    // Check volume if available
    const volumeStrength = 1; // Default if no volume data
    
    return Math.min(bodyRatio * volumeStrength, 1);
  }
}

export default AdvancedIndicators;
