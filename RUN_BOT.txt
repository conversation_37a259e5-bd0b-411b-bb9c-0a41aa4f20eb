🤖 AyoPFX Trading Bot - دليل التشغيل السريع
================================================

⚡ التشغيل السريع (30 ثانية):
================================

1️⃣ على Windows:
   - انقر مرتين على: start-bot.bat
   - أو افتح Command Prompt واكتب: npm start

2️⃣ على Mac/Linux:
   - افتح Terminal واكتب: ./start-bot.sh
   - أو: npm start

3️⃣ افتح المتصفح على:
   http://localhost:3000

🎉 البوت جاهز للاستخدام!

================================================

🛠️ طرق التشغيل المختلفة:
==========================

للمبتدئين:
-----------
npm start                    # البوت البسيط
node simple-bot.js          # تشغيل مباشر

للمحترفين:
-----------
npm run start:advanced      # البوت المتقدم
node src/app.js            # تشغيل مباشر متقدم

للمطورين:
----------
npm run dev                 # مع مراقبة الملفات
npm run dev:advanced       # متقدم مع مراقبة

للإنتاج:
---------
npm run pm2:start          # تشغيل مع PM2
npm run pm2:logs           # عرض السجلات
npm run pm2:stop           # إيقاف البوت

================================================

🌐 الروابط المتاحة:
==================

الواجهة الرئيسية:
http://localhost:3000

لوحة التحكم:
http://localhost:3000/api/dashboard

فحص الصحة:
http://localhost:3000/health

الإشارات:
http://localhost:3000/api/signals

التحليل:
http://localhost:3000/api/analysis/EURUSD/1h

الإحصائيات:
http://localhost:3000/api/analytics

================================================

🛠️ حل المشاكل الشائعة:
=======================

❌ "npm is not recognized"
الحل: تثبيت Node.js من https://nodejs.org

❌ "Port 3000 is already in use"
الحل: PORT=3001 npm start

❌ "Cannot find module"
الحل: npm install

❌ البوت لا يعمل
الحل: node simple-bot.js

================================================

📱 التحكم في البوت:
==================

إيقاف البوت:
اضغط Ctrl + C في Terminal

إعادة التشغيل:
أوقف البوت ثم: npm start

مراقبة السجلات:
tail -f logs/combined.log

================================================

⚙️ إعدادات سريعة:
==================

تغيير المنفذ:
PORT=8080 npm start

تفعيل التشخيص:
DEBUG=ayopfx:* npm start

تفعيل التنبيهات:
1. انسخ: cp .env.example .env
2. حرر ملف .env وأضف:
   TELEGRAM_BOT_TOKEN=your_token
   EMAIL_USER=your_email

================================================

📞 الحصول على المساعدة:
========================

📧 البريد الإلكتروني: <EMAIL>
💬 Telegram: @ayopfx_support
📚 الدليل الشامل: INSTALLATION.md
🐛 إبلاغ عن مشكلة: GitHub Issues

================================================

✅ قائمة التحقق:
================

قبل التشغيل:
□ Node.js مثبت (node --version)
□ npm يعمل (npm --version)
□ التبعيات مثبتة (npm install)

بعد التشغيل:
□ البوت يعمل (لا توجد أخطاء)
□ الواجهة تفتح (http://localhost:3000)
□ API يعمل (http://localhost:3000/health)

================================================

🎯 الميزات المتاحة:
===================

✅ تحليل فني متقدم (50+ مؤشر)
✅ ذكاء اصطناعي للتنبؤ
✅ تحليل الأخبار والمشاعر
✅ إدارة المحفظة الذكية
✅ تحليل الجلسات التداولية
✅ نظام التنبيهات المتطور
✅ واجهة عربية احترافية
✅ تكامل مع منصات خارجية
✅ نظام الأمان المتقدم
✅ إعدادات النشر الاحترافية

================================================

🚀 استمتع بالتداول الذكي مع AyoPFX!

البوت جاهز للعمل مع جميع الميزات المتقدمة
تم تطبيق جميع التخصيصات المطلوبة بنجاح

مبروك! أنت الآن تملك أقوى بوت تداول ذكي! 🎉
