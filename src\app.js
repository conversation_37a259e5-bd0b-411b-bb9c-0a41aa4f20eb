import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import path from 'path';
import { fileURLToPath } from 'url';

// Import services and middleware
import logger from './utils/logger.js';
import config from './config/config.js';
import { globalErrorHandler, AppError } from './middleware/errorHandler.js';
import { generalRateLimitMiddleware } from './middleware/rateLimiter.js';
import { initializeDatabase } from './database/redis.js';

// Import services
import DataService from './services/DataService.js';
import TradingEngine from './services/TradingEngine.js';
import NotificationService from './services/NotificationService.js';
import AIService from './services/AIService.js';

// Import routes
import apiRoutes from './api/routes.js';
import webhookRoutes from './api/webhook.js';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AyoPFXTradingBot {
  constructor() {
    this.app = express();
    this.server = null;
    this.io = null;
    this.services = {};
    this.isRunning = false;
  }

  // Initialize the application
  async initialize() {
    try {
      logger.info('🚀 Initializing AyoPFX Trading Bot...');

      // Initialize database
      await this.initializeDatabase();

      // Setup Express app
      this.setupExpress();

      // Initialize services
      await this.initializeServices();

      // Setup Socket.IO
      this.setupSocketIO();

      // Setup routes
      this.setupRoutes();

      // Setup error handling
      this.setupErrorHandling();

      logger.info('✅ AyoPFX Trading Bot initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Application initialization failed:', error);
      throw error;
    }
  }

  // Initialize database
  async initializeDatabase() {
    try {
      await initializeDatabase();
      logger.info('✅ Database initialized');
    } catch (error) {
      logger.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  // Setup Express application
  setupExpress() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://fonts.googleapis.com"],
          scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com", "https://unpkg.com", "https://cdn.socket.io"],
          fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"]
        }
      }
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.server.corsOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'x-webhook-source', 'x-tradingview-signature']
    }));

    // Compression
    this.app.use(compression());

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    this.app.use(generalRateLimitMiddleware);

    // Static files
    this.app.use(express.static(path.join(__dirname, '../public')));

    // Request logging
    this.app.use((req, res, next) => {
      logger.http(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });

    logger.info('✅ Express application configured');
  }

  // Initialize all services
  async initializeServices() {
    try {
      logger.info('Initializing services...');

      // Initialize Data Service
      this.services.dataService = new DataService();
      await this.services.dataService.initialize();

      // Initialize AI Service
      this.services.aiService = new AIService();
      await this.services.aiService.initialize();

      // Initialize Notification Service
      this.services.notificationService = new NotificationService();
      await this.services.notificationService.initialize();

      // Initialize Trading Engine
      this.services.tradingEngine = new TradingEngine({
        dataService: this.services.dataService,
        aiService: this.services.aiService,
        notificationService: this.services.notificationService,
        io: null // Will be set after Socket.IO initialization
      });
      await this.services.tradingEngine.initialize();

      // Make services available to routes
      this.app.locals.dataService = this.services.dataService;
      this.app.locals.aiService = this.services.aiService;
      this.app.locals.notificationService = this.services.notificationService;
      this.app.locals.tradingEngine = this.services.tradingEngine;

      logger.info('✅ All services initialized successfully');
    } catch (error) {
      logger.error('❌ Services initialization failed:', error);
      throw error;
    }
  }

  // Setup Socket.IO
  setupSocketIO() {
    this.server = createServer(this.app);
    
    this.io = new Server(this.server, {
      cors: {
        origin: config.server.corsOrigins,
        methods: ['GET', 'POST']
      },
      transports: ['websocket', 'polling']
    });

    // Set IO instance in trading engine
    this.services.tradingEngine.io = this.io;

    // Socket.IO event handlers
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // Handle client subscription to data feeds
      socket.on('subscribe', (data) => {
        try {
          const { pairs, timeframes } = data;
          
          if (pairs && Array.isArray(pairs)) {
            pairs.forEach(pair => {
              socket.join(`data:${pair}`);
            });
          }
          
          logger.info(`Client ${socket.id} subscribed to ${pairs?.length || 0} pairs`);
        } catch (error) {
          logger.error('Socket subscription error:', error);
        }
      });

      // Handle client unsubscription
      socket.on('unsubscribe', (data) => {
        try {
          const { pairs } = data;
          
          if (pairs && Array.isArray(pairs)) {
            pairs.forEach(pair => {
              socket.leave(`data:${pair}`);
            });
          }
          
          logger.info(`Client ${socket.id} unsubscribed from ${pairs?.length || 0} pairs`);
        } catch (error) {
          logger.error('Socket unsubscription error:', error);
        }
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.error(`Socket error for client ${socket.id}:`, error);
      });
    });

    logger.info('✅ Socket.IO configured');
  }

  // Setup routes
  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0'
      });
    });

    // API routes
    this.app.use('/api', apiRoutes);

    // Webhook routes
    this.app.use('/webhook', webhookRoutes);

    // Serve main application
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });

    // Handle 404 for API routes
    this.app.all('/api/*', (req, res, next) => {
      next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
    });

    // Serve index.html for all other routes (SPA support)
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });

    logger.info('✅ Routes configured');
  }

  // Setup error handling
  setupErrorHandling() {
    // Global error handler
    this.app.use(globalErrorHandler);

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (err, promise) => {
      logger.error('Unhandled Promise Rejection:', err);
      this.gracefulShutdown('UNHANDLED_REJECTION');
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      logger.error('Uncaught Exception:', err);
      this.gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle SIGTERM
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received');
      this.gracefulShutdown('SIGTERM');
    });

    // Handle SIGINT
    process.on('SIGINT', () => {
      logger.info('SIGINT received');
      this.gracefulShutdown('SIGINT');
    });

    logger.info('✅ Error handling configured');
  }

  // Start the application
  async start() {
    try {
      if (this.isRunning) {
        logger.warn('Application is already running');
        return;
      }

      const port = config.server.port;
      
      this.server.listen(port, () => {
        logger.info(`🚀 AyoPFX Trading Bot started on port ${port}`);
        logger.info(`📊 Dashboard: http://localhost:${port}`);
        logger.info(`🔗 API: http://localhost:${port}/api`);
        logger.info(`🪝 Webhooks: http://localhost:${port}/webhook`);
        
        this.isRunning = true;
      });

      // Start trading engine
      await this.services.tradingEngine.start();

      // Send startup notification
      try {
        await this.services.notificationService.sendSystemAlert({
          type: 'system_startup',
          message: 'AyoPFX Trading Bot started successfully',
          level: 'info'
        });
      } catch (error) {
        logger.warn('Startup notification failed:', error);
      }

      logger.info('✅ AyoPFX Trading Bot is now running');

    } catch (error) {
      logger.error('❌ Application start failed:', error);
      throw error;
    }
  }

  // Graceful shutdown
  async gracefulShutdown(signal) {
    try {
      logger.info(`🛑 Graceful shutdown initiated (${signal})`);

      // Stop accepting new connections
      if (this.server) {
        this.server.close(() => {
          logger.info('HTTP server closed');
        });
      }

      // Stop trading engine
      if (this.services.tradingEngine) {
        await this.services.tradingEngine.stop();
      }

      // Cleanup services
      await this.cleanupServices();

      // Send shutdown notification
      try {
        await this.services.notificationService.sendSystemAlert({
          type: 'system_shutdown',
          message: `AyoPFX Trading Bot shutting down (${signal})`,
          level: 'warning'
        });
      } catch (error) {
        logger.warn('Shutdown notification failed:', error);
      }

      logger.info('✅ Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      logger.error('❌ Graceful shutdown failed:', error);
      process.exit(1);
    }
  }

  // Cleanup services
  async cleanupServices() {
    try {
      const cleanupPromises = [];

      if (this.services.dataService) {
        cleanupPromises.push(this.services.dataService.cleanup());
      }

      if (this.services.aiService) {
        cleanupPromises.push(this.services.aiService.cleanup());
      }

      if (this.services.notificationService) {
        cleanupPromises.push(this.services.notificationService.cleanup());
      }

      await Promise.allSettled(cleanupPromises);
      logger.info('✅ Services cleanup completed');

    } catch (error) {
      logger.error('❌ Services cleanup failed:', error);
    }
  }

  // Get application status
  getStatus() {
    return {
      isRunning: this.isRunning,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      services: {
        dataService: this.services.dataService?.isConnected || false,
        aiService: this.services.aiService?.isReady || false,
        notificationService: this.services.notificationService?.isInitialized || false,
        tradingEngine: this.services.tradingEngine?.isRunning || false
      },
      timestamp: new Date().toISOString()
    };
  }
}

// Create and export application instance
const app = new AyoPFXTradingBot();

// Initialize and start the application
async function main() {
  try {
    await app.initialize();
    await app.start();
  } catch (error) {
    logger.error('❌ Application failed to start:', error);
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default app;
