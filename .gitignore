# =================================
# AyoPFX Trading Bot .gitignore
# =================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =================================
# Trading Bot Specific
# =================================

# AI Models (trained models can be large)
models/*.json
models/*.h5
models/*.pb
models/saved_model/
models/variables/

# Trading data and backups
backups/
data/
historical_data/
*.csv
*.xlsx

# Configuration files with sensitive data
config/production.json
config/staging.json
config/local.json

# Database files
*.db
*.sqlite
*.sqlite3

# Redis dump files
dump.rdb
appendonly.aof

# PM2 files
.pm2/

# Docker files (if using secrets)
docker-compose.override.yml
docker-compose.prod.yml

# SSL certificates
*.pem
*.key
*.crt
*.p12
*.pfx

# API keys and secrets
secrets/
keys/
certificates/

# Test files
test-results/
coverage/
.nyc_output/

# Build artifacts
build/
dist/
out/

# Monitoring and metrics
metrics/
monitoring/data/

# Jupyter notebooks (if used for analysis)
.ipynb_checkpoints/
*.ipynb

# Python files (if using Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# R files (if using R for analysis)
.Rhistory
.RData
.Ruserdata

# MATLAB files
*.asv
*.m~

# Temporary analysis files
analysis_temp/
temp_charts/
temp_data/

# Performance profiling
*.prof
*.trace

# Memory dumps
*.heapsnapshot

# =================================
# IDE and Editor specific
# =================================

# Visual Studio Code
.vscode/
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =================================
# System files
# =================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =================================
# Security and sensitive files
# =================================

# Never commit these!
*.key
*.pem
*.p12
*.pfx
*.jks
*.keystore
.env*
config/secrets.json
secrets.json
private_keys/
ssl/
certs/
