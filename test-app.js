// اختبار بسيط للتطبيق
console.log('🚀 بدء تشغيل AyoPFX Trading Bot...');

try {
  // اختبار استيراد express
  const express = await import('express');
  console.log('✅ Express تم تحميله بنجاح');
  
  const app = express.default();
  const PORT = 3000;
  
  // إعداد middleware بسيط
  app.use(express.default.json());
  
  // Route بسيط
  app.get('/', (req, res) => {
    res.json({
      message: 'مرحباً بك في AyoPFX Trading Bot!',
      status: 'working',
      timestamp: new Date().toISOString()
    });
  });
  
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    });
  });
  
  // بدء الخادم
  app.listen(PORT, () => {
    console.log(`✅ الخادم يعمل على المنفذ ${PORT}`);
    console.log(`🌐 افتح المتصفح على: http://localhost:${PORT}`);
    console.log(`🏥 فحص الصحة: http://localhost:${PORT}/health`);
  });
  
} catch (error) {
  console.error('❌ خطأ في تشغيل التطبيق:', error);
  process.exit(1);
}
