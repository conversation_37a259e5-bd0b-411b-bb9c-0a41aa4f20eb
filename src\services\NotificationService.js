import TelegramBot from 'node-telegram-bot-api';
import nodemailer from 'nodemailer';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class NotificationService extends EventEmitter {
  constructor() {
    super();
    this.telegramBot = null;
    this.emailTransporter = null;
    this.isInitialized = false;
    this.notificationQueue = [];
    this.rateLimitCache = new Map();
    this.templates = this.initializeTemplates();
  }

  // Initialize notification templates
  initializeTemplates() {
    return {
      tradeSignal: {
        telegram: {
          title: '🚨 إشارة تداول جديدة',
          format: (data) => `
🚨 *إشارة تداول جديدة*

📊 *الزوج:* ${data.symbol}
📈 *الاتجاه:* ${data.direction === 'buy' ? '🟢 شراء' : '🔴 بيع'}
💰 *سعر الدخول:* ${data.entry.price}
🛑 *وقف الخسارة:* ${data.stopLoss.price}

🎯 *الأهداف:*
${data.takeProfits.map((tp, i) => `${i + 1}. ${tp.price} (${tp.percentage}%)`).join('\n')}

📊 *نسبة المخاطرة/العائد:* ${data.riskReward.toFixed(2)}
🎯 *مستوى الثقة:* ${Math.round(data.confidence * 100)}%

⏰ *الوقت:* ${new Date(data.timestamp).toLocaleString('ar-EG')}

${data.reasoning.entry}
          `
        },
        email: {
          subject: (data) => `إشارة تداول جديدة - ${data.symbol} ${data.direction.toUpperCase()}`,
          html: (data) => `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2c3e50; text-align: center;">🚨 إشارة تداول جديدة</h2>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #34495e;">تفاصيل الصفقة</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>الزوج:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd;">${data.symbol}</td></tr>
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>الاتجاه:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd; color: ${data.direction === 'buy' ? '#27ae60' : '#e74c3c'};">${data.direction === 'buy' ? '🟢 شراء' : '🔴 بيع'}</td></tr>
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>سعر الدخول:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd;">${data.entry.price}</td></tr>
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>وقف الخسارة:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd;">${data.stopLoss.price}</td></tr>
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>نسبة المخاطرة/العائد:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd;">${data.riskReward.toFixed(2)}</td></tr>
                  <tr><td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>مستوى الثقة:</strong></td><td style="padding: 8px; border-bottom: 1px solid #ddd;">${Math.round(data.confidence * 100)}%</td></tr>
                </table>
              </div>
              
              <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #27ae60; margin-top: 0;">🎯 الأهداف</h4>
                ${data.takeProfits.map((tp, i) => `<p style="margin: 5px 0;">الهدف ${i + 1}: ${tp.price} (${tp.percentage}%)</p>`).join('')}
              </div>
              
              <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #856404; margin-top: 0;">📝 سبب الإشارة</h4>
                <p>${data.reasoning.entry}</p>
              </div>
              
              <p style="text-align: center; color: #6c757d; font-size: 12px;">
                تم إنشاء هذه الإشارة بواسطة AyoPFX Trading Bot<br>
                ${new Date(data.timestamp).toLocaleString('ar-EG')}
              </p>
            </div>
          `
        }
      },
      
      priceAlert: {
        telegram: {
          title: '📊 تنبيه سعر',
          format: (data) => `
📊 *تنبيه سعر*

💱 *الزوج:* ${data.symbol}
💰 *السعر الحالي:* ${data.currentPrice}
🎯 *السعر المستهدف:* ${data.targetPrice}
📈 *التغيير:* ${data.change > 0 ? '+' : ''}${data.change.toFixed(4)} (${data.changePercent.toFixed(2)}%)

⏰ *الوقت:* ${new Date().toLocaleString('ar-EG')}
          `
        },
        email: {
          subject: (data) => `تنبيه سعر - ${data.symbol}`,
          html: (data) => `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2c3e50; text-align: center;">📊 تنبيه سعر</h2>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <p><strong>الزوج:</strong> ${data.symbol}</p>
                <p><strong>السعر الحالي:</strong> ${data.currentPrice}</p>
                <p><strong>السعر المستهدف:</strong> ${data.targetPrice}</p>
                <p><strong>التغيير:</strong> <span style="color: ${data.change > 0 ? '#27ae60' : '#e74c3c'};">${data.change > 0 ? '+' : ''}${data.change.toFixed(4)} (${data.changePercent.toFixed(2)}%)</span></p>
              </div>
            </div>
          `
        }
      },
      
      marketUpdate: {
        telegram: {
          title: '📈 تحديث السوق',
          format: (data) => `
📈 *تحديث السوق*

🌍 *الجلسة الحالية:* ${data.session}
📊 *الاتجاه العام:* ${data.trend}
💧 *السيولة:* ${data.liquidity}
⚡ *الزخم:* ${data.momentum}

📋 *الملخص:* ${data.summary}

⏰ *الوقت:* ${new Date().toLocaleString('ar-EG')}
          `
        },
        email: {
          subject: () => 'تحديث حالة السوق',
          html: (data) => `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #2c3e50; text-align: center;">📈 تحديث السوق</h2>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <p><strong>الجلسة الحالية:</strong> ${data.session}</p>
                <p><strong>الاتجاه العام:</strong> ${data.trend}</p>
                <p><strong>السيولة:</strong> ${data.liquidity}</p>
                <p><strong>الزخم:</strong> ${data.momentum}</p>
                <p><strong>الملخص:</strong> ${data.summary}</p>
              </div>
            </div>
          `
        }
      },
      
      systemAlert: {
        telegram: {
          title: '⚠️ تنبيه النظام',
          format: (data) => `
⚠️ *تنبيه النظام*

🔧 *النوع:* ${data.type}
📝 *الرسالة:* ${data.message}
⚡ *المستوى:* ${data.level}

⏰ *الوقت:* ${new Date().toLocaleString('ar-EG')}
          `
        },
        email: {
          subject: (data) => `تنبيه النظام - ${data.type}`,
          html: (data) => `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #e74c3c; text-align: center;">⚠️ تنبيه النظام</h2>
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <p><strong>النوع:</strong> ${data.type}</p>
                <p><strong>الرسالة:</strong> ${data.message}</p>
                <p><strong>المستوى:</strong> <span style="color: ${data.level === 'error' ? '#e74c3c' : data.level === 'warning' ? '#f39c12' : '#3498db'};">${data.level}</span></p>
              </div>
            </div>
          `
        }
      }
    };
  }

  // Initialize the notification service
  async initialize() {
    try {
      logger.info('Initializing Notification Service...');

      // Initialize Telegram Bot
      if (config.telegram.enabled && config.telegram.botToken) {
        await this.initializeTelegramBot();
      }

      // Initialize Email Service
      if (config.email.enabled && config.email.auth.user) {
        await this.initializeEmailService();
      }

      this.isInitialized = true;
      logger.info('✅ Notification Service initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Notification Service initialization failed:', error);
      throw error;
    }
  }

  // Initialize Telegram Bot
  async initializeTelegramBot() {
    try {
      this.telegramBot = new TelegramBot(config.telegram.botToken, { polling: false });

      // Test the bot
      const botInfo = await this.telegramBot.getMe();
      logger.info(`Telegram bot initialized: @${botInfo.username}`);

      // Set up bot commands
      await this.setupTelegramCommands();

      return true;
    } catch (error) {
      logger.error('Telegram bot initialization error:', error);
      throw error;
    }
  }

  // Initialize Email Service
  async initializeEmailService() {
    try {
      this.emailTransporter = nodemailer.createTransporter({
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure,
        auth: config.email.auth
      });

      // Test the email service
      await this.emailTransporter.verify();
      logger.info('Email service initialized successfully');

      return true;
    } catch (error) {
      logger.error('Email service initialization error:', error);
      throw error;
    }
  }

  // Setup Telegram bot commands
  async setupTelegramCommands() {
    const commands = [
      { command: 'start', description: 'بدء استخدام البوت' },
      { command: 'status', description: 'حالة النظام' },
      { command: 'signals', description: 'آخر الإشارات' },
      { command: 'market', description: 'حالة السوق' },
      { command: 'help', description: 'المساعدة' }
    ];

    await this.telegramBot.setMyCommands(commands);
    logger.info('Telegram bot commands set up');
  }

  // Send trade signal notification
  async sendTradeSignal(tradeData) {
    try {
      const notificationData = {
        type: 'tradeSignal',
        data: tradeData,
        priority: 'high',
        timestamp: new Date().toISOString()
      };

      await this.sendNotification(notificationData);

      logger.notification('Trade signal notification sent', {
        symbol: tradeData.symbol,
        direction: tradeData.direction,
        confidence: tradeData.confidence
      });

      return true;
    } catch (error) {
      logger.error('Trade signal notification error:', error);
      throw error;
    }
  }

  // Send price alert
  async sendPriceAlert(alertData) {
    try {
      // Check rate limiting
      if (await this.isRateLimited('priceAlert', alertData.symbol)) {
        logger.warn(`Price alert rate limited for ${alertData.symbol}`);
        return false;
      }

      const notificationData = {
        type: 'priceAlert',
        data: alertData,
        priority: 'medium',
        timestamp: new Date().toISOString()
      };

      await this.sendNotification(notificationData);

      // Set rate limit
      await this.setRateLimit('priceAlert', alertData.symbol, config.notifications.cooldownMinutes * 60);

      logger.notification('Price alert sent', {
        symbol: alertData.symbol,
        currentPrice: alertData.currentPrice
      });

      return true;
    } catch (error) {
      logger.error('Price alert notification error:', error);
      throw error;
    }
  }

  // Send market update
  async sendMarketUpdate(marketData) {
    try {
      // Check rate limiting
      if (await this.isRateLimited('marketUpdate', 'global')) {
        return false;
      }

      const notificationData = {
        type: 'marketUpdate',
        data: marketData,
        priority: 'low',
        timestamp: new Date().toISOString()
      };

      await this.sendNotification(notificationData);

      // Set rate limit (15 minutes for market updates)
      await this.setRateLimit('marketUpdate', 'global', 15 * 60);

      logger.notification('Market update sent');

      return true;
    } catch (error) {
      logger.error('Market update notification error:', error);
      throw error;
    }
  }

  // Send system alert
  async sendSystemAlert(alertData) {
    try {
      const notificationData = {
        type: 'systemAlert',
        data: alertData,
        priority: alertData.level === 'error' ? 'high' : 'medium',
        timestamp: new Date().toISOString()
      };

      await this.sendNotification(notificationData);

      logger.notification('System alert sent', {
        type: alertData.type,
        level: alertData.level
      });

      return true;
    } catch (error) {
      logger.error('System alert notification error:', error);
      throw error;
    }
  }

  // Send notification (main method)
  async sendNotification(notificationData) {
    try {
      const { type, data, priority } = notificationData;
      const template = this.templates[type];

      if (!template) {
        throw new Error(`Unknown notification type: ${type}`);
      }

      const promises = [];

      // Send Telegram notification
      if (this.telegramBot && config.telegram.enabled) {
        promises.push(this.sendTelegramNotification(template.telegram, data));
      }

      // Send Email notification
      if (this.emailTransporter && config.email.enabled) {
        promises.push(this.sendEmailNotification(template.email, data));
      }

      // Wait for all notifications to be sent
      const results = await Promise.allSettled(promises);

      // Log any failures
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const channel = index === 0 ? 'Telegram' : 'Email';
          logger.error(`${channel} notification failed:`, result.reason);
        }
      });

      return results.some(result => result.status === 'fulfilled');
    } catch (error) {
      logger.error('Notification sending error:', error);
      throw error;
    }
  }

  // Send Telegram notification
  async sendTelegramNotification(template, data) {
    try {
      if (!this.telegramBot || !config.telegram.chatId) {
        throw new Error('Telegram not configured');
      }

      const message = template.format(data);

      await this.telegramBot.sendMessage(config.telegram.chatId, message, {
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      });

      return true;
    } catch (error) {
      logger.error('Telegram notification error:', error);
      throw error;
    }
  }

  // Send Email notification
  async sendEmailNotification(template, data) {
    try {
      if (!this.emailTransporter) {
        throw new Error('Email not configured');
      }

      const mailOptions = {
        from: config.email.from,
        to: config.email.auth.user, // Send to the configured email
        subject: template.subject(data),
        html: template.html(data)
      };

      await this.emailTransporter.sendMail(mailOptions);

      return true;
    } catch (error) {
      logger.error('Email notification error:', error);
      throw error;
    }
  }

  // Check rate limiting
  async isRateLimited(type, key) {
    try {
      const rateLimitKey = `rate_limit_${type}_${key}`;
      const exists = await cacheUtils.exists(rateLimitKey);
      return exists;
    } catch (error) {
      logger.error('Rate limit check error:', error);
      return false;
    }
  }

  // Set rate limit
  async setRateLimit(type, key, seconds) {
    try {
      const rateLimitKey = `rate_limit_${type}_${key}`;
      await cacheUtils.set(rateLimitKey, true, seconds);
      return true;
    } catch (error) {
      logger.error('Rate limit set error:', error);
      return false;
    }
  }

  // Send bulk notifications
  async sendBulkNotifications(notifications) {
    try {
      const results = [];

      for (const notification of notifications) {
        try {
          const result = await this.sendNotification(notification);
          results.push({ success: true, notification });
        } catch (error) {
          results.push({ success: false, notification, error: error.message });
        }
      }

      logger.notification('Bulk notifications processed', {
        total: notifications.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      });

      return results;
    } catch (error) {
      logger.error('Bulk notification error:', error);
      throw error;
    }
  }

  // Get notification statistics
  async getNotificationStats() {
    try {
      // This would typically come from a database
      // For now, return basic stats
      return {
        totalSent: 0,
        telegramSent: 0,
        emailSent: 0,
        failed: 0,
        lastSent: null
      };
    } catch (error) {
      logger.error('Notification stats error:', error);
      return null;
    }
  }

  // Test notifications
  async testNotifications() {
    try {
      const testData = {
        symbol: 'EUR/USD',
        direction: 'buy',
        entry: { price: 1.1000 },
        stopLoss: { price: 1.0950 },
        takeProfits: [
          { price: 1.1050, percentage: 50 },
          { price: 1.1100, percentage: 30 },
          { price: 1.1150, percentage: 20 }
        ],
        riskReward: 2.0,
        confidence: 0.85,
        reasoning: { entry: 'Test signal for system verification' },
        timestamp: new Date().toISOString()
      };

      await this.sendTradeSignal(testData);

      logger.info('Test notifications sent successfully');
      return true;
    } catch (error) {
      logger.error('Test notification error:', error);
      throw error;
    }
  }

  // Cleanup
  async cleanup() {
    try {
      if (this.telegramBot) {
        await this.telegramBot.stopPolling();
      }

      if (this.emailTransporter) {
        this.emailTransporter.close();
      }

      this.isInitialized = false;
      logger.info('Notification Service cleaned up');
    } catch (error) {
      logger.error('Notification Service cleanup error:', error);
    }
  }
}

export default NotificationService;
