// نسخة مبسطة من بوت التداول للاختبار
import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0'
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    status: 'success',
    data: {
      isRunning: true,
      services: {
        dataService: true,
        aiService: true,
        notificationService: true,
        tradingEngine: true
      },
      timestamp: new Date().toISOString()
    }
  });
});

app.get('/api/signals', (req, res) => {
  // Mock signals data
  const mockSignals = [
    {
      metadata: {
        symbol: 'EURUSD',
        timeframe: '1h',
        timestamp: new Date().toISOString()
      },
      direction: 'buy',
      entry: { price: 1.1000 },
      stopLoss: { price: 1.0950 },
      takeProfits: [
        { price: 1.1050, percentage: 50 },
        { price: 1.1100, percentage: 30 }
      ],
      confidence: 0.85,
      riskReward: 2.0
    }
  ];

  res.json({
    status: 'success',
    data: {
      signals: mockSignals,
      count: mockSignals.length
    }
  });
});

app.get('/api/analysis/:symbol/:timeframe', (req, res) => {
  const { symbol, timeframe } = req.params;
  
  // Mock analysis data
  const mockAnalysis = {
    symbol,
    timeframe,
    sentiment: {
      overall: 'bullish',
      strength: 0.75
    },
    confluence: {
      score: 0.8,
      level: 'high'
    },
    technical: {
      rsi: {
        current: 65
      }
    },
    timestamp: new Date().toISOString()
  };

  res.json({
    status: 'success',
    data: mockAnalysis
  });
});

app.get('/api/status/performance', (req, res) => {
  res.json({
    status: 'success',
    data: {
      totalSignals: 45,
      successfulSignals: 35,
      failedSignals: 10,
      successRate: 77.8,
      averageConfidence: 0.78,
      activeSignals: 3
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    status: 'error',
    message: 'Something went wrong!'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'Route not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 AyoPFX Trading Bot (Simple Mode) started on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:${PORT}`);
  console.log(`🔗 API: http://localhost:${PORT}/api`);
  console.log(`✅ Server is running successfully!`);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
