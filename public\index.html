<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AyoPFX Trading Bot - بوت التداول الاحترافي</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- TradingView Charting Library -->
    <script src="https://unpkg.com/tradingview-charting-library@latest/bundles/library.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment@1.0.1/dist/chartjs-adapter-moment.min.js"></script>
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --dark-color: #343a40;
            --light-color: #f8f9fa;
            --border-color: #dee2e6;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-weight: 700;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header .subtitle {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            left: 30px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .nav-tabs {
            background: var(--light-color);
            border-bottom: 3px solid var(--primary-color);
        }

        .nav-tabs .nav-link {
            color: var(--dark-color);
            font-weight: 600;
            border: none;
            padding: 15px 25px;
            margin: 0 5px;
            border-radius: 10px 10px 0 0;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .nav-tabs .nav-link:hover {
            background: var(--secondary-color);
            color: white;
        }

        .tab-content {
            padding: 30px;
            min-height: 600px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            padding: 15px 20px;
            border: none;
        }

        .signal-card {
            border-left: 5px solid var(--success-color);
        }

        .signal-card.sell {
            border-left-color: var(--danger-color);
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            padding: 20px;
        }

        .price-change.positive {
            color: var(--success-color);
        }

        .price-change.negative {
            color: var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
            gap: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .metric-card {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .metric-label {
            color: var(--dark-color);
            font-weight: 600;
            margin-top: 5px;
        }

        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin-bottom: 15px;
        }

        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .countdown {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--warning-color);
            text-align: center;
            padding: 10px;
            background: rgba(243, 156, 18, 0.1);
            border-radius: 10px;
            margin: 10px 0;
        }

        .market-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: var(--light-color);
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .session-indicator {
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .session-london { background: #3498db; color: white; }
        .session-newyork { background: #e74c3c; color: white; }
        .session-tokyo { background: #f39c12; color: white; }
        .session-sydney { background: #27ae60; color: white; }

        .footer {
            background: var(--dark-color);
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .status-indicator {
                position: static;
                justify-content: center;
                margin-top: 15px;
            }
            
            .nav-tabs .nav-link {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
            
            .tab-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">متصل</span>
            </div>
            <h1><i class="fas fa-robot"></i> AyoPFX Trading Bot</h1>
            <p class="subtitle">بوت التداول الاحترافي المدعوم بالذكاء الاصطناعي</p>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="signals-tab" data-bs-toggle="tab" data-bs-target="#signals" type="button" role="tab">
                    <i class="fas fa-signal"></i> الإشارات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                    <i class="fas fa-chart-line"></i> التحليل
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="market-tab" data-bs-toggle="tab" data-bs-target="#market" type="button" role="tab">
                    <i class="fas fa-globe"></i> السوق
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                    <i class="fas fa-cog"></i> الإعدادات
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="mainTabContent">
            <!-- Dashboard Tab -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                <div class="row">
                    <!-- System Status -->
                    <div class="col-md-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="totalSignals">0</div>
                            <div class="metric-label">إجمالي الإشارات</div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="successRate">0%</div>
                            <div class="metric-label">معدل النجاح</div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="activeSignals">0</div>
                            <div class="metric-label">الإشارات النشطة</div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="avgConfidence">0%</div>
                            <div class="metric-label">متوسط الثقة</div>
                        </div>
                    </div>
                </div>

                <!-- Market Status -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-clock"></i> حالة السوق الحالية
                    </div>
                    <div class="card-body">
                        <div class="market-status">
                            <div>
                                <strong>الجلسة الحالية:</strong>
                                <span class="session-indicator" id="currentSession">تحديد...</span>
                            </div>
                            <div>
                                <strong>الوقت:</strong>
                                <span id="currentTime"></span>
                            </div>
                        </div>
                        <div id="marketOverview" class="loading">
                            <div class="spinner"></div>
                            <span>جاري تحميل بيانات السوق...</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Signals -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history"></i> آخر الإشارات
                    </div>
                    <div class="card-body">
                        <div id="recentSignals" class="loading">
                            <div class="spinner"></div>
                            <span>جاري تحميل الإشارات...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Signals Tab -->
            <div class="tab-pane fade" id="signals" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-signal"></i> الإشارات النشطة
                            </div>
                            <div class="card-body">
                                <div id="activeSignalsList" class="loading">
                                    <div class="spinner"></div>
                                    <span>جاري تحميل الإشارات النشطة...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-bell"></i> التنبيهات القادمة
                            </div>
                            <div class="card-body">
                                <div id="upcomingAlerts">
                                    <div class="alert alert-info alert-custom">
                                        <i class="fas fa-info-circle"></i>
                                        لا توجد تنبيهات قادمة حالياً
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div class="tab-pane fade" id="analysis" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-chart-line"></i> الرسم البياني المباشر
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <select class="form-select" id="symbolSelect">
                                        <option value="EURUSD">EUR/USD</option>
                                        <option value="GBPUSD">GBP/USD</option>
                                        <option value="USDJPY">USD/JPY</option>
                                        <option value="AUDUSD">AUD/USD</option>
                                        <option value="USDCAD">USD/CAD</option>
                                        <option value="USDCHF">USD/CHF</option>
                                        <option value="NZDUSD">NZD/USD</option>
                                        <option value="XAUUSD">XAU/USD</option>
                                    </select>
                                </div>
                                <div class="chart-container">
                                    <canvas id="priceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-brain"></i> تحليل الذكاء الاصطناعي
                            </div>
                            <div class="card-body">
                                <div id="aiAnalysis" class="loading">
                                    <div class="spinner"></div>
                                    <span>جاري تحليل البيانات...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Market Tab -->
            <div class="tab-pane fade" id="market" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-coins"></i> أسعار العملات المباشرة
                            </div>
                            <div class="card-body">
                                <div id="livePrices" class="loading">
                                    <div class="spinner"></div>
                                    <span>جاري تحميل الأسعار...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-newspaper"></i> أخبار السوق
                            </div>
                            <div class="card-body">
                                <div id="marketNews">
                                    <div class="alert alert-info alert-custom">
                                        <i class="fas fa-info-circle"></i>
                                        سيتم إضافة أخبار السوق قريباً
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-shield-alt"></i> إدارة المخاطر
                            </div>
                            <div class="card-body">
                                <form id="riskSettings">
                                    <div class="mb-3">
                                        <label class="form-label">نسبة المخاطرة (%)</label>
                                        <input type="number" class="form-control" id="riskPercentage" value="2" min="0.1" max="10" step="0.1">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">نسبة المخاطرة/العائد الدنيا</label>
                                        <input type="number" class="form-control" id="minRiskReward" value="1.5" min="1" max="5" step="0.1">
                                    </div>
                                    <button type="submit" class="btn btn-primary-custom btn-custom">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-bell"></i> إعدادات التنبيهات
                            </div>
                            <div class="card-body">
                                <form id="notificationSettings">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="telegramNotifications" checked>
                                            <label class="form-check-label" for="telegramNotifications">
                                                تنبيهات Telegram
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                            <label class="form-check-label" for="emailNotifications">
                                                تنبيهات البريد الإلكتروني
                                            </label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">فترة التهدئة (دقائق)</label>
                                        <input type="number" class="form-control" id="cooldownMinutes" value="5" min="1" max="60">
                                    </div>
                                    <button type="submit" class="btn btn-primary-custom btn-custom">
                                        <i class="fas fa-save"></i> حفظ الإعدادات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 AyoPFX Trading Bot. جميع الحقوق محفوظة.</p>
            <p>تم التطوير بواسطة الذكاء الاصطناعي المتقدم</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/js/app.js"></script>
</body>
</html>
