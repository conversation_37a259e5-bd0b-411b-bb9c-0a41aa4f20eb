{"name": "ayopfx-trading-bot", "version": "1.0.0", "description": "Professional AI-powered trading bot with advanced technical analysis", "main": "simple-server.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.js", "build": "echo 'No build step required for Node.js application'", "docker:build": "docker build -t ayopfx-trading-bot .", "docker:run": "docker run -p 3000:3000 ayopfx-trading-bot", "setup": "npm install && npm run setup:config", "setup:config": "node scripts/setup-config.js", "backup": "node scripts/backup.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "logs": "tail -f logs/app.log", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ayopfx-trading-bot", "pm2:restart": "pm2 restart ayopfx-trading-bot", "pm2:logs": "pm2 logs ayopfx-trading-bot"}, "keywords": ["trading", "forex", "ai", "technical-analysis", "tradingview", "cryptocurrency", "bot"], "author": "AyoPFX", "license": "MIT", "dependencies": {"axios": "^1.5.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "node-telegram-bot-api": "^0.63.0", "nodemailer": "^6.9.4", "simple-statistics": "^7.8.3", "socket.io": "^4.7.2", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.13.0"}, "devDependencies": {"@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "prettier": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js"]}, "eslintConfig": {"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100}, "nodemonConfig": {"watch": ["src"], "ext": "js,json", "ignore": ["src/logs/*", "src/models/*", "node_modules"], "env": {"NODE_ENV": "development"}}}