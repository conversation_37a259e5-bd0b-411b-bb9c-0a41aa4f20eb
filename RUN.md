# 🚀 تشغيل البوت - دليل سريع

## ⚡ التشغيل السريع (30 ثانية)

### على Windows:
```cmd
# انقر مرتين على الملف:
start-bot.bat

# أو افتح Command Prompt واكتب:
npm start
```

### على Mac/Linux:
```bash
# في Terminal:
./start-bot.sh

# أو:
npm start
```

---

## 🎯 الطرق المختلفة للتشغيل

### 1️⃣ الطريقة الأسهل (للمبتدئين)
```bash
# فقط اكتب هذا:
npm start
```

### 2️⃣ التشغيل المباشر
```bash
# تشغيل البوت البسيط:
node simple-bot.js

# أو البوت المتقدم:
node src/app.js
```

### 3️⃣ التشغيل مع مراقبة الملفات (للتطوير)
```bash
npm run dev
```

### 4️⃣ التشغيل الاحترافي (للإنتاج)
```bash
# تثبيت PM2
npm install -g pm2

# تشغيل البوت
pm2 start ecosystem.config.js

# مراقبة الحالة
pm2 status
```

---

## 🌐 الوصول للبوت

بعد التشغيل، افتح المتصفح على:

### 🏠 الروابط الأساسية:
- **الصفحة الرئيسية**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3000/api/dashboard
- **فحص الصحة**: http://localhost:3000/health

### 📊 روابط API:
- **الإشارات**: http://localhost:3000/api/signals
- **التحليل**: http://localhost:3000/api/analysis/EURUSD/1h
- **الإحصائيات**: http://localhost:3000/api/analytics

---

## 🛠️ حل المشاكل السريع

### ❌ "npm is not recognized"
**الحل:** تثبيت Node.js من https://nodejs.org

### ❌ "Port 3000 is already in use"
**الحل:**
```bash
# Windows:
set PORT=3001 && npm start

# Mac/Linux:
PORT=3001 npm start
```

### ❌ "Cannot find module"
**الحل:**
```bash
npm install
```

### ❌ البوت لا يعمل
**الحل:**
```bash
# تحقق من الأخطاء:
npm run check

# أو شغل البوت البسيط:
node simple-bot.js
```

---

## 📱 التحكم في البوت

### إيقاف البوت:
- **اضغط**: `Ctrl + C` في Terminal/Command Prompt
- **أو أغلق**: نافذة Terminal

### إعادة تشغيل البوت:
```bash
# أوقف البوت أولاً (Ctrl+C)
# ثم شغله مرة أخرى:
npm start
```

### تشغيل في الخلفية:
```bash
# Windows:
start /B npm start

# Mac/Linux:
nohup npm start &
```

---

## 🔧 إعدادات سريعة

### تغيير المنفذ:
```bash
# Windows:
set PORT=8080 && npm start

# Mac/Linux:
PORT=8080 npm start
```

### تفعيل وضع التطوير:
```bash
export NODE_ENV=development
npm start
```

### تفعيل السجلات المفصلة:
```bash
export DEBUG=ayopfx:*
npm start
```

---

## 📋 قائمة تحقق سريعة

- [ ] ✅ Node.js مثبت (node --version)
- [ ] ✅ npm يعمل (npm --version)
- [ ] ✅ التبعيات مثبتة (npm install)
- [ ] ✅ البوت يعمل (npm start)
- [ ] ✅ المتصفح يفتح (http://localhost:3000)
- [ ] ✅ الواجهة تظهر بشكل صحيح

---

## 🆘 تحتاج مساعدة؟

### 📞 الدعم السريع:
1. **تحقق من**: [استكشاف الأخطاء](INSTALLATION.md#️-استكشاف-الأخطاء-وحلها)
2. **اقرأ**: [الدليل الشامل](INSTALLATION.md)
3. **انضم**: [مجموعة Telegram](https://t.me/ayopfx_support)
4. **راسلنا**: <EMAIL>

### 🔍 فحص سريع للمشاكل:
```bash
# فحص إصدار Node.js
node --version

# فحص إصدار npm
npm --version

# فحص الملفات
ls -la

# فحص المنافذ المستخدمة
netstat -an | findstr :3000
```

---

<div align="center">

## 🎉 البوت جاهز للعمل!

![Status](https://img.shields.io/badge/Status-Running-success?style=for-the-badge&logo=check-circle)

**استمتع بالتداول الذكي مع AyoPFX! 🚀**

</div>
