import TechnicalIndicators from './TechnicalIndicators.js';
import AdvancedIndicators from './AdvancedIndicators.js';
import VolumeProfile from './VolumeProfile.js';
import logger from '../utils/logger.js';

class IndicatorEngine {
  constructor() {
    this.technicalIndicators = new TechnicalIndicators();
    this.advancedIndicators = new AdvancedIndicators();
    this.volumeProfile = new VolumeProfile();
    this.cache = new Map();
  }

  // Comprehensive analysis of all indicators
  async analyzeMarket(marketData, symbol, timeframe) {
    try {
      const { opens, highs, lows, closes, volumes, timestamps } = marketData;
      
      if (!this.validateMarketData(marketData)) {
        throw new Error('Invalid market data provided');
      }

      logger.analysis(`Starting comprehensive analysis for ${symbol} ${timeframe}`, {
        symbol,
        timeframe,
        dataPoints: closes.length
      });

      // Calculate all technical indicators
      const technicalAnalysis = await this.calculateTechnicalIndicators(closes, highs, lows, volumes);
      
      // Calculate advanced indicators
      const advancedAnalysis = await this.calculateAdvancedIndicators(opens, highs, lows, closes, volumes);
      
      // Calculate volume profile
      const volumeAnalysis = await this.calculateVolumeAnalysis(highs, lows, closes, volumes);
      
      // Determine overall market sentiment
      const marketSentiment = this.determineMarketSentiment(technicalAnalysis, advancedAnalysis, volumeAnalysis);
      
      // Generate trading signals
      const tradingSignals = this.generateTradingSignals(technicalAnalysis, advancedAnalysis, volumeAnalysis, marketSentiment);
      
      // Calculate confluence score
      const confluenceScore = this.calculateConfluenceScore(tradingSignals);

      const analysis = {
        symbol,
        timeframe,
        timestamp: new Date().toISOString(),
        technical: technicalAnalysis,
        advanced: advancedAnalysis,
        volume: volumeAnalysis,
        sentiment: marketSentiment,
        signals: tradingSignals,
        confluence: confluenceScore,
        summary: this.generateAnalysisSummary(marketSentiment, tradingSignals, confluenceScore)
      };

      // Cache the analysis
      this.cacheAnalysis(symbol, timeframe, analysis);

      logger.analysis(`Analysis completed for ${symbol} ${timeframe}`, {
        sentiment: marketSentiment.overall,
        signals: tradingSignals.length,
        confluence: confluenceScore.score
      });

      return analysis;
    } catch (error) {
      logger.error('Market analysis error:', error);
      throw error;
    }
  }

  // Calculate all technical indicators
  async calculateTechnicalIndicators(closes, highs, lows, volumes) {
    try {
      const indicators = {};

      // Moving Averages
      indicators.ema20 = this.technicalIndicators.calculateEMA(closes, 20);
      indicators.ema50 = this.technicalIndicators.calculateEMA(closes, 50);
      indicators.ema200 = this.technicalIndicators.calculateEMA(closes, 200);

      // Momentum Indicators
      indicators.rsi = this.technicalIndicators.calculateRSI(closes, 14);
      indicators.macd = this.technicalIndicators.calculateMACD(closes);

      // Volatility Indicators
      indicators.atr = this.technicalIndicators.calculateATR(highs, lows, closes);
      indicators.supertrend = this.technicalIndicators.calculateSupertrend(highs, lows, closes);

      // Volume-based indicators
      if (volumes && volumes.length > 0) {
        indicators.vwap = this.technicalIndicators.calculateVWAP(highs, lows, closes, volumes);
      }

      return indicators;
    } catch (error) {
      logger.error('Technical indicators calculation error:', error);
      return {};
    }
  }

  // Calculate advanced indicators
  async calculateAdvancedIndicators(opens, highs, lows, closes, volumes) {
    try {
      const indicators = {};

      // Market Structure
      indicators.fvg = this.advancedIndicators.detectFairValueGaps(highs, lows, closes);
      indicators.choch = this.advancedIndicators.detectChangeOfCharacter(highs, lows, closes);
      indicators.bos = this.advancedIndicators.detectBreakOfStructure(highs, lows, closes);

      // Support and Resistance
      indicators.supportResistance = this.advancedIndicators.identifySupportResistance(highs, lows, closes);

      // Order Flow
      if (opens && opens.length > 0) {
        indicators.orderBlocks = this.advancedIndicators.detectOrderBlocks(opens, highs, lows, closes, volumes);
      }

      return indicators;
    } catch (error) {
      logger.error('Advanced indicators calculation error:', error);
      return {};
    }
  }

  // Calculate volume analysis
  async calculateVolumeAnalysis(highs, lows, closes, volumes) {
    try {
      if (!volumes || volumes.length === 0) {
        return { available: false, message: 'Volume data not available' };
      }

      const volumeProfile = this.volumeProfile.calculateVolumeProfile(highs, lows, closes, volumes);
      const volumeDistribution = this.volumeProfile.analyzeVolumeDistribution(volumeProfile);

      return {
        available: true,
        profile: volumeProfile,
        distribution: volumeDistribution
      };
    } catch (error) {
      logger.error('Volume analysis error:', error);
      return { available: false, error: error.message };
    }
  }

  // Determine overall market sentiment
  determineMarketSentiment(technical, advanced, volume) {
    const sentiments = [];

    // Technical sentiment
    if (technical.rsi) {
      if (technical.rsi.current > 70) sentiments.push({ type: 'bearish', weight: 0.3, reason: 'RSI overbought' });
      else if (technical.rsi.current < 30) sentiments.push({ type: 'bullish', weight: 0.3, reason: 'RSI oversold' });
    }

    if (technical.macd) {
      if (technical.macd.signal === 'strong_bullish') sentiments.push({ type: 'bullish', weight: 0.4, reason: 'MACD bullish' });
      else if (technical.macd.signal === 'strong_bearish') sentiments.push({ type: 'bearish', weight: 0.4, reason: 'MACD bearish' });
    }

    if (technical.supertrend) {
      sentiments.push({ 
        type: technical.supertrend.bullish ? 'bullish' : 'bearish', 
        weight: 0.3, 
        reason: `Supertrend ${technical.supertrend.signal}` 
      });
    }

    // Advanced sentiment
    if (advanced.choch && advanced.choch.lastSignal) {
      sentiments.push({ 
        type: advanced.choch.lastSignal.type, 
        weight: 0.5, 
        reason: 'Change of Character detected' 
      });
    }

    if (advanced.bos && advanced.bos.lastSignal) {
      sentiments.push({ 
        type: advanced.bos.lastSignal.type, 
        weight: 0.3, 
        reason: 'Break of Structure detected' 
      });
    }

    // Volume sentiment
    if (volume.available && volume.profile) {
      const imbalance = volume.profile.volumeImbalance;
      if (Math.abs(imbalance.imbalance) > 15) {
        sentiments.push({ 
          type: imbalance.dominantSide === 'buy' ? 'bullish' : 'bearish', 
          weight: 0.2, 
          reason: `Volume ${imbalance.dominantSide} dominance` 
        });
      }
    }

    // Calculate weighted sentiment
    const bullishWeight = sentiments.filter(s => s.type === 'bullish').reduce((sum, s) => sum + s.weight, 0);
    const bearishWeight = sentiments.filter(s => s.type === 'bearish').reduce((sum, s) => sum + s.weight, 0);
    const totalWeight = bullishWeight + bearishWeight;

    let overall = 'neutral';
    let strength = 0;

    if (totalWeight > 0) {
      const bullishRatio = bullishWeight / totalWeight;
      if (bullishRatio > 0.6) {
        overall = 'bullish';
        strength = bullishRatio;
      } else if (bullishRatio < 0.4) {
        overall = 'bearish';
        strength = 1 - bullishRatio;
      }
    }

    return {
      overall,
      strength,
      bullishWeight,
      bearishWeight,
      factors: sentiments,
      confidence: Math.min(totalWeight / 2, 1) // Normalize confidence
    };
  }

  // Generate trading signals
  generateTradingSignals(technical, advanced, volume, sentiment) {
    const signals = [];

    // RSI signals
    if (technical.rsi) {
      if (technical.rsi.oversold && sentiment.overall === 'bullish') {
        signals.push({
          type: 'buy',
          indicator: 'RSI',
          strength: 0.7,
          reason: 'RSI oversold in bullish sentiment',
          entry: technical.rsi.current,
          confidence: 0.6
        });
      }
      if (technical.rsi.overbought && sentiment.overall === 'bearish') {
        signals.push({
          type: 'sell',
          indicator: 'RSI',
          strength: 0.7,
          reason: 'RSI overbought in bearish sentiment',
          entry: technical.rsi.current,
          confidence: 0.6
        });
      }
    }

    // MACD signals
    if (technical.macd) {
      if (technical.macd.bullishCrossover) {
        signals.push({
          type: 'buy',
          indicator: 'MACD',
          strength: 0.8,
          reason: 'MACD bullish crossover',
          entry: technical.macd.current.MACD,
          confidence: 0.7
        });
      }
      if (technical.macd.bearishCrossover) {
        signals.push({
          type: 'sell',
          indicator: 'MACD',
          strength: 0.8,
          reason: 'MACD bearish crossover',
          entry: technical.macd.current.MACD,
          confidence: 0.7
        });
      }
    }

    // Supertrend signals
    if (technical.supertrend && technical.supertrend.trendChange) {
      signals.push({
        type: technical.supertrend.bullish ? 'buy' : 'sell',
        indicator: 'Supertrend',
        strength: 0.9,
        reason: 'Supertrend direction change',
        entry: technical.supertrend.current,
        confidence: 0.8
      });
    }

    // Advanced signals
    if (advanced.choch && advanced.choch.lastSignal) {
      signals.push({
        type: advanced.choch.lastSignal.type === 'bullish' ? 'buy' : 'sell',
        indicator: 'CHoCH',
        strength: 0.9,
        reason: 'Change of Character detected',
        entry: advanced.choch.lastSignal.price,
        confidence: 0.8
      });
    }

    // Volume-based signals
    if (volume.available && volume.profile) {
      const currentPrice = volume.profile.bins[volume.profile.bins.length - 1]?.priceLevel;
      if (currentPrice) {
        // POC retest signal
        const pocDistance = Math.abs(currentPrice - volume.profile.poc.priceLevel);
        const avgRange = volume.profile.valueAreaHigh - volume.profile.valueAreaLow;
        
        if (pocDistance < avgRange * 0.1) {
          signals.push({
            type: volume.profile.volumeImbalance.dominantSide === 'buy' ? 'buy' : 'sell',
            indicator: 'Volume Profile',
            strength: 0.6,
            reason: 'Price near POC with volume imbalance',
            entry: volume.profile.poc.priceLevel,
            confidence: 0.5
          });
        }
      }
    }

    return signals;
  }

  // Calculate confluence score
  calculateConfluenceScore(signals) {
    if (signals.length === 0) {
      return { score: 0, level: 'none', signals: 0 };
    }

    const buySignals = signals.filter(s => s.type === 'buy');
    const sellSignals = signals.filter(s => s.type === 'sell');

    const buyStrength = buySignals.reduce((sum, s) => sum + (s.strength * s.confidence), 0);
    const sellStrength = sellSignals.reduce((sum, s) => sum + (s.strength * s.confidence), 0);

    const totalStrength = buyStrength + sellStrength;
    const dominantSide = buyStrength > sellStrength ? 'buy' : 'sell';
    const dominantStrength = Math.max(buyStrength, sellStrength);

    const score = totalStrength > 0 ? (dominantStrength / totalStrength) * Math.min(signals.length / 3, 1) : 0;

    let level = 'low';
    if (score > 0.7) level = 'high';
    else if (score > 0.4) level = 'medium';

    return {
      score: Math.round(score * 100) / 100,
      level,
      direction: dominantSide,
      signals: signals.length,
      buySignals: buySignals.length,
      sellSignals: sellSignals.length,
      buyStrength,
      sellStrength
    };
  }

  // Generate analysis summary
  generateAnalysisSummary(sentiment, signals, confluence) {
    const summary = {
      recommendation: 'hold',
      confidence: 'low',
      keyPoints: [],
      risks: [],
      opportunities: []
    };

    // Determine recommendation
    if (confluence.score > 0.6 && sentiment.strength > 0.6) {
      summary.recommendation = confluence.direction;
      summary.confidence = 'high';
    } else if (confluence.score > 0.4 || sentiment.strength > 0.4) {
      summary.recommendation = confluence.direction || sentiment.overall;
      summary.confidence = 'medium';
    }

    // Key points
    if (sentiment.overall !== 'neutral') {
      summary.keyPoints.push(`Market sentiment is ${sentiment.overall} with ${Math.round(sentiment.strength * 100)}% strength`);
    }

    if (confluence.signals > 0) {
      summary.keyPoints.push(`${confluence.signals} trading signals detected with ${confluence.level} confluence`);
    }

    // Risks and opportunities
    if (sentiment.strength < 0.3) {
      summary.risks.push('Low conviction market sentiment');
    }

    if (confluence.score < 0.3) {
      summary.risks.push('Low signal confluence - conflicting indicators');
    }

    if (confluence.score > 0.7) {
      summary.opportunities.push('High confluence trading opportunity');
    }

    return summary;
  }

  // Validate market data
  validateMarketData(data) {
    const required = ['highs', 'lows', 'closes'];
    return required.every(field => data[field] && Array.isArray(data[field]) && data[field].length > 0);
  }

  // Cache analysis
  cacheAnalysis(symbol, timeframe, analysis) {
    const key = `${symbol}_${timeframe}`;
    this.cache.set(key, {
      analysis,
      timestamp: Date.now()
    });

    // Clean old cache entries (keep last 100)
    if (this.cache.size > 100) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  // Get cached analysis
  getCachedAnalysis(symbol, timeframe, maxAge = 300000) { // 5 minutes default
    const key = `${symbol}_${timeframe}`;
    const cached = this.cache.get(key);
    
    if (cached && (Date.now() - cached.timestamp) < maxAge) {
      return cached.analysis;
    }
    
    return null;
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.technicalIndicators.clearCache();
    this.advancedIndicators.clearCache();
    this.volumeProfile.clearCache();
  }
}

export default IndicatorEngine;
