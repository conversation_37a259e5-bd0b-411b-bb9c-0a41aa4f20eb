import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class AdvancedSecurity extends EventEmitter {
  constructor() {
    super();
    this.sessions = new Map();
    this.blockedIPs = new Set();
    this.suspiciousActivities = [];
    this.encryptionKey = process.env.ENCRYPTION_KEY || crypto.randomBytes(32);
    this.jwtSecret = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
  }

  // تهيئة نظام الأمان
  initialize(app) {
    try {
      logger.info('Initializing Advanced Security System...');

      // إعداد Helmet للحماية الأساسية
      this.setupHelmet(app);

      // إعداد Rate Limiting المتقدم
      this.setupAdvancedRateLimit(app);

      // إعداد مراقبة الأنشطة المشبوهة
      this.setupSuspiciousActivityMonitoring(app);

      // إعداد التشفير والمصادقة
      this.setupEncryptionAndAuth();

      // إعداد حماية CSRF
      this.setupCSRFProtection(app);

      // إعداد مراقبة الجلسات
      this.setupSessionMonitoring();

      logger.info('✅ Advanced Security System initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Advanced Security System initialization failed:', error);
      throw error;
    }
  }

  // إعداد Helmet
  setupHelmet(app) {
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
          scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"],
          fontSrc: ["'self'", "https://cdnjs.cloudflare.com"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }));
  }

  // إعداد Rate Limiting المتقدم
  setupAdvancedRateLimit(app) {
    // Rate limit عام
    const generalLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 دقيقة
      max: 1000, // 1000 طلب لكل IP
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        this.logSuspiciousActivity(req.ip, 'RATE_LIMIT_EXCEEDED', {
          endpoint: req.path,
          userAgent: req.get('User-Agent')
        });
        res.status(429).json({
          error: 'Rate limit exceeded',
          retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
      }
    });

    // Rate limit للAPI
    const apiLimiter = rateLimit({
      windowMs: 1 * 60 * 1000, // دقيقة واحدة
      max: 100, // 100 طلب API لكل دقيقة
      message: {
        error: 'API rate limit exceeded',
        retryAfter: '1 minute'
      }
    });

    // Rate limit لتسجيل الدخول
    const loginLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 دقيقة
      max: 5, // 5 محاولات تسجيل دخول
      skipSuccessfulRequests: true,
      handler: (req, res) => {
        this.logSuspiciousActivity(req.ip, 'LOGIN_ATTEMPTS_EXCEEDED', {
          username: req.body.username,
          userAgent: req.get('User-Agent')
        });
        res.status(429).json({
          error: 'Too many login attempts, please try again later'
        });
      }
    });

    app.use(generalLimiter);
    app.use('/api', apiLimiter);
    app.use('/auth/login', loginLimiter);
  }

  // إعداد مراقبة الأنشطة المشبوهة
  setupSuspiciousActivityMonitoring(app) {
    app.use((req, res, next) => {
      // فحص IP المحظورة
      if (this.blockedIPs.has(req.ip)) {
        this.logSuspiciousActivity(req.ip, 'BLOCKED_IP_ACCESS', {
          endpoint: req.path,
          userAgent: req.get('User-Agent')
        });
        return res.status(403).json({ error: 'Access denied' });
      }

      // فحص User-Agent المشبوه
      if (this.isSuspiciousUserAgent(req.get('User-Agent'))) {
        this.logSuspiciousActivity(req.ip, 'SUSPICIOUS_USER_AGENT', {
          userAgent: req.get('User-Agent'),
          endpoint: req.path
        });
      }

      // فحص محاولات SQL Injection
      if (this.detectSQLInjection(req)) {
        this.logSuspiciousActivity(req.ip, 'SQL_INJECTION_ATTEMPT', {
          endpoint: req.path,
          query: req.query,
          body: req.body
        });
        return res.status(400).json({ error: 'Invalid request' });
      }

      // فحص محاولات XSS
      if (this.detectXSS(req)) {
        this.logSuspiciousActivity(req.ip, 'XSS_ATTEMPT', {
          endpoint: req.path,
          query: req.query,
          body: req.body
        });
        return res.status(400).json({ error: 'Invalid request' });
      }

      next();
    });
  }

  // فحص User-Agent المشبوه
  isSuspiciousUserAgent(userAgent) {
    if (!userAgent) return true;
    
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /curl/i,
      /wget/i,
      /python/i,
      /java/i,
      /perl/i,
      /php/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  // كشف محاولات SQL Injection
  detectSQLInjection(req) {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(\'|\"|;|--|\*|\|)/,
      /(\bSCRIPT\b)/i
    ];

    const checkString = JSON.stringify(req.query) + JSON.stringify(req.body);
    return sqlPatterns.some(pattern => pattern.test(checkString));
  }

  // كشف محاولات XSS
  detectXSS(req) {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<img[^>]+src[^>]*>/gi
    ];

    const checkString = JSON.stringify(req.query) + JSON.stringify(req.body);
    return xssPatterns.some(pattern => pattern.test(checkString));
  }

  // تسجيل النشاط المشبوه
  logSuspiciousActivity(ip, type, details = {}) {
    const activity = {
      ip,
      type,
      details,
      timestamp: new Date().toISOString(),
      id: crypto.randomUUID()
    };

    this.suspiciousActivities.push(activity);
    
    // الاحتفاظ بآخر 1000 نشاط فقط
    if (this.suspiciousActivities.length > 1000) {
      this.suspiciousActivities = this.suspiciousActivities.slice(-1000);
    }

    logger.warn('Suspicious activity detected', activity);

    // حظر IP بعد عدة أنشطة مشبوهة
    const recentActivities = this.suspiciousActivities.filter(
      a => a.ip === ip && Date.now() - new Date(a.timestamp).getTime() < 3600000 // آخر ساعة
    );

    if (recentActivities.length >= 5) {
      this.blockIP(ip, 'Multiple suspicious activities');
    }

    this.emit('suspiciousActivity', activity);
  }

  // حظر IP
  blockIP(ip, reason) {
    this.blockedIPs.add(ip);
    logger.warn(`IP blocked: ${ip}`, { reason });
    
    // إزالة الحظر بعد 24 ساعة
    setTimeout(() => {
      this.blockedIPs.delete(ip);
      logger.info(`IP unblocked: ${ip}`);
    }, 24 * 60 * 60 * 1000);

    this.emit('ipBlocked', { ip, reason });
  }

  // إعداد التشفير والمصادقة
  setupEncryptionAndAuth() {
    // إعداد خوارزمية التشفير
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
  }

  // تشفير البيانات
  encrypt(text) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey, iv);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      logger.error('Encryption failed:', error);
      throw new Error('Encryption failed');
    }
  }

  // فك التشفير
  decrypt(encryptedData) {
    try {
      const { encrypted, iv, tag } = encryptedData;
      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey, Buffer.from(iv, 'hex'));
      
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Decryption failed:', error);
      throw new Error('Decryption failed');
    }
  }

  // إنشاء JWT Token
  generateJWT(payload, expiresIn = '24h') {
    try {
      return jwt.sign(payload, this.jwtSecret, { 
        expiresIn,
        issuer: 'ayopfx-trading-bot',
        audience: 'ayopfx-users'
      });
    } catch (error) {
      logger.error('JWT generation failed:', error);
      throw new Error('Token generation failed');
    }
  }

  // التحقق من JWT Token
  verifyJWT(token) {
    try {
      return jwt.verify(token, this.jwtSecret, {
        issuer: 'ayopfx-trading-bot',
        audience: 'ayopfx-users'
      });
    } catch (error) {
      logger.error('JWT verification failed:', error);
      throw new Error('Invalid token');
    }
  }

  // تشفير كلمة المرور
  async hashPassword(password) {
    try {
      const saltRounds = 12;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      logger.error('Password hashing failed:', error);
      throw new Error('Password hashing failed');
    }
  }

  // التحقق من كلمة المرور
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Password verification failed:', error);
      return false;
    }
  }

  // إعداد حماية CSRF
  setupCSRFProtection(app) {
    // إنشاء CSRF token
    app.use((req, res, next) => {
      if (!req.session) {
        req.session = {};
      }
      
      if (!req.session.csrfToken) {
        req.session.csrfToken = crypto.randomBytes(32).toString('hex');
      }
      
      res.locals.csrfToken = req.session.csrfToken;
      next();
    });

    // التحقق من CSRF token للطلبات المهمة
    app.use('/api', (req, res, next) => {
      if (['POST', 'PUT', 'DELETE'].includes(req.method)) {
        const token = req.headers['x-csrf-token'] || req.body.csrfToken;
        
        if (!token || token !== req.session.csrfToken) {
          this.logSuspiciousActivity(req.ip, 'CSRF_TOKEN_MISMATCH', {
            endpoint: req.path,
            method: req.method
          });
          return res.status(403).json({ error: 'CSRF token mismatch' });
        }
      }
      next();
    });
  }

  // إعداد مراقبة الجلسات
  setupSessionMonitoring() {
    // تنظيف الجلسات المنتهية الصلاحية كل ساعة
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 60 * 1000);

    // مراقبة الجلسات المشبوهة
    setInterval(() => {
      this.monitorSuspiciousSessions();
    }, 5 * 60 * 1000); // كل 5 دقائق
  }

  // تنظيف الجلسات المنتهية
  cleanupExpiredSessions() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [sessionId, session] of this.sessions) {
      if (session.expiresAt < now) {
        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  // مراقبة الجلسات المشبوهة
  monitorSuspiciousSessions() {
    for (const [sessionId, session] of this.sessions) {
      // فحص الجلسات من IPs متعددة
      if (session.ips && session.ips.size > 3) {
        this.logSuspiciousActivity(Array.from(session.ips)[0], 'MULTIPLE_IPS_SAME_SESSION', {
          sessionId,
          ips: Array.from(session.ips)
        });
      }

      // فحص الجلسات طويلة المدى
      const sessionAge = Date.now() - session.createdAt;
      if (sessionAge > 7 * 24 * 60 * 60 * 1000) { // أكثر من أسبوع
        this.logSuspiciousActivity(session.ip, 'LONG_RUNNING_SESSION', {
          sessionId,
          age: sessionAge
        });
      }
    }
  }

  // إنشاء جلسة آمنة
  createSecureSession(userId, ip, userAgent) {
    const sessionId = crypto.randomUUID();
    const expiresAt = Date.now() + (24 * 60 * 60 * 1000); // 24 ساعة

    const session = {
      id: sessionId,
      userId,
      ip,
      userAgent,
      createdAt: Date.now(),
      expiresAt,
      lastActivity: Date.now(),
      ips: new Set([ip])
    };

    this.sessions.set(sessionId, session);
    
    logger.info('Secure session created', { sessionId, userId, ip });
    return sessionId;
  }

  // التحقق من الجلسة
  validateSession(sessionId, ip) {
    const session = this.sessions.get(sessionId);
    
    if (!session) {
      return { valid: false, reason: 'Session not found' };
    }

    if (session.expiresAt < Date.now()) {
      this.sessions.delete(sessionId);
      return { valid: false, reason: 'Session expired' };
    }

    // إضافة IP جديد للجلسة
    session.ips.add(ip);
    session.lastActivity = Date.now();

    return { valid: true, session };
  }

  // إنهاء الجلسة
  destroySession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.sessions.delete(sessionId);
      logger.info('Session destroyed', { sessionId, userId: session.userId });
      return true;
    }
    return false;
  }

  // الحصول على إحصائيات الأمان
  getSecurityStats() {
    return {
      activeSessions: this.sessions.size,
      blockedIPs: this.blockedIPs.size,
      suspiciousActivities: this.suspiciousActivities.length,
      recentSuspiciousActivities: this.suspiciousActivities.filter(
        a => Date.now() - new Date(a.timestamp).getTime() < 3600000
      ).length,
      securityLevel: this.calculateSecurityLevel()
    };
  }

  // حساب مستوى الأمان
  calculateSecurityLevel() {
    const recentActivities = this.suspiciousActivities.filter(
      a => Date.now() - new Date(a.timestamp).getTime() < 3600000
    ).length;

    if (recentActivities === 0) return 'high';
    if (recentActivities < 5) return 'medium';
    if (recentActivities < 20) return 'low';
    return 'critical';
  }

  // تصدير سجل الأمان
  exportSecurityLog() {
    return {
      suspiciousActivities: this.suspiciousActivities,
      blockedIPs: Array.from(this.blockedIPs),
      activeSessions: this.sessions.size,
      exportedAt: new Date().toISOString()
    };
  }
}

export default new AdvancedSecurity();
