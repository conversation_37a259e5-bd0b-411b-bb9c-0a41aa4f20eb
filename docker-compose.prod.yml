# AyoPFX Trading Bot - Production Docker Compose
version: '3.8'

services:
  # Main Trading Bot Application
  ayopfx-bot:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ayopfx-trading-bot
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - LOG_LEVEL=info
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=postgresql://ayopfx:${DB_PASSWORD}@postgres:5432/ayopfx_trading
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - TWELVE_DATA_API_KEY=${TWELVE_DATA_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./backups:/app/backups
    depends_on:
      - redis
      - postgres
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ayopfx-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ayopfx-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ayopfx_trading
      - POSTGRES_USER=ayopfx
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ayopfx -d ayopfx_trading"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ayopfx-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - ayopfx-bot
    networks:
      - ayopfx-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ayopfx-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ayopfx-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: ayopfx-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - ayopfx-network

  # Log Management with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ayopfx-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ayopfx-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: ayopfx-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    networks:
      - ayopfx-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: ayopfx-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - ayopfx-network

  # Backup Service
  backup:
    image: alpine:latest
    container_name: ayopfx-backup
    restart: unless-stopped
    volumes:
      - ./backups:/backups
      - ./data:/data:ro
      - postgres-data:/postgres-data:ro
      - redis-data:/redis-data:ro
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - RETENTION_DAYS=30
    command: |
      sh -c "
        apk add --no-cache postgresql-client redis
        echo '${BACKUP_SCHEDULE} /backup.sh' | crontab -
        crond -f
      "
    networks:
      - ayopfx-network

  # Health Monitor
  health-monitor:
    image: alpine:latest
    container_name: ayopfx-health-monitor
    restart: unless-stopped
    volumes:
      - ./monitoring/health-check.sh:/health-check.sh
    environment:
      - CHECK_INTERVAL=60
      - WEBHOOK_URL=${HEALTH_WEBHOOK_URL}
    command: |
      sh -c "
        apk add --no-cache curl
        chmod +x /health-check.sh
        while true; do
          /health-check.sh
          sleep ${CHECK_INTERVAL}
        done
      "
    depends_on:
      - ayopfx-bot
    networks:
      - ayopfx-network

# Networks
networks:
  ayopfx-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local

# Secrets (for production use)
secrets:
  db_password:
    file: ./secrets/db_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  encryption_key:
    file: ./secrets/encryption_key.txt
