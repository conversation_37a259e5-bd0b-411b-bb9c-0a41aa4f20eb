import { EventEmitter } from 'events';
import moment from 'moment';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class BacktestingService extends EventEmitter {
  constructor() {
    super();
    this.isRunning = false;
    this.results = new Map();
  }

  // Initialize backtesting service
  async initialize() {
    try {
      logger.info('Initializing Backtesting Service...');
      
      logger.info('✅ Backtesting Service initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Backtesting Service initialization failed:', error);
      throw error;
    }
  }

  // Run backtest on historical data
  async runBacktest(strategy, historicalData, settings = {}) {
    try {
      if (this.isRunning) {
        throw new Error('Backtest already running');
      }

      this.isRunning = true;
      const backtestId = this.generateBacktestId();

      logger.info('Starting backtest', {
        id: backtestId,
        strategy: strategy.name,
        dataPoints: historicalData.length
      });

      const defaultSettings = {
        initialBalance: 10000,
        riskPerTrade: 0.02,
        commission: 7,
        spread: 2,
        slippage: 1,
        startDate: moment().subtract(1, 'year').toISOString(),
        endDate: moment().toISOString()
      };

      const backtestSettings = { ...defaultSettings, ...settings };

      // Initialize backtest state
      const state = {
        id: backtestId,
        balance: backtestSettings.initialBalance,
        equity: backtestSettings.initialBalance,
        positions: [],
        trades: [],
        currentIndex: 0,
        settings: backtestSettings,
        startTime: new Date().toISOString()
      };

      // Run simulation
      const results = await this.simulateTrading(state, strategy, historicalData);

      // Calculate performance metrics
      const performance = this.calculatePerformanceMetrics(results);

      // Save results
      const backtestResult = {
        id: backtestId,
        strategy: strategy.name,
        settings: backtestSettings,
        performance,
        trades: results.trades,
        equity: results.equityCurve,
        drawdown: results.drawdownCurve,
        completedAt: new Date().toISOString(),
        duration: moment().diff(moment(state.startTime), 'seconds')
      };

      this.results.set(backtestId, backtestResult);
      
      // Cache results
      await cacheUtils.set(`backtest_${backtestId}`, backtestResult, 86400); // 24 hours

      this.isRunning = false;

      logger.info('Backtest completed', {
        id: backtestId,
        totalTrades: results.trades.length,
        finalBalance: results.balance,
        winRate: performance.winRate
      });

      this.emit('backtestCompleted', backtestResult);
      return backtestResult;

    } catch (error) {
      this.isRunning = false;
      logger.error('Backtest error:', error);
      throw error;
    }
  }

  // Simulate trading on historical data
  async simulateTrading(state, strategy, historicalData) {
    const equityCurve = [];
    const drawdownCurve = [];
    let maxEquity = state.balance;

    for (let i = 0; i < historicalData.length; i++) {
      const candle = historicalData[i];
      state.currentIndex = i;

      // Update current prices for open positions
      this.updatePositions(state, candle);

      // Check for strategy signals
      const signal = await this.getStrategySignal(strategy, historicalData, i);

      if (signal && this.canOpenPosition(state)) {
        await this.openBacktestPosition(state, signal, candle);
      }

      // Check for position exits
      await this.checkPositionExits(state, candle);

      // Record equity curve
      const currentEquity = this.calculateEquity(state, candle);
      equityCurve.push({
        timestamp: candle.timestamp,
        equity: currentEquity,
        balance: state.balance
      });

      // Calculate drawdown
      if (currentEquity > maxEquity) {
        maxEquity = currentEquity;
      }
      const drawdown = ((maxEquity - currentEquity) / maxEquity) * 100;
      drawdownCurve.push({
        timestamp: candle.timestamp,
        drawdown: drawdown
      });

      state.equity = currentEquity;
    }

    return {
      balance: state.balance,
      equity: state.equity,
      trades: state.trades,
      equityCurve,
      drawdownCurve
    };
  }

  // Get strategy signal for backtesting
  async getStrategySignal(strategy, data, currentIndex) {
    try {
      // Mock strategy signal generation
      if (currentIndex < 50) return null; // Need enough data for indicators

      const recentData = data.slice(Math.max(0, currentIndex - 50), currentIndex + 1);
      
      // Simple moving average crossover strategy for demo
      const shortMA = this.calculateSMA(recentData, 10);
      const longMA = this.calculateSMA(recentData, 20);
      
      if (shortMA.length < 2 || longMA.length < 2) return null;

      const currentShort = shortMA[shortMA.length - 1];
      const currentLong = longMA[longMA.length - 1];
      const prevShort = shortMA[shortMA.length - 2];
      const prevLong = longMA[longMA.length - 2];

      // Bullish crossover
      if (prevShort <= prevLong && currentShort > currentLong) {
        return {
          direction: 'buy',
          entry: { price: data[currentIndex].close },
          stopLoss: { price: data[currentIndex].close * 0.99 },
          takeProfits: [
            { price: data[currentIndex].close * 1.02, percentage: 100 }
          ],
          confidence: 0.7,
          metadata: {
            symbol: 'EURUSD',
            timeframe: '1h',
            timestamp: data[currentIndex].timestamp
          }
        };
      }

      // Bearish crossover
      if (prevShort >= prevLong && currentShort < currentLong) {
        return {
          direction: 'sell',
          entry: { price: data[currentIndex].close },
          stopLoss: { price: data[currentIndex].close * 1.01 },
          takeProfits: [
            { price: data[currentIndex].close * 0.98, percentage: 100 }
          ],
          confidence: 0.7,
          metadata: {
            symbol: 'EURUSD',
            timeframe: '1h',
            timestamp: data[currentIndex].timestamp
          }
        };
      }

      return null;
    } catch (error) {
      logger.error('Error getting strategy signal:', error);
      return null;
    }
  }

  // Calculate Simple Moving Average
  calculateSMA(data, period) {
    const sma = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1)
        .reduce((acc, candle) => acc + candle.close, 0);
      sma.push(sum / period);
    }
    return sma;
  }

  // Check if can open new position
  canOpenPosition(state) {
    return state.positions.length < 3; // Max 3 concurrent positions
  }

  // Open position in backtest
  async openBacktestPosition(state, signal, candle) {
    const positionSize = this.calculateBacktestPositionSize(state, signal);
    
    const position = {
      id: this.generatePositionId(),
      symbol: signal.metadata.symbol,
      direction: signal.direction,
      entryPrice: signal.entry.price,
      stopLoss: signal.stopLoss.price,
      takeProfits: signal.takeProfits,
      size: positionSize,
      openTime: candle.timestamp,
      status: 'open',
      unrealizedPnL: 0
    };

    state.positions.push(position);
  }

  // Calculate position size for backtest
  calculateBacktestPositionSize(state, signal) {
    const riskAmount = state.balance * state.settings.riskPerTrade;
    const stopLossDistance = Math.abs(signal.entry.price - signal.stopLoss.price);
    const positionSize = riskAmount / stopLossDistance;
    
    return Math.min(positionSize, state.balance * 0.1); // Max 10% of balance
  }

  // Update positions with current market data
  updatePositions(state, candle) {
    state.positions.forEach(position => {
      const priceChange = candle.close - position.entryPrice;
      
      if (position.direction === 'buy') {
        position.unrealizedPnL = priceChange * position.size;
      } else {
        position.unrealizedPnL = -priceChange * position.size;
      }
    });
  }

  // Check for position exits
  async checkPositionExits(state, candle) {
    const positionsToClose = [];

    state.positions.forEach(position => {
      // Check stop loss
      if (position.direction === 'buy' && candle.low <= position.stopLoss) {
        positionsToClose.push({ position, exitPrice: position.stopLoss, reason: 'stop_loss' });
      } else if (position.direction === 'sell' && candle.high >= position.stopLoss) {
        positionsToClose.push({ position, exitPrice: position.stopLoss, reason: 'stop_loss' });
      }

      // Check take profit
      position.takeProfits.forEach(tp => {
        if (position.direction === 'buy' && candle.high >= tp.price) {
          positionsToClose.push({ position, exitPrice: tp.price, reason: 'take_profit' });
        } else if (position.direction === 'sell' && candle.low <= tp.price) {
          positionsToClose.push({ position, exitPrice: tp.price, reason: 'take_profit' });
        }
      });
    });

    // Close positions
    positionsToClose.forEach(({ position, exitPrice, reason }) => {
      this.closeBacktestPosition(state, position, exitPrice, reason, candle.timestamp);
    });
  }

  // Close position in backtest
  closeBacktestPosition(state, position, exitPrice, reason, timestamp) {
    const priceChange = exitPrice - position.entryPrice;
    let pnl = 0;

    if (position.direction === 'buy') {
      pnl = priceChange * position.size;
    } else {
      pnl = -priceChange * position.size;
    }

    // Subtract commission
    pnl -= state.settings.commission;

    // Update balance
    state.balance += pnl;

    // Record trade
    const trade = {
      ...position,
      exitPrice,
      closeTime: timestamp,
      realizedPnL: pnl,
      closeReason: reason,
      duration: moment(timestamp).diff(moment(position.openTime), 'minutes')
    };

    state.trades.push(trade);

    // Remove from open positions
    const index = state.positions.findIndex(p => p.id === position.id);
    if (index !== -1) {
      state.positions.splice(index, 1);
    }
  }

  // Calculate current equity
  calculateEquity(state, candle) {
    let unrealizedPnL = 0;
    state.positions.forEach(position => {
      unrealizedPnL += position.unrealizedPnL;
    });
    return state.balance + unrealizedPnL;
  }

  // Calculate performance metrics
  calculatePerformanceMetrics(results) {
    const trades = results.trades;
    
    if (trades.length === 0) {
      return this.getEmptyPerformanceMetrics();
    }

    const winningTrades = trades.filter(t => t.realizedPnL > 0);
    const losingTrades = trades.filter(t => t.realizedPnL < 0);
    
    const totalProfit = winningTrades.reduce((sum, t) => sum + t.realizedPnL, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.realizedPnL, 0));
    
    const winRate = (winningTrades.length / trades.length) * 100;
    const profitFactor = totalLoss > 0 ? totalProfit / totalLoss : 0;
    
    const avgWin = winningTrades.length > 0 ? totalProfit / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? totalLoss / losingTrades.length : 0;
    
    const maxDrawdown = this.calculateMaxDrawdown(results.drawdownCurve);
    
    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: winRate,
      profitFactor: profitFactor,
      totalProfit: totalProfit,
      totalLoss: totalLoss,
      netProfit: totalProfit - totalLoss,
      avgWin: avgWin,
      avgLoss: avgLoss,
      maxDrawdown: maxDrawdown,
      sharpeRatio: this.calculateSharpeRatio(results.equityCurve),
      returnOnInvestment: ((results.balance - 10000) / 10000) * 100
    };
  }

  // Calculate maximum drawdown
  calculateMaxDrawdown(drawdownCurve) {
    return Math.max(...drawdownCurve.map(d => d.drawdown));
  }

  // Calculate Sharpe ratio
  calculateSharpeRatio(equityCurve) {
    if (equityCurve.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < equityCurve.length; i++) {
      const returnPct = (equityCurve[i].equity - equityCurve[i-1].equity) / equityCurve[i-1].equity;
      returns.push(returnPct);
    }
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev > 0 ? avgReturn / stdDev : 0;
  }

  // Get empty performance metrics
  getEmptyPerformanceMetrics() {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      profitFactor: 0,
      totalProfit: 0,
      totalLoss: 0,
      netProfit: 0,
      avgWin: 0,
      avgLoss: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      returnOnInvestment: 0
    };
  }

  // Generate backtest ID
  generateBacktestId() {
    return `BT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generate position ID
  generatePositionId() {
    return `POS_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get backtest results
  getBacktestResults(backtestId) {
    return this.results.get(backtestId);
  }

  // Get all backtest results
  getAllBacktestResults() {
    return Array.from(this.results.values());
  }
}

export default new BacktestingService();
