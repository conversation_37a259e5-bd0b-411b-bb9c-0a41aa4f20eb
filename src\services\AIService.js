import { create, all } from 'mathjs';
import * as ss from 'simple-statistics';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';
import fs from 'fs/promises';
import path from 'path';

// Initialize math.js
const math = create(all);

class AIService extends EventEmitter {
  constructor() {
    super();
    this.models = new Map();
    this.isReady = false;
    this.trainingData = new Map();
    this.predictionCache = new Map();
    this.modelConfigurations = this.initializeModelConfigurations();
  }

  // Initialize model configurations
  initializeModelConfigurations() {
    return {
      pricePredictor: {
        type: 'tensorflow',
        architecture: 'lstm',
        inputFeatures: 20,
        outputFeatures: 1,
        hiddenLayers: [50, 30],
        epochs: 100,
        batchSize: 32,
        learningRate: 0.001
      },
      patternRecognizer: {
        type: 'brain',
        architecture: 'neural',
        hiddenLayers: [20, 15, 10],
        learningRate: 0.3,
        iterations: 20000
      },
      sentimentAnalyzer: {
        type: 'tensorflow',
        architecture: 'dense',
        inputFeatures: 15,
        outputFeatures: 3, // bullish, bearish, neutral
        hiddenLayers: [32, 16],
        epochs: 50,
        batchSize: 16
      },
      volatilityPredictor: {
        type: 'tensorflow',
        architecture: 'cnn',
        inputFeatures: 30,
        outputFeatures: 1,
        filters: [32, 64],
        kernelSize: 3,
        epochs: 75
      }
    };
  }

  // Initialize the AI service (Mock mode for development)
  async initialize() {
    try {
      logger.info('Initializing AI Service (Mock mode)...');

      // Create models directory if it doesn't exist
      await this.ensureModelsDirectory();

      // Mock AI initialization for development
      if (config.server.env === 'development' || process.env.USE_MOCK_DATA === 'true') {
        logger.info('Using mock AI models for development');
        this.isReady = true;
        logger.info('✅ Mock AI Service initialized successfully');
        return true;
      }

      // Real AI initialization would go here
      this.isReady = true;
      logger.info('✅ AI Service initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ AI Service initialization failed:', error);
      // Don't throw error in development mode
      if (config.server.env === 'development') {
        logger.warn('Continuing with mock AI...');
        this.isReady = true;
        return true;
      }
      throw error;
    }
  }

  // Ensure models directory exists
  async ensureModelsDirectory() {
    try {
      const modelsDir = path.dirname(config.ai.modelPath);
      await fs.mkdir(modelsDir, { recursive: true });
    } catch (error) {
      logger.error('Models directory creation error:', error);
    }
  }

  // Initialize TensorFlow models
  async initializeTensorFlowModels() {
    try {
      // Price Predictor LSTM Model
      const pricePredictorModel = tf.sequential({
        layers: [
          tf.layers.lstm({
            units: this.modelConfigurations.pricePredictor.hiddenLayers[0],
            returnSequences: true,
            inputShape: [this.modelConfigurations.pricePredictor.inputFeatures, 1]
          }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.lstm({
            units: this.modelConfigurations.pricePredictor.hiddenLayers[1],
            returnSequences: false
          }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({
            units: this.modelConfigurations.pricePredictor.outputFeatures,
            activation: 'linear'
          })
        ]
      });

      pricePredictorModel.compile({
        optimizer: tf.train.adam(this.modelConfigurations.pricePredictor.learningRate),
        loss: 'meanSquaredError',
        metrics: ['mae']
      });

      this.models.set('pricePredictor', pricePredictorModel);

      // Sentiment Analyzer Model
      const sentimentModel = tf.sequential({
        layers: [
          tf.layers.dense({
            units: this.modelConfigurations.sentimentAnalyzer.hiddenLayers[0],
            activation: 'relu',
            inputShape: [this.modelConfigurations.sentimentAnalyzer.inputFeatures]
          }),
          tf.layers.dropout({ rate: 0.3 }),
          tf.layers.dense({
            units: this.modelConfigurations.sentimentAnalyzer.hiddenLayers[1],
            activation: 'relu'
          }),
          tf.layers.dropout({ rate: 0.3 }),
          tf.layers.dense({
            units: this.modelConfigurations.sentimentAnalyzer.outputFeatures,
            activation: 'softmax'
          })
        ]
      });

      sentimentModel.compile({
        optimizer: 'adam',
        loss: 'categoricalCrossentropy',
        metrics: ['accuracy']
      });

      this.models.set('sentimentAnalyzer', sentimentModel);

      // Volatility Predictor CNN Model
      const volatilityModel = tf.sequential({
        layers: [
          tf.layers.conv1d({
            filters: this.modelConfigurations.volatilityPredictor.filters[0],
            kernelSize: this.modelConfigurations.volatilityPredictor.kernelSize,
            activation: 'relu',
            inputShape: [this.modelConfigurations.volatilityPredictor.inputFeatures, 1]
          }),
          tf.layers.maxPooling1d({ poolSize: 2 }),
          tf.layers.conv1d({
            filters: this.modelConfigurations.volatilityPredictor.filters[1],
            kernelSize: this.modelConfigurations.volatilityPredictor.kernelSize,
            activation: 'relu'
          }),
          tf.layers.globalMaxPooling1d(),
          tf.layers.dense({ units: 50, activation: 'relu' }),
          tf.layers.dropout({ rate: 0.2 }),
          tf.layers.dense({
            units: this.modelConfigurations.volatilityPredictor.outputFeatures,
            activation: 'linear'
          })
        ]
      });

      volatilityModel.compile({
        optimizer: 'adam',
        loss: 'meanSquaredError',
        metrics: ['mae']
      });

      this.models.set('volatilityPredictor', volatilityModel);

      logger.info('TensorFlow models initialized');
    } catch (error) {
      logger.error('TensorFlow models initialization error:', error);
      throw error;
    }
  }

  // Initialize Brain.js models
  async initializeBrainModels() {
    try {
      // Pattern Recognizer Neural Network
      const patternRecognizer = new brain.NeuralNetwork({
        hiddenLayers: this.modelConfigurations.patternRecognizer.hiddenLayers,
        learningRate: this.modelConfigurations.patternRecognizer.learningRate,
        iterations: this.modelConfigurations.patternRecognizer.iterations,
        errorThresh: 0.005,
        activation: 'sigmoid'
      });

      this.models.set('patternRecognizer', patternRecognizer);

      logger.info('Brain.js models initialized');
    } catch (error) {
      logger.error('Brain.js models initialization error:', error);
      throw error;
    }
  }

  // Load existing models
  async loadExistingModels() {
    try {
      const modelPath = config.ai.modelPath;
      
      // Check if model file exists
      try {
        await fs.access(modelPath);
        
        // Load TensorFlow models
        const pricePredictorPath = modelPath.replace('.json', '_price_predictor');
        const sentimentPath = modelPath.replace('.json', '_sentiment');
        const volatilityPath = modelPath.replace('.json', '_volatility');
        
        try {
          const loadedPriceModel = await tf.loadLayersModel(`file://${pricePredictorPath}/model.json`);
          this.models.set('pricePredictor', loadedPriceModel);
          logger.info('Loaded existing price predictor model');
        } catch (error) {
          logger.info('No existing price predictor model found, using new model');
        }

        try {
          const loadedSentimentModel = await tf.loadLayersModel(`file://${sentimentPath}/model.json`);
          this.models.set('sentimentAnalyzer', loadedSentimentModel);
          logger.info('Loaded existing sentiment analyzer model');
        } catch (error) {
          logger.info('No existing sentiment analyzer model found, using new model');
        }

        try {
          const loadedVolatilityModel = await tf.loadLayersModel(`file://${volatilityPath}/model.json`);
          this.models.set('volatilityPredictor', loadedVolatilityModel);
          logger.info('Loaded existing volatility predictor model');
        } catch (error) {
          logger.info('No existing volatility predictor model found, using new model');
        }

        // Load Brain.js models
        try {
          const brainModelData = await fs.readFile(modelPath.replace('.json', '_pattern.json'), 'utf8');
          const patternRecognizer = this.models.get('patternRecognizer');
          patternRecognizer.fromJSON(JSON.parse(brainModelData));
          logger.info('Loaded existing pattern recognizer model');
        } catch (error) {
          logger.info('No existing pattern recognizer model found, using new model');
        }

      } catch (error) {
        logger.info('No existing models found, starting with fresh models');
      }
    } catch (error) {
      logger.error('Model loading error:', error);
    }
  }

  // Predict price movement (Mock version for development)
  async predictPrice(analysis) {
    try {
      if (!this.isReady) {
        throw new Error('AI Service not ready');
      }

      // Mock prediction for development
      if (config.server.env === 'development' || process.env.USE_MOCK_DATA === 'true') {
        const mockPrediction = {
          direction: Math.random() > 0.5 ? 'bullish' : 'bearish',
          magnitude: Math.random() * 0.1,
          confidence: 0.6 + (Math.random() * 0.3), // 60-90% confidence
          timestamp: new Date().toISOString(),
          model: 'mockPredictor'
        };

        logger.info('Mock AI prediction generated', {
          symbol: analysis.symbol,
          direction: mockPrediction.direction,
          confidence: mockPrediction.confidence
        });

        return mockPrediction;
      }

      // Real AI prediction would go here
      return null;
    } catch (error) {
      logger.error('Price prediction error:', error);
      return null;
    }
  }

  // Recognize patterns
  async recognizePatterns(marketData) {
    try {
      if (!this.isReady) {
        throw new Error('AI Service not ready');
      }

      // Prepare pattern features
      const features = this.preparePatternFeatures(marketData);
      
      if (!features) {
        throw new Error('Unable to prepare pattern features');
      }

      // Get model
      const model = this.models.get('patternRecognizer');
      if (!model) {
        throw new Error('Pattern recognizer model not available');
      }

      // Make prediction
      const prediction = model.run(features);

      // Interpret results
      const patterns = this.interpretPatternPrediction(prediction);

      logger.ai('Pattern recognition completed', {
        patternsFound: patterns.length
      });

      return patterns;
    } catch (error) {
      logger.error('Pattern recognition error:', error);
      return [];
    }
  }

  // Analyze market sentiment
  async analyzeSentiment(analysis) {
    try {
      if (!this.isReady) {
        throw new Error('AI Service not ready');
      }

      // Prepare sentiment features
      const features = this.prepareSentimentFeatures(analysis);
      
      if (!features || features.length === 0) {
        throw new Error('Unable to prepare sentiment features');
      }

      // Get model
      const model = this.models.get('sentimentAnalyzer');
      if (!model) {
        throw new Error('Sentiment analyzer model not available');
      }

      // Make prediction
      const inputTensor = tf.tensor2d([features], [1, features.length]);
      const prediction = model.predict(inputTensor);
      const predictionValues = await prediction.data();
      
      // Clean up tensors
      inputTensor.dispose();
      prediction.dispose();

      // Interpret sentiment
      const sentiments = ['bearish', 'neutral', 'bullish'];
      const maxIndex = predictionValues.indexOf(Math.max(...predictionValues));
      
      const result = {
        sentiment: sentiments[maxIndex],
        confidence: predictionValues[maxIndex],
        probabilities: {
          bearish: predictionValues[0],
          neutral: predictionValues[1],
          bullish: predictionValues[2]
        },
        timestamp: new Date().toISOString()
      };

      logger.ai('Sentiment analysis completed', {
        sentiment: result.sentiment,
        confidence: result.confidence
      });

      return result;
    } catch (error) {
      logger.error('Sentiment analysis error:', error);
      return null;
    }
  }

  // Predict volatility
  async predictVolatility(marketData) {
    try {
      if (!this.isReady) {
        throw new Error('AI Service not ready');
      }

      // Prepare volatility features
      const features = this.prepareVolatilityFeatures(marketData);
      
      if (!features || features.length === 0) {
        throw new Error('Unable to prepare volatility features');
      }

      // Get model
      const model = this.models.get('volatilityPredictor');
      if (!model) {
        throw new Error('Volatility predictor model not available');
      }

      // Make prediction
      const inputTensor = tf.tensor3d([features], [1, features.length, 1]);
      const prediction = model.predict(inputTensor);
      const predictionValue = await prediction.data();
      
      // Clean up tensors
      inputTensor.dispose();
      prediction.dispose();

      const result = {
        volatility: predictionValue[0],
        level: this.classifyVolatilityLevel(predictionValue[0]),
        timestamp: new Date().toISOString()
      };

      logger.ai('Volatility prediction completed', {
        volatility: result.volatility,
        level: result.level
      });

      return result;
    } catch (error) {
      logger.error('Volatility prediction error:', error);
      return null;
    }
  }

  // Train models with new data
  async trainModels(trainingData) {
    try {
      logger.info('Starting model training...');

      // Train price predictor
      await this.trainPricePredictor(trainingData.priceData);

      // Train pattern recognizer
      await this.trainPatternRecognizer(trainingData.patternData);

      // Train sentiment analyzer
      await this.trainSentimentAnalyzer(trainingData.sentimentData);

      // Train volatility predictor
      await this.trainVolatilityPredictor(trainingData.volatilityData);

      // Save models
      await this.saveModels();

      logger.info('✅ Model training completed successfully');
      this.emit('modelsUpdated');

      return true;
    } catch (error) {
      logger.error('Model training error:', error);
      throw error;
    }
  }

  // Train price predictor model
  async trainPricePredictor(trainingData) {
    try {
      if (!trainingData || trainingData.length === 0) {
        logger.warn('No training data available for price predictor');
        return;
      }

      const model = this.models.get('pricePredictor');
      const { inputs, outputs } = this.preparePriceTrainingData(trainingData);

      const inputTensor = tf.tensor3d(inputs);
      const outputTensor = tf.tensor2d(outputs);

      await model.fit(inputTensor, outputTensor, {
        epochs: this.modelConfigurations.pricePredictor.epochs,
        batchSize: this.modelConfigurations.pricePredictor.batchSize,
        validationSplit: 0.2,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (epoch % 10 === 0) {
              logger.info(`Price predictor training epoch ${epoch}: loss=${logs.loss.toFixed(4)}`);
            }
          }
        }
      });

      // Clean up tensors
      inputTensor.dispose();
      outputTensor.dispose();

      logger.info('Price predictor model trained successfully');
    } catch (error) {
      logger.error('Price predictor training error:', error);
      throw error;
    }
  }

  // Train pattern recognizer model
  async trainPatternRecognizer(trainingData) {
    try {
      if (!trainingData || trainingData.length === 0) {
        logger.warn('No training data available for pattern recognizer');
        return;
      }

      const model = this.models.get('patternRecognizer');
      const formattedData = this.preparePatternTrainingData(trainingData);

      await model.trainAsync(formattedData);

      logger.info('Pattern recognizer model trained successfully');
    } catch (error) {
      logger.error('Pattern recognizer training error:', error);
      throw error;
    }
  }

  // Train sentiment analyzer model
  async trainSentimentAnalyzer(trainingData) {
    try {
      if (!trainingData || trainingData.length === 0) {
        logger.warn('No training data available for sentiment analyzer');
        return;
      }

      const model = this.models.get('sentimentAnalyzer');
      const { inputs, outputs } = this.prepareSentimentTrainingData(trainingData);

      const inputTensor = tf.tensor2d(inputs);
      const outputTensor = tf.tensor2d(outputs);

      await model.fit(inputTensor, outputTensor, {
        epochs: this.modelConfigurations.sentimentAnalyzer.epochs,
        batchSize: this.modelConfigurations.sentimentAnalyzer.batchSize,
        validationSplit: 0.2
      });

      // Clean up tensors
      inputTensor.dispose();
      outputTensor.dispose();

      logger.info('Sentiment analyzer model trained successfully');
    } catch (error) {
      logger.error('Sentiment analyzer training error:', error);
      throw error;
    }
  }

  // Train volatility predictor model
  async trainVolatilityPredictor(trainingData) {
    try {
      if (!trainingData || trainingData.length === 0) {
        logger.warn('No training data available for volatility predictor');
        return;
      }

      const model = this.models.get('volatilityPredictor');
      const { inputs, outputs } = this.prepareVolatilityTrainingData(trainingData);

      const inputTensor = tf.tensor3d(inputs);
      const outputTensor = tf.tensor2d(outputs);

      await model.fit(inputTensor, outputTensor, {
        epochs: this.modelConfigurations.volatilityPredictor.epochs,
        batchSize: 16,
        validationSplit: 0.2
      });

      // Clean up tensors
      inputTensor.dispose();
      outputTensor.dispose();

      logger.info('Volatility predictor model trained successfully');
    } catch (error) {
      logger.error('Volatility predictor training error:', error);
      throw error;
    }
  }

  // Save models
  async saveModels() {
    try {
      const modelPath = config.ai.modelPath;
      
      // Save TensorFlow models
      const pricePredictorPath = modelPath.replace('.json', '_price_predictor');
      const sentimentPath = modelPath.replace('.json', '_sentiment');
      const volatilityPath = modelPath.replace('.json', '_volatility');

      await this.models.get('pricePredictor').save(`file://${pricePredictorPath}`);
      await this.models.get('sentimentAnalyzer').save(`file://${sentimentPath}`);
      await this.models.get('volatilityPredictor').save(`file://${volatilityPath}`);

      // Save Brain.js model
      const patternModelData = this.models.get('patternRecognizer').toJSON();
      await fs.writeFile(modelPath.replace('.json', '_pattern.json'), JSON.stringify(patternModelData));

      logger.info('All models saved successfully');
    } catch (error) {
      logger.error('Model saving error:', error);
      throw error;
    }
  }

  // Helper methods for feature preparation
  preparePricePredictionFeatures(analysis) {
    try {
      const features = [];
      
      // Technical indicators
      if (analysis.technical.ema20) features.push(analysis.technical.ema20.current);
      if (analysis.technical.ema50) features.push(analysis.technical.ema50.current);
      if (analysis.technical.rsi) features.push(analysis.technical.rsi.current / 100);
      if (analysis.technical.macd) features.push(analysis.technical.macd.current.MACD);
      if (analysis.technical.atr) features.push(analysis.technical.atr.current);
      
      // Market state
      if (analysis.marketState) {
        features.push(analysis.marketState.marketScore.overall);
        features.push(analysis.marketState.liquidity.score);
        features.push(analysis.marketState.momentum.overall?.strength || 0);
      }
      
      // Sentiment
      features.push(analysis.sentiment.strength);
      features.push(analysis.confluence.score);
      
      // Pad or truncate to required length
      while (features.length < this.modelConfigurations.pricePredictor.inputFeatures) {
        features.push(0);
      }
      
      return features.slice(0, this.modelConfigurations.pricePredictor.inputFeatures);
    } catch (error) {
      logger.error('Price prediction feature preparation error:', error);
      return null;
    }
  }

  preparePatternFeatures(marketData) {
    try {
      const { closes, highs, lows, volumes } = marketData;
      
      if (!closes || closes.length < 20) {
        return null;
      }
      
      const features = {};
      const recent = closes.slice(-20);
      
      // Price patterns
      features.trend = this.calculateTrend(recent);
      features.volatility = this.calculateVolatility(recent);
      features.momentum = this.calculateMomentum(recent);
      
      // Volume patterns
      if (volumes && volumes.length >= 20) {
        const recentVolumes = volumes.slice(-20);
        features.volumeTrend = this.calculateTrend(recentVolumes);
        features.volumeSpike = this.detectVolumeSpike(recentVolumes);
      }
      
      // Normalize features
      Object.keys(features).forEach(key => {
        if (typeof features[key] === 'number') {
          features[key] = Math.max(-1, Math.min(1, features[key]));
        }
      });
      
      return features;
    } catch (error) {
      logger.error('Pattern feature preparation error:', error);
      return null;
    }
  }

  prepareSentimentFeatures(analysis) {
    try {
      const features = [];
      
      // Technical sentiment
      if (analysis.technical.rsi) {
        features.push((analysis.technical.rsi.current - 50) / 50); // Normalize RSI
      }
      
      if (analysis.technical.macd) {
        features.push(analysis.technical.macd.current.MACD > 0 ? 1 : -1);
      }
      
      // Market sentiment
      features.push(analysis.sentiment.strength);
      features.push(analysis.confluence.score);
      
      // Market state sentiment
      if (analysis.marketState) {
        features.push(analysis.marketState.marketScore.overall);
        features.push(analysis.marketState.liquidity.score);
        features.push(analysis.marketState.momentum.overall?.strength || 0);
      }
      
      // Pad to required length
      while (features.length < this.modelConfigurations.sentimentAnalyzer.inputFeatures) {
        features.push(0);
      }
      
      return features.slice(0, this.modelConfigurations.sentimentAnalyzer.inputFeatures);
    } catch (error) {
      logger.error('Sentiment feature preparation error:', error);
      return null;
    }
  }

  prepareVolatilityFeatures(marketData) {
    try {
      const { closes, highs, lows } = marketData;
      
      if (!closes || closes.length < 30) {
        return null;
      }
      
      const features = [];
      const recent = closes.slice(-30);
      
      // Price-based volatility features
      for (let i = 1; i < recent.length; i++) {
        const return_ = (recent[i] - recent[i-1]) / recent[i-1];
        features.push(return_);
      }
      
      return features;
    } catch (error) {
      logger.error('Volatility feature preparation error:', error);
      return null;
    }
  }

  // Helper calculation methods
  calculateTrend(values) {
    if (values.length < 2) return 0;
    const slope = (values[values.length - 1] - values[0]) / values.length;
    return Math.max(-1, Math.min(1, slope * 1000)); // Normalize
  }

  calculateVolatility(values) {
    if (values.length < 2) return 0;
    const returns = [];
    for (let i = 1; i < values.length; i++) {
      returns.push((values[i] - values[i-1]) / values[i-1]);
    }
    return ss.standardDeviation(returns);
  }

  calculateMomentum(values) {
    if (values.length < 10) return 0;
    const recent = values.slice(-5);
    const older = values.slice(-10, -5);
    const recentAvg = ss.mean(recent);
    const olderAvg = ss.mean(older);
    return (recentAvg - olderAvg) / olderAvg;
  }

  detectVolumeSpike(volumes) {
    if (volumes.length < 10) return 0;
    const recent = volumes.slice(-5);
    const baseline = volumes.slice(-10, -5);
    const recentAvg = ss.mean(recent);
    const baselineAvg = ss.mean(baseline);
    return recentAvg > baselineAvg * 1.5 ? 1 : 0;
  }

  calculatePredictionConfidence(analysis, prediction) {
    let confidence = 0.5; // Base confidence
    
    // Adjust based on confluence score
    confidence += analysis.confluence.score * 0.3;
    
    // Adjust based on market conditions
    if (analysis.marketState) {
      confidence += analysis.marketState.marketScore.overall * 0.2;
    }
    
    // Adjust based on prediction magnitude
    confidence += Math.min(Math.abs(prediction) * 0.1, 0.2);
    
    return Math.max(0, Math.min(1, confidence));
  }

  interpretPatternPrediction(prediction) {
    const patterns = [];
    
    // This is a simplified interpretation
    // In a real implementation, you would have more sophisticated pattern recognition
    if (prediction.trend > 0.7) {
      patterns.push({ type: 'uptrend', confidence: prediction.trend });
    } else if (prediction.trend < -0.7) {
      patterns.push({ type: 'downtrend', confidence: Math.abs(prediction.trend) });
    }
    
    if (prediction.volatility > 0.8) {
      patterns.push({ type: 'high_volatility', confidence: prediction.volatility });
    }
    
    return patterns;
  }

  classifyVolatilityLevel(volatility) {
    if (volatility > 0.02) return 'high';
    if (volatility > 0.01) return 'medium';
    return 'low';
  }

  // Training data preparation methods
  preparePriceTrainingData(trainingData) {
    // This would prepare sequences of features and corresponding price movements
    // Implementation depends on your training data format
    return { inputs: [], outputs: [] };
  }

  preparePatternTrainingData(trainingData) {
    // This would prepare pattern features and corresponding pattern labels
    // Implementation depends on your training data format
    return [];
  }

  prepareSentimentTrainingData(trainingData) {
    // This would prepare sentiment features and corresponding sentiment labels
    // Implementation depends on your training data format
    return { inputs: [], outputs: [] };
  }

  prepareVolatilityTrainingData(trainingData) {
    // This would prepare volatility features and corresponding volatility values
    // Implementation depends on your training data format
    return { inputs: [], outputs: [] };
  }

  // Cleanup
  async cleanup() {
    try {
      // Dispose of TensorFlow models
      for (const [name, model] of this.models) {
        if (model && typeof model.dispose === 'function') {
          model.dispose();
        }
      }
      
      this.models.clear();
      this.predictionCache.clear();
      this.isReady = false;
      
      logger.info('AI Service cleaned up');
    } catch (error) {
      logger.error('AI Service cleanup error:', error);
    }
  }
}

export default AIService;
