import express from 'express';
import logger from '../utils/logger.js';
import { AppError } from '../middleware/errorHandler.js';

const router = express.Router();

// News Analysis Routes
router.get('/news/sentiment/:symbol?', async (req, res, next) => {
  try {
    const { symbol = 'EURUSD' } = req.params;
    const newsAnalysisService = req.app.locals.newsAnalysisService;
    
    const sentiment = await newsAnalysisService.analyzeMarketSentiment(symbol);
    
    res.json({
      status: 'success',
      data: sentiment
    });
  } catch (error) {
    next(new AppError('Failed to analyze market sentiment', 500));
  }
});

router.get('/news/calendar', async (req, res, next) => {
  try {
    const { date } = req.query;
    const newsAnalysisService = req.app.locals.newsAnalysisService;
    
    const calendar = await newsAnalysisService.getEconomicCalendar(
      date ? new Date(date) : new Date()
    );
    
    res.json({
      status: 'success',
      data: calendar
    });
  } catch (error) {
    next(new AppError('Failed to get economic calendar', 500));
  }
});

router.get('/news/impact/:symbol/:timeframe?', async (req, res, next) => {
  try {
    const { symbol, timeframe = '1h' } = req.params;
    const newsAnalysisService = req.app.locals.newsAnalysisService;
    
    const impact = await newsAnalysisService.analyzeNewsImpact(symbol, timeframe);
    
    res.json({
      status: 'success',
      data: impact
    });
  } catch (error) {
    next(new AppError('Failed to analyze news impact', 500));
  }
});

// Trading Sessions Routes
router.get('/sessions/active', async (req, res, next) => {
  try {
    const tradingSessionService = req.app.locals.tradingSessionService;
    
    const activeSessions = tradingSessionService.getCurrentActiveSessions();
    
    res.json({
      status: 'success',
      data: activeSessions
    });
  } catch (error) {
    next(new AppError('Failed to get active sessions', 500));
  }
});

router.get('/sessions/status', async (req, res, next) => {
  try {
    const tradingSessionService = req.app.locals.tradingSessionService;
    
    const sessionStatus = tradingSessionService.getSessionStatus();
    
    res.json({
      status: 'success',
      data: sessionStatus
    });
  } catch (error) {
    next(new AppError('Failed to get session status', 500));
  }
});

router.get('/sessions/analysis/:symbol', async (req, res, next) => {
  try {
    const { symbol } = req.params;
    const tradingSessionService = req.app.locals.tradingSessionService;
    
    const analysis = tradingSessionService.getSessionAnalysis(symbol);
    
    res.json({
      status: 'success',
      data: analysis
    });
  } catch (error) {
    next(new AppError('Failed to get session analysis', 500));
  }
});

router.get('/sessions/volatility', async (req, res, next) => {
  try {
    const tradingSessionService = req.app.locals.tradingSessionService;
    
    const volatility = tradingSessionService.getCurrentVolatility();
    const bestPairs = tradingSessionService.getBestTradingPairs();
    
    res.json({
      status: 'success',
      data: {
        currentVolatility: volatility,
        bestTradingPairs: bestPairs,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    next(new AppError('Failed to get volatility data', 500));
  }
});

// Portfolio Management Routes
router.get('/portfolio/summary', async (req, res, next) => {
  try {
    const portfolioService = req.app.locals.portfolioService;
    
    const summary = portfolioService.getPortfolioSummary();
    
    res.json({
      status: 'success',
      data: summary
    });
  } catch (error) {
    next(new AppError('Failed to get portfolio summary', 500));
  }
});

router.get('/portfolio/risk', async (req, res, next) => {
  try {
    const portfolioService = req.app.locals.portfolioService;
    
    const riskAnalysis = portfolioService.getRiskAnalysis();
    
    res.json({
      status: 'success',
      data: riskAnalysis
    });
  } catch (error) {
    next(new AppError('Failed to get risk analysis', 500));
  }
});

router.post('/portfolio/position/open', async (req, res, next) => {
  try {
    const portfolioService = req.app.locals.portfolioService;
    const signal = req.body;
    
    const position = await portfolioService.openPosition(signal);
    
    res.json({
      status: 'success',
      data: position
    });
  } catch (error) {
    next(new AppError('Failed to open position', 500));
  }
});

router.post('/portfolio/position/:id/close', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { closePrice, reason = 'manual' } = req.body;
    const portfolioService = req.app.locals.portfolioService;
    
    const result = await portfolioService.closePosition(id, closePrice, reason);
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    next(new AppError('Failed to close position', 500));
  }
});

// Backtesting Routes
router.post('/backtest/run', async (req, res, next) => {
  try {
    const { strategy, historicalData, settings } = req.body;
    const backtestingService = req.app.locals.backtestingService;
    
    if (!strategy || !historicalData) {
      throw new AppError('Strategy and historical data are required', 400);
    }
    
    const result = await backtestingService.runBacktest(strategy, historicalData, settings);
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    next(new AppError('Failed to run backtest', 500));
  }
});

router.get('/backtest/results/:id?', async (req, res, next) => {
  try {
    const { id } = req.params;
    const backtestingService = req.app.locals.backtestingService;
    
    let results;
    if (id) {
      results = backtestingService.getBacktestResults(id);
      if (!results) {
        throw new AppError('Backtest results not found', 404);
      }
    } else {
      results = backtestingService.getAllBacktestResults();
    }
    
    res.json({
      status: 'success',
      data: results
    });
  } catch (error) {
    next(new AppError('Failed to get backtest results', 500));
  }
});

// Mock historical data endpoint for backtesting
router.get('/backtest/mock-data/:symbol/:timeframe', async (req, res, next) => {
  try {
    const { symbol, timeframe } = req.params;
    const { days = 30 } = req.query;
    
    // Generate mock historical data
    const mockData = generateMockHistoricalData(symbol, timeframe, parseInt(days));
    
    res.json({
      status: 'success',
      data: mockData
    });
  } catch (error) {
    next(new AppError('Failed to generate mock data', 500));
  }
});

// Combined analysis endpoint
router.get('/analysis/comprehensive/:symbol/:timeframe?', async (req, res, next) => {
  try {
    const { symbol, timeframe = '1h' } = req.params;
    
    const newsAnalysisService = req.app.locals.newsAnalysisService;
    const tradingSessionService = req.app.locals.tradingSessionService;
    const dataService = req.app.locals.dataService;
    
    // Get comprehensive analysis
    const [
      newsImpact,
      sessionAnalysis,
      technicalAnalysis
    ] = await Promise.all([
      newsAnalysisService.analyzeNewsImpact(symbol, timeframe),
      tradingSessionService.getSessionAnalysis(symbol),
      dataService.getAnalysis(symbol, timeframe)
    ]);
    
    // Calculate overall score
    const overallScore = calculateOverallScore(newsImpact, sessionAnalysis, technicalAnalysis);
    
    res.json({
      status: 'success',
      data: {
        symbol,
        timeframe,
        newsImpact,
        sessionAnalysis,
        technicalAnalysis,
        overallScore,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    next(new AppError('Failed to get comprehensive analysis', 500));
  }
});

// Helper function to generate mock historical data
function generateMockHistoricalData(symbol, timeframe, days) {
  const data = [];
  const startPrice = 1.1000; // Starting price for EURUSD
  let currentPrice = startPrice;
  
  const intervals = {
    '1m': days * 24 * 60,
    '5m': days * 24 * 12,
    '15m': days * 24 * 4,
    '1h': days * 24,
    '4h': days * 6,
    '1d': days
  };
  
  const totalCandles = intervals[timeframe] || days * 24;
  
  for (let i = 0; i < totalCandles; i++) {
    const timestamp = new Date(Date.now() - (totalCandles - i) * getTimeframeMs(timeframe));
    
    // Generate realistic price movement
    const volatility = 0.001; // 0.1% volatility
    const change = (Math.random() - 0.5) * volatility * currentPrice;
    
    const open = currentPrice;
    const close = open + change;
    const high = Math.max(open, close) + Math.random() * volatility * currentPrice * 0.5;
    const low = Math.min(open, close) - Math.random() * volatility * currentPrice * 0.5;
    const volume = Math.floor(Math.random() * 1000000) + 500000;
    
    data.push({
      timestamp: timestamp.toISOString(),
      open: parseFloat(open.toFixed(5)),
      high: parseFloat(high.toFixed(5)),
      low: parseFloat(low.toFixed(5)),
      close: parseFloat(close.toFixed(5)),
      volume
    });
    
    currentPrice = close;
  }
  
  return data;
}

// Helper function to get timeframe in milliseconds
function getTimeframeMs(timeframe) {
  const timeframes = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000
  };
  
  return timeframes[timeframe] || timeframes['1h'];
}

// Helper function to calculate overall score
function calculateOverallScore(newsImpact, sessionAnalysis, technicalAnalysis) {
  let score = 0;
  let weight = 0;
  
  // News impact weight (30%)
  if (newsImpact && newsImpact.sentiment) {
    const newsScore = newsImpact.sentiment.confidence * 
      (newsImpact.sentiment.overall === 'bullish' ? 1 : 
       newsImpact.sentiment.overall === 'bearish' ? -1 : 0);
    score += newsScore * 0.3;
    weight += 0.3;
  }
  
  // Session analysis weight (20%)
  if (sessionAnalysis && sessionAnalysis.sessionScore) {
    const sessionScore = (sessionAnalysis.sessionScore / 100) * 
      (sessionAnalysis.recommendation.action === 'excellent' ? 1 :
       sessionAnalysis.recommendation.action === 'good' ? 0.7 :
       sessionAnalysis.recommendation.action === 'moderate' ? 0.4 : 0);
    score += sessionScore * 0.2;
    weight += 0.2;
  }
  
  // Technical analysis weight (50%)
  if (technicalAnalysis && technicalAnalysis.confluence) {
    const techScore = technicalAnalysis.confluence.score * 
      (technicalAnalysis.sentiment.overall === 'bullish' ? 1 :
       technicalAnalysis.sentiment.overall === 'bearish' ? -1 : 0);
    score += techScore * 0.5;
    weight += 0.5;
  }
  
  const finalScore = weight > 0 ? score / weight : 0;
  
  return {
    score: finalScore,
    confidence: weight,
    recommendation: getScoreRecommendation(finalScore),
    timestamp: new Date().toISOString()
  };
}

// Helper function to get recommendation based on score
function getScoreRecommendation(score) {
  if (score > 0.7) return { action: 'strong_buy', confidence: 0.9 };
  if (score > 0.3) return { action: 'buy', confidence: 0.7 };
  if (score > -0.3) return { action: 'hold', confidence: 0.5 };
  if (score > -0.7) return { action: 'sell', confidence: 0.7 };
  return { action: 'strong_sell', confidence: 0.9 };
}

export default router;
