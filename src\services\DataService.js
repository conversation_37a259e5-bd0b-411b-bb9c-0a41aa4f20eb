import axios from 'axios';
import WebSocket from 'ws';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class DataService extends EventEmitter {
  constructor() {
    super();
    this.connections = new Map();
    this.subscriptions = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;
    this.dataProviders = this.initializeDataProviders();
  }

  // Initialize data providers
  initializeDataProviders() {
    return {
      tradingView: {
        name: 'TradingView',
        baseUrl: 'https://scanner.tradingview.com',
        websocketUrl: 'wss://data.tradingview.com/socket.io/websocket',
        priority: 1,
        active: true
      },
      alphaVantage: {
        name: 'Alpha Vantage',
        baseUrl: 'https://www.alphavantage.co/query',
        apiKey: config.apiKeys.alphaVantage,
        priority: 2,
        active: !!config.apiKeys.alphaVantage
      },
      finnhub: {
        name: 'Finnhub',
        baseUrl: 'https://finnhub.io/api/v1',
        apiKey: config.apiKeys.finnhub,
        priority: 3,
        active: !!config.apiKeys.finnhub
      },
      twelveData: {
        name: 'Twelve Data',
        baseUrl: 'https://api.twelvedata.com',
        apiKey: config.apiKeys.twelveData,
        priority: 4,
        active: !!config.apiKeys.twelveData
      }
    };
  }

  // Initialize the data service
  async initialize() {
    try {
      logger.info('Initializing Data Service...');
      
      // Test data provider connections
      await this.testDataProviders();
      
      // Initialize WebSocket connections for real-time data
      await this.initializeWebSocketConnections();
      
      this.isConnected = true;
      logger.info('✅ Data Service initialized successfully');
      
      return true;
    } catch (error) {
      logger.error('❌ Data Service initialization failed:', error);
      throw error;
    }
  }

  // Test data provider connections
  async testDataProviders() {
    const results = {};
    
    for (const [name, provider] of Object.entries(this.dataProviders)) {
      if (!provider.active) {
        results[name] = { status: 'disabled', reason: 'No API key or disabled' };
        continue;
      }
      
      try {
        const isHealthy = await this.testProviderHealth(provider);
        results[name] = { status: isHealthy ? 'healthy' : 'unhealthy' };
        
        if (!isHealthy) {
          provider.active = false;
        }
      } catch (error) {
        results[name] = { status: 'error', error: error.message };
        provider.active = false;
      }
    }
    
    logger.info('Data provider health check results:', results);
    return results;
  }

  // Test individual provider health
  async testProviderHealth(provider) {
    try {
      switch (provider.name) {
        case 'Alpha Vantage':
          const avResponse = await axios.get(`${provider.baseUrl}?function=TIME_SERIES_INTRADAY&symbol=EURUSD&interval=1min&apikey=${provider.apiKey}&outputsize=compact`);
          return !avResponse.data['Error Message'] && !avResponse.data['Note'];
          
        case 'Finnhub':
          const fhResponse = await axios.get(`${provider.baseUrl}/quote?symbol=OANDA:EUR_USD&token=${provider.apiKey}`);
          return fhResponse.data && typeof fhResponse.data.c === 'number';
          
        case 'Twelve Data':
          const tdResponse = await axios.get(`${provider.baseUrl}/price?symbol=EUR/USD&apikey=${provider.apiKey}`);
          return tdResponse.data && typeof tdResponse.data.price === 'string';
          
        default:
          return true; // TradingView doesn't require API key for basic functionality
      }
    } catch (error) {
      logger.warn(`Provider ${provider.name} health check failed:`, error.message);
      return false;
    }
  }

  // Initialize WebSocket connections
  async initializeWebSocketConnections() {
    try {
      // TradingView WebSocket connection
      await this.initializeTradingViewWebSocket();
      
      // Finnhub WebSocket connection (if available)
      if (this.dataProviders.finnhub.active) {
        await this.initializeFinnhubWebSocket();
      }
      
      logger.info('WebSocket connections initialized');
    } catch (error) {
      logger.error('WebSocket initialization error:', error);
      throw error;
    }
  }

  // Initialize TradingView WebSocket
  async initializeTradingViewWebSocket() {
    try {
      const ws = new WebSocket('wss://data.tradingview.com/socket.io/websocket');
      
      ws.on('open', () => {
        logger.info('TradingView WebSocket connected');
        this.connections.set('tradingview', ws);
        
        // Send initial connection message
        this.sendTradingViewMessage(ws, 'set_auth_token', [config.tradingView.apiKey || '']);
        this.sendTradingViewMessage(ws, 'chart_create_session', ['cs_1', '']);
        this.sendTradingViewMessage(ws, 'quote_create_session', ['qs_1']);
      });
      
      ws.on('message', (data) => {
        this.handleTradingViewMessage(data);
      });
      
      ws.on('error', (error) => {
        logger.error('TradingView WebSocket error:', error);
        this.handleWebSocketError('tradingview', error);
      });
      
      ws.on('close', () => {
        logger.warn('TradingView WebSocket disconnected');
        this.connections.delete('tradingview');
        this.scheduleReconnect('tradingview');
      });
      
    } catch (error) {
      logger.error('TradingView WebSocket initialization error:', error);
      throw error;
    }
  }

  // Initialize Finnhub WebSocket
  async initializeFinnhubWebSocket() {
    try {
      const ws = new WebSocket(`wss://ws.finnhub.io?token=${this.dataProviders.finnhub.apiKey}`);
      
      ws.on('open', () => {
        logger.info('Finnhub WebSocket connected');
        this.connections.set('finnhub', ws);
      });
      
      ws.on('message', (data) => {
        this.handleFinnhubMessage(data);
      });
      
      ws.on('error', (error) => {
        logger.error('Finnhub WebSocket error:', error);
        this.handleWebSocketError('finnhub', error);
      });
      
      ws.on('close', () => {
        logger.warn('Finnhub WebSocket disconnected');
        this.connections.delete('finnhub');
        this.scheduleReconnect('finnhub');
      });
      
    } catch (error) {
      logger.error('Finnhub WebSocket initialization error:', error);
    }
  }

  // Get historical data
  async getHistoricalData(symbol, timeframe, limit = 1000) {
    try {
      const cacheKey = `historical_${symbol}_${timeframe}_${limit}`;
      
      // Check cache first
      const cachedData = await cacheUtils.get(cacheKey);
      if (cachedData) {
        logger.info(`Historical data retrieved from cache for ${symbol} ${timeframe}`);
        return cachedData;
      }
      
      // Try providers in priority order
      const activeProviders = Object.values(this.dataProviders)
        .filter(p => p.active)
        .sort((a, b) => a.priority - b.priority);
      
      for (const provider of activeProviders) {
        try {
          const data = await this.fetchHistoricalDataFromProvider(provider, symbol, timeframe, limit);
          if (data && data.length > 0) {
            // Cache the data for 5 minutes
            await cacheUtils.set(cacheKey, data, 300);
            
            logger.info(`Historical data retrieved from ${provider.name} for ${symbol} ${timeframe}`, {
              provider: provider.name,
              symbol,
              timeframe,
              dataPoints: data.length
            });
            
            return data;
          }
        } catch (error) {
          logger.warn(`Failed to get data from ${provider.name}:`, error.message);
          continue;
        }
      }
      
      throw new Error('No data providers available or all failed');
    } catch (error) {
      logger.error(`Historical data retrieval error for ${symbol} ${timeframe}:`, error);
      throw error;
    }
  }

  // Fetch historical data from specific provider
  async fetchHistoricalDataFromProvider(provider, symbol, timeframe, limit) {
    switch (provider.name) {
      case 'Alpha Vantage':
        return await this.fetchAlphaVantageData(symbol, timeframe, limit);
      case 'Finnhub':
        return await this.fetchFinnhubData(symbol, timeframe, limit);
      case 'Twelve Data':
        return await this.fetchTwelveDataData(symbol, timeframe, limit);
      default:
        throw new Error(`Provider ${provider.name} not implemented`);
    }
  }

  // Fetch data from Alpha Vantage
  async fetchAlphaVantageData(symbol, timeframe, limit) {
    try {
      const interval = this.convertTimeframeToAlphaVantage(timeframe);
      const function_name = interval.includes('min') ? 'TIME_SERIES_INTRADAY' : 'TIME_SERIES_DAILY';
      
      const params = {
        function: function_name,
        symbol: this.convertSymbolToAlphaVantage(symbol),
        apikey: this.dataProviders.alphaVantage.apiKey,
        outputsize: limit > 100 ? 'full' : 'compact'
      };
      
      if (function_name === 'TIME_SERIES_INTRADAY') {
        params.interval = interval;
      }
      
      const response = await axios.get(this.dataProviders.alphaVantage.baseUrl, { params });
      
      if (response.data['Error Message']) {
        throw new Error(response.data['Error Message']);
      }
      
      if (response.data['Note']) {
        throw new Error('API call frequency limit reached');
      }
      
      return this.parseAlphaVantageData(response.data, function_name);
    } catch (error) {
      logger.error('Alpha Vantage data fetch error:', error);
      throw error;
    }
  }

  // Fetch data from Finnhub
  async fetchFinnhubData(symbol, timeframe, limit) {
    try {
      const resolution = this.convertTimeframeToFinnhub(timeframe);
      const to = Math.floor(Date.now() / 1000);
      const from = to - (limit * this.getTimeframeSeconds(timeframe));
      
      const params = {
        symbol: this.convertSymbolToFinnhub(symbol),
        resolution: resolution,
        from: from,
        to: to,
        token: this.dataProviders.finnhub.apiKey
      };
      
      const response = await axios.get(`${this.dataProviders.finnhub.baseUrl}/stock/candle`, { params });
      
      if (response.data.s !== 'ok') {
        throw new Error('No data available');
      }
      
      return this.parseFinnhubData(response.data);
    } catch (error) {
      logger.error('Finnhub data fetch error:', error);
      throw error;
    }
  }

  // Fetch data from Twelve Data
  async fetchTwelveDataData(symbol, timeframe, limit) {
    try {
      const interval = this.convertTimeframeToTwelveData(timeframe);
      
      const params = {
        symbol: this.convertSymbolToTwelveData(symbol),
        interval: interval,
        outputsize: limit,
        apikey: this.dataProviders.twelveData.apiKey
      };
      
      const response = await axios.get(`${this.dataProviders.twelveData.baseUrl}/time_series`, { params });
      
      if (response.data.status === 'error') {
        throw new Error(response.data.message);
      }
      
      return this.parseTwelveDataData(response.data);
    } catch (error) {
      logger.error('Twelve Data fetch error:', error);
      throw error;
    }
  }

  // Get real-time price
  async getRealTimePrice(symbol) {
    try {
      const cacheKey = `realtime_${symbol}`;
      
      // Check cache first (cache for 1 second)
      const cachedPrice = await cacheUtils.get(cacheKey);
      if (cachedPrice) {
        return cachedPrice;
      }
      
      // Try to get from WebSocket data first
      const wsPrice = this.getWebSocketPrice(symbol);
      if (wsPrice) {
        await cacheUtils.set(cacheKey, wsPrice, 1);
        return wsPrice;
      }
      
      // Fallback to REST API
      const activeProviders = Object.values(this.dataProviders)
        .filter(p => p.active)
        .sort((a, b) => a.priority - b.priority);
      
      for (const provider of activeProviders) {
        try {
          const price = await this.fetchRealTimePriceFromProvider(provider, symbol);
          if (price) {
            await cacheUtils.set(cacheKey, price, 1);
            return price;
          }
        } catch (error) {
          logger.warn(`Failed to get real-time price from ${provider.name}:`, error.message);
          continue;
        }
      }
      
      throw new Error('No real-time price available');
    } catch (error) {
      logger.error(`Real-time price error for ${symbol}:`, error);
      throw error;
    }
  }

  // Subscribe to real-time data
  subscribeToRealTimeData(symbol, callback) {
    try {
      const subscriptionKey = `realtime_${symbol}`;
      
      if (!this.subscriptions.has(subscriptionKey)) {
        this.subscriptions.set(subscriptionKey, new Set());
        
        // Subscribe via WebSocket
        this.subscribeWebSocket(symbol);
      }
      
      this.subscriptions.get(subscriptionKey).add(callback);
      
      logger.info(`Subscribed to real-time data for ${symbol}`);
      
      return () => {
        this.unsubscribeFromRealTimeData(symbol, callback);
      };
    } catch (error) {
      logger.error(`Real-time subscription error for ${symbol}:`, error);
      throw error;
    }
  }

  // Unsubscribe from real-time data
  unsubscribeFromRealTimeData(symbol, callback) {
    try {
      const subscriptionKey = `realtime_${symbol}`;
      const callbacks = this.subscriptions.get(subscriptionKey);
      
      if (callbacks) {
        callbacks.delete(callback);
        
        if (callbacks.size === 0) {
          this.subscriptions.delete(subscriptionKey);
          this.unsubscribeWebSocket(symbol);
        }
      }
      
      logger.info(`Unsubscribed from real-time data for ${symbol}`);
    } catch (error) {
      logger.error(`Real-time unsubscription error for ${symbol}:`, error);
    }
  }

  // Subscribe via WebSocket
  subscribeWebSocket(symbol) {
    // TradingView subscription
    const tvWs = this.connections.get('tradingview');
    if (tvWs && tvWs.readyState === WebSocket.OPEN) {
      this.sendTradingViewMessage(tvWs, 'quote_add_symbols', ['qs_1', symbol]);
    }
    
    // Finnhub subscription
    const fhWs = this.connections.get('finnhub');
    if (fhWs && fhWs.readyState === WebSocket.OPEN) {
      fhWs.send(JSON.stringify({ type: 'subscribe', symbol: this.convertSymbolToFinnhub(symbol) }));
    }
  }

  // Unsubscribe via WebSocket
  unsubscribeWebSocket(symbol) {
    // TradingView unsubscription
    const tvWs = this.connections.get('tradingview');
    if (tvWs && tvWs.readyState === WebSocket.OPEN) {
      this.sendTradingViewMessage(tvWs, 'quote_remove_symbols', ['qs_1', symbol]);
    }
    
    // Finnhub unsubscription
    const fhWs = this.connections.get('finnhub');
    if (fhWs && fhWs.readyState === WebSocket.OPEN) {
      fhWs.send(JSON.stringify({ type: 'unsubscribe', symbol: this.convertSymbolToFinnhub(symbol) }));
    }
  }

  // Handle TradingView WebSocket messages
  handleTradingViewMessage(data) {
    try {
      const message = data.toString();
      
      // Parse TradingView protocol messages
      if (message.startsWith('~m~')) {
        const parts = message.split('~m~');
        for (let i = 1; i < parts.length; i += 2) {
          if (parts[i + 1]) {
            const payload = JSON.parse(parts[i + 1]);
            this.processTradingViewData(payload);
          }
        }
      }
    } catch (error) {
      logger.error('TradingView message handling error:', error);
    }
  }

  // Handle Finnhub WebSocket messages
  handleFinnhubMessage(data) {
    try {
      const message = JSON.parse(data.toString());
      
      if (message.type === 'trade' && message.data) {
        message.data.forEach(trade => {
          this.processFinnhubTrade(trade);
        });
      }
    } catch (error) {
      logger.error('Finnhub message handling error:', error);
    }
  }

  // Process TradingView data
  processTradingViewData(payload) {
    if (payload.m === 'qsd' && payload.p && payload.p[1]) {
      const symbol = payload.p[1].n;
      const data = payload.p[1].v;
      
      if (data && data.lp) { // Last price
        this.emitPriceUpdate(symbol, {
          price: data.lp,
          change: data.ch,
          changePercent: data.chp,
          volume: data.volume,
          timestamp: Date.now()
        });
      }
    }
  }

  // Process Finnhub trade data
  processFinnhubTrade(trade) {
    const symbol = this.convertFinnhubSymbolBack(trade.s);
    
    this.emitPriceUpdate(symbol, {
      price: trade.p,
      volume: trade.v,
      timestamp: trade.t
    });
  }

  // Emit price update to subscribers
  emitPriceUpdate(symbol, priceData) {
    const subscriptionKey = `realtime_${symbol}`;
    const callbacks = this.subscriptions.get(subscriptionKey);
    
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(symbol, priceData);
        } catch (error) {
          logger.error('Price update callback error:', error);
        }
      });
    }
    
    // Emit event
    this.emit('priceUpdate', { symbol, data: priceData });
  }

  // Helper methods for data conversion and parsing
  convertTimeframeToAlphaVantage(timeframe) {
    const mapping = {
      '1m': '1min', '5m': '5min', '15m': '15min', '30m': '30min',
      '1h': '60min', '4h': '60min', '1d': 'daily'
    };
    return mapping[timeframe] || '1min';
  }

  convertTimeframeToFinnhub(timeframe) {
    const mapping = {
      '1m': '1', '5m': '5', '15m': '15', '30m': '30',
      '1h': '60', '4h': '240', '1d': 'D'
    };
    return mapping[timeframe] || '1';
  }

  convertTimeframeToTwelveData(timeframe) {
    const mapping = {
      '1m': '1min', '5m': '5min', '15m': '15min', '30m': '30min',
      '1h': '1h', '4h': '4h', '1d': '1day'
    };
    return mapping[timeframe] || '1min';
  }

  convertSymbolToAlphaVantage(symbol) {
    // Convert forex pairs to Alpha Vantage format
    if (symbol.includes('USD')) {
      return symbol.replace('/', '');
    }
    return symbol;
  }

  convertSymbolToFinnhub(symbol) {
    // Convert to Finnhub forex format
    if (symbol.includes('/')) {
      return `OANDA:${symbol.replace('/', '_')}`;
    }
    return symbol;
  }

  convertSymbolToTwelveData(symbol) {
    // Twelve Data uses standard forex format
    return symbol;
  }

  convertFinnhubSymbolBack(finnhubSymbol) {
    // Convert back from Finnhub format
    if (finnhubSymbol.startsWith('OANDA:')) {
      return finnhubSymbol.replace('OANDA:', '').replace('_', '/');
    }
    return finnhubSymbol;
  }

  getTimeframeSeconds(timeframe) {
    const mapping = {
      '1m': 60, '5m': 300, '15m': 900, '30m': 1800,
      '1h': 3600, '4h': 14400, '1d': 86400
    };
    return mapping[timeframe] || 60;
  }

  // Parse data from different providers
  parseAlphaVantageData(data, functionName) {
    const timeSeriesKey = functionName === 'TIME_SERIES_INTRADAY' 
      ? Object.keys(data).find(key => key.includes('Time Series'))
      : 'Time Series (Daily)';
    
    const timeSeries = data[timeSeriesKey];
    if (!timeSeries) return [];
    
    return Object.entries(timeSeries)
      .map(([timestamp, values]) => ({
        timestamp: new Date(timestamp).getTime(),
        open: parseFloat(values['1. open']),
        high: parseFloat(values['2. high']),
        low: parseFloat(values['3. low']),
        close: parseFloat(values['4. close']),
        volume: parseInt(values['5. volume']) || 0
      }))
      .sort((a, b) => a.timestamp - b.timestamp);
  }

  parseFinnhubData(data) {
    const { t, o, h, l, c, v } = data;
    
    return t.map((timestamp, index) => ({
      timestamp: timestamp * 1000,
      open: o[index],
      high: h[index],
      low: l[index],
      close: c[index],
      volume: v[index] || 0
    }));
  }

  parseTwelveDataData(data) {
    if (!data.values) return [];
    
    return data.values
      .map(item => ({
        timestamp: new Date(item.datetime).getTime(),
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close),
        volume: parseInt(item.volume) || 0
      }))
      .sort((a, b) => a.timestamp - b.timestamp);
  }

  // Get WebSocket price
  getWebSocketPrice(symbol) {
    // This would return cached WebSocket price data
    // Implementation depends on how you store real-time data
    return null;
  }

  // Fetch real-time price from provider
  async fetchRealTimePriceFromProvider(provider, symbol) {
    switch (provider.name) {
      case 'Finnhub':
        const response = await axios.get(`${provider.baseUrl}/quote`, {
          params: {
            symbol: this.convertSymbolToFinnhub(symbol),
            token: provider.apiKey
          }
        });
        return {
          price: response.data.c,
          change: response.data.d,
          changePercent: response.data.dp,
          timestamp: Date.now()
        };
      
      case 'Twelve Data':
        const tdResponse = await axios.get(`${provider.baseUrl}/price`, {
          params: {
            symbol: this.convertSymbolToTwelveData(symbol),
            apikey: provider.apiKey
          }
        });
        return {
          price: parseFloat(tdResponse.data.price),
          timestamp: Date.now()
        };
      
      default:
        return null;
    }
  }

  // Send TradingView message
  sendTradingViewMessage(ws, method, params) {
    const message = JSON.stringify({
      m: method,
      p: params
    });
    const formattedMessage = `~m~${message.length}~m~${message}`;
    ws.send(formattedMessage);
  }

  // Handle WebSocket errors
  handleWebSocketError(provider, error) {
    logger.error(`WebSocket error for ${provider}:`, error);
    this.emit('connectionError', { provider, error });
  }

  // Schedule reconnection
  scheduleReconnect(provider) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      setTimeout(() => {
        logger.info(`Attempting to reconnect ${provider} (attempt ${this.reconnectAttempts})`);
        
        if (provider === 'tradingview') {
          this.initializeTradingViewWebSocket();
        } else if (provider === 'finnhub') {
          this.initializeFinnhubWebSocket();
        }
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      logger.error(`Max reconnection attempts reached for ${provider}`);
    }
  }

  // Cleanup
  async cleanup() {
    try {
      // Close all WebSocket connections
      for (const [name, ws] of this.connections) {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
        }
      }
      
      this.connections.clear();
      this.subscriptions.clear();
      this.isConnected = false;
      
      logger.info('Data Service cleaned up');
    } catch (error) {
      logger.error('Data Service cleanup error:', error);
    }
  }
}

export default DataService;
