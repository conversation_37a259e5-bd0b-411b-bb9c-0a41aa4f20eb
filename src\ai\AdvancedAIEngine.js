import { create, all } from 'mathjs';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import tradingConfig from '../config/tradingConfig.js';
import { cacheUtils } from '../database/redis.js';

// Initialize math.js
const math = create(all);

class AdvancedAIEngine extends EventEmitter {
  constructor() {
    super();
    this.models = new Map();
    this.strategies = new Map();
    this.isReady = false;
    this.learningData = [];
    this.performanceMetrics = {
      totalPredictions: 0,
      correctPredictions: 0,
      accuracy: 0,
      lastUpdated: null
    };
  }

  // تهيئة محرك الذكاء الاصطناعي
  async initialize() {
    try {
      logger.info('Initializing Advanced AI Engine...');

      // تهيئة النماذج المختلفة
      await this.initializeModels();

      // تهيئة الاستراتيجيات
      await this.initializeStrategies();

      // تحميل بيانات التعلم السابقة
      await this.loadLearningData();

      this.isReady = true;
      logger.info('✅ Advanced AI Engine initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Advanced AI Engine initialization failed:', error);
      throw error;
    }
  }

  // تهيئة النماذج
  async initializeModels() {
    // نموذج التنبؤ بالاتجاه
    this.models.set('direction_predictor', {
      type: 'neural_network',
      inputs: ['rsi', 'macd', 'bollinger_position', 'volume_ratio', 'sentiment'],
      outputs: ['bullish_probability', 'bearish_probability'],
      weights: this.initializeWeights(5, 10, 2), // 5 inputs, 10 hidden, 2 outputs
      learningRate: 0.01,
      accuracy: 0.75
    });

    // نموذج تحديد مستويات الدعم والمقاومة
    this.models.set('support_resistance', {
      type: 'pattern_recognition',
      lookback: 50,
      minTouches: 3,
      tolerance: 0.0005,
      accuracy: 0.82
    });

    // نموذج تحليل المشاعر
    this.models.set('sentiment_analyzer', {
      type: 'text_analysis',
      keywords: {
        bullish: ['rise', 'up', 'bull', 'positive', 'growth', 'strong'],
        bearish: ['fall', 'down', 'bear', 'negative', 'decline', 'weak'],
        neutral: ['stable', 'sideways', 'range', 'consolidation']
      },
      weights: { news: 0.4, social: 0.3, economic: 0.3 },
      accuracy: 0.68
    });

    // نموذج إدارة المخاطر الذكية
    this.models.set('risk_manager', {
      type: 'decision_tree',
      factors: ['volatility', 'correlation', 'drawdown', 'win_rate'],
      thresholds: {
        low_risk: 0.3,
        medium_risk: 0.6,
        high_risk: 0.8
      },
      accuracy: 0.85
    });

    // نموذج توقيت الدخول والخروج
    this.models.set('timing_optimizer', {
      type: 'reinforcement_learning',
      states: ['trend', 'volatility', 'volume', 'session'],
      actions: ['enter', 'wait', 'exit'],
      rewards: { profit: 1, loss: -1, missed_opportunity: -0.1 },
      accuracy: 0.71
    });
  }

  // تهيئة الاستراتيجيات
  async initializeStrategies() {
    // استراتيجية الذكاء الاصطناعي المتقدمة
    this.strategies.set('ai_advanced', {
      name: 'AI Advanced Strategy',
      description: 'استراتيجية متقدمة تجمع بين عدة نماذج ذكية',
      models: ['direction_predictor', 'support_resistance', 'sentiment_analyzer'],
      weights: { technical: 0.5, sentiment: 0.3, pattern: 0.2 },
      minConfidence: 0.75,
      enabled: true
    });

    // استراتيجية التعلم التكيفي
    this.strategies.set('adaptive_learning', {
      name: 'Adaptive Learning Strategy',
      description: 'استراتيجية تتعلم وتتكيف مع ظروف السوق',
      adaptationPeriod: 100, // عدد الصفقات للتكيف
      performanceThreshold: 0.6,
      enabled: true
    });

    // استراتيجية الأنماط المتقدمة
    this.strategies.set('advanced_patterns', {
      name: 'Advanced Pattern Recognition',
      description: 'تحليل الأنماط المعقدة والتشكيلات الفنية',
      patterns: ['head_shoulders', 'double_top', 'triangle', 'flag', 'wedge'],
      minPatternStrength: 0.7,
      enabled: true
    });
  }

  // تحليل شامل للسوق
  async analyzeMarket(symbol, timeframe, marketData) {
    try {
      if (!this.isReady) {
        throw new Error('AI Engine not ready');
      }

      const cacheKey = `ai_analysis_${symbol}_${timeframe}`;
      
      // فحص التخزين المؤقت
      const cachedAnalysis = await cacheUtils.get(cacheKey);
      if (cachedAnalysis) {
        return cachedAnalysis;
      }

      // تحليل فني متقدم
      const technicalAnalysis = await this.performTechnicalAnalysis(marketData);

      // تحليل المشاعر
      const sentimentAnalysis = await this.analyzeSentiment(symbol);

      // تحليل الأنماط
      const patternAnalysis = await this.analyzePatterns(marketData);

      // تنبؤ الاتجاه
      const directionPrediction = await this.predictDirection(technicalAnalysis, sentimentAnalysis);

      // تحديد مستويات الدعم والمقاومة
      const supportResistance = await this.findSupportResistance(marketData);

      // تقييم المخاطر
      const riskAssessment = await this.assessRisk(symbol, marketData);

      // دمج جميع التحليلات
      const comprehensiveAnalysis = this.combineAnalyses({
        technical: technicalAnalysis,
        sentiment: sentimentAnalysis,
        patterns: patternAnalysis,
        direction: directionPrediction,
        levels: supportResistance,
        risk: riskAssessment
      });

      // حفظ في التخزين المؤقت
      await cacheUtils.set(cacheKey, comprehensiveAnalysis, 300); // 5 دقائق

      logger.info('AI market analysis completed', {
        symbol,
        timeframe,
        confidence: comprehensiveAnalysis.overallConfidence
      });

      return comprehensiveAnalysis;

    } catch (error) {
      logger.error('AI market analysis failed:', error);
      return null;
    }
  }

  // التحليل الفني المتقدم
  async performTechnicalAnalysis(marketData) {
    const indicators = {};

    // حساب المؤشرات الفنية
    indicators.rsi = this.calculateRSI(marketData, 14);
    indicators.macd = this.calculateMACD(marketData);
    indicators.bollinger = this.calculateBollingerBands(marketData, 20, 2);
    indicators.ema = this.calculateEMA(marketData, [20, 50, 200]);
    indicators.stochastic = this.calculateStochastic(marketData, 14, 3);
    indicators.atr = this.calculateATR(marketData, 14);

    // تحليل الاتجاه
    const trendAnalysis = this.analyzeTrend(indicators);

    // تحليل الزخم
    const momentumAnalysis = this.analyzeMomentum(indicators);

    // تحليل التقلبات
    const volatilityAnalysis = this.analyzeVolatility(indicators);

    return {
      indicators,
      trend: trendAnalysis,
      momentum: momentumAnalysis,
      volatility: volatilityAnalysis,
      strength: this.calculateTechnicalStrength(indicators),
      confidence: this.calculateTechnicalConfidence(indicators)
    };
  }

  // تحليل المشاعر المتقدم
  async analyzeSentiment(symbol) {
    try {
      // محاكاة تحليل المشاعر (في التطبيق الحقيقي، سيتم جلب البيانات من مصادر خارجية)
      const newsScore = Math.random() * 2 - 1; // -1 إلى 1
      const socialScore = Math.random() * 2 - 1;
      const economicScore = Math.random() * 2 - 1;

      const model = this.models.get('sentiment_analyzer');
      const overallSentiment = 
        newsScore * model.weights.news +
        socialScore * model.weights.social +
        economicScore * model.weights.economic;

      return {
        overall: overallSentiment > 0.1 ? 'bullish' : overallSentiment < -0.1 ? 'bearish' : 'neutral',
        score: overallSentiment,
        components: {
          news: newsScore,
          social: socialScore,
          economic: economicScore
        },
        confidence: Math.abs(overallSentiment),
        strength: Math.min(Math.abs(overallSentiment) * 2, 1)
      };
    } catch (error) {
      logger.error('Sentiment analysis failed:', error);
      return { overall: 'neutral', score: 0, confidence: 0 };
    }
  }

  // تحليل الأنماط
  async analyzePatterns(marketData) {
    const patterns = [];

    // البحث عن أنماط الشموع اليابانية
    const candlestickPatterns = this.findCandlestickPatterns(marketData);
    patterns.push(...candlestickPatterns);

    // البحث عن أنماط الرسم البياني
    const chartPatterns = this.findChartPatterns(marketData);
    patterns.push(...chartPatterns);

    // تقييم قوة الأنماط
    const strongPatterns = patterns.filter(p => p.strength > 0.7);

    return {
      allPatterns: patterns,
      strongPatterns,
      count: patterns.length,
      averageStrength: patterns.length > 0 ? 
        patterns.reduce((sum, p) => sum + p.strength, 0) / patterns.length : 0
    };
  }

  // تنبؤ الاتجاه
  async predictDirection(technicalAnalysis, sentimentAnalysis) {
    const model = this.models.get('direction_predictor');
    
    // إعداد المدخلات
    const inputs = [
      technicalAnalysis.indicators.rsi.current / 100,
      technicalAnalysis.indicators.macd.signal > 0 ? 1 : 0,
      technicalAnalysis.indicators.bollinger.position,
      technicalAnalysis.volatility.normalized,
      sentimentAnalysis.score
    ];

    // تشغيل النموذج العصبي المبسط
    const prediction = this.runNeuralNetwork(model, inputs);

    return {
      bullishProbability: prediction[0],
      bearishProbability: prediction[1],
      direction: prediction[0] > prediction[1] ? 'bullish' : 'bearish',
      confidence: Math.abs(prediction[0] - prediction[1]),
      strength: Math.max(prediction[0], prediction[1])
    };
  }

  // تشغيل الشبكة العصبية المبسطة
  runNeuralNetwork(model, inputs) {
    // تطبيق مبسط للشبكة العصبية
    const hiddenLayer = inputs.map((input, i) => {
      return math.tanh(input * model.weights.inputToHidden[i] + model.weights.hiddenBias[i]);
    });

    const outputs = model.weights.hiddenToOutput.map((weights, i) => {
      const sum = hiddenLayer.reduce((acc, hidden, j) => acc + hidden * weights[j], 0);
      return 1 / (1 + Math.exp(-sum)); // sigmoid activation
    });

    return outputs;
  }

  // تهيئة الأوزان العشوائية
  initializeWeights(inputSize, hiddenSize, outputSize) {
    return {
      inputToHidden: Array(inputSize).fill().map(() => 
        Array(hiddenSize).fill().map(() => Math.random() * 2 - 1)
      ),
      hiddenToOutput: Array(outputSize).fill().map(() => 
        Array(hiddenSize).fill().map(() => Math.random() * 2 - 1)
      ),
      hiddenBias: Array(hiddenSize).fill().map(() => Math.random() * 2 - 1)
    };
  }

  // حساب RSI
  calculateRSI(data, period = 14) {
    if (data.length < period + 1) return { current: 50, trend: 'neutral' };

    const gains = [];
    const losses = [];

    for (let i = 1; i < data.length; i++) {
      const change = data[i].close - data[i - 1].close;
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    const avgGain = gains.slice(-period).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(-period).reduce((a, b) => a + b, 0) / period;

    const rs = avgGain / (avgLoss || 0.0001);
    const rsi = 100 - (100 / (1 + rs));

    return {
      current: rsi,
      trend: rsi > 70 ? 'overbought' : rsi < 30 ? 'oversold' : 'neutral',
      strength: Math.abs(rsi - 50) / 50
    };
  }

  // حساب MACD
  calculateMACD(data, fast = 12, slow = 26, signal = 9) {
    if (data.length < slow) return { macd: 0, signal: 0, histogram: 0 };

    const emaFast = this.calculateEMA(data, [fast])[fast];
    const emaSlow = this.calculateEMA(data, [slow])[slow];
    
    const macdLine = emaFast[emaFast.length - 1] - emaSlow[emaSlow.length - 1];
    
    // تبسيط حساب خط الإشارة
    const signalLine = macdLine * 0.8; // تقريب مبسط
    const histogram = macdLine - signalLine;

    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram,
      trend: histogram > 0 ? 'bullish' : 'bearish'
    };
  }

  // حساب Bollinger Bands
  calculateBollingerBands(data, period = 20, deviation = 2) {
    if (data.length < period) return { upper: 0, middle: 0, lower: 0, position: 0.5 };

    const closes = data.slice(-period).map(d => d.close);
    const sma = closes.reduce((a, b) => a + b, 0) / period;
    
    const variance = closes.reduce((acc, close) => acc + Math.pow(close - sma, 2), 0) / period;
    const stdDev = Math.sqrt(variance);

    const upper = sma + (stdDev * deviation);
    const lower = sma - (stdDev * deviation);
    const current = data[data.length - 1].close;
    const position = (current - lower) / (upper - lower);

    return {
      upper,
      middle: sma,
      lower,
      position: Math.max(0, Math.min(1, position)),
      squeeze: (upper - lower) / sma < 0.1
    };
  }

  // حساب EMA
  calculateEMA(data, periods) {
    const result = {};
    
    periods.forEach(period => {
      if (data.length < period) {
        result[period] = data.map(d => d.close);
        return;
      }

      const ema = [];
      const multiplier = 2 / (period + 1);
      
      // البداية بـ SMA
      const sma = data.slice(0, period).reduce((sum, d) => sum + d.close, 0) / period;
      ema.push(sma);

      // حساب EMA للباقي
      for (let i = period; i < data.length; i++) {
        const emaValue = (data[i].close * multiplier) + (ema[ema.length - 1] * (1 - multiplier));
        ema.push(emaValue);
      }

      result[period] = ema;
    });

    return result;
  }

  // حساب Stochastic
  calculateStochastic(data, kPeriod = 14, dPeriod = 3) {
    if (data.length < kPeriod) return { k: 50, d: 50, trend: 'neutral' };

    const recent = data.slice(-kPeriod);
    const high = Math.max(...recent.map(d => d.high));
    const low = Math.min(...recent.map(d => d.low));
    const close = data[data.length - 1].close;

    const k = ((close - low) / (high - low)) * 100;
    const d = k * 0.8; // تبسيط حساب %D

    return {
      k,
      d,
      trend: k > 80 ? 'overbought' : k < 20 ? 'oversold' : 'neutral'
    };
  }

  // حساب ATR
  calculateATR(data, period = 14) {
    if (data.length < period + 1) return { current: 0, normalized: 0 };

    const trueRanges = [];
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      trueRanges.push(tr);
    }

    const atr = trueRanges.slice(-period).reduce((a, b) => a + b, 0) / period;
    const currentPrice = data[data.length - 1].close;
    const normalized = atr / currentPrice;

    return {
      current: atr,
      normalized,
      volatility: normalized > 0.02 ? 'high' : normalized > 0.01 ? 'medium' : 'low'
    };
  }

  // تحليل الاتجاه
  analyzeTrend(indicators) {
    const ema = indicators.ema;
    const macd = indicators.macd;
    
    let trendScore = 0;
    let signals = [];

    // تحليل EMA
    if (ema[20] && ema[50] && ema[200]) {
      const current20 = ema[20][ema[20].length - 1];
      const current50 = ema[50][ema[50].length - 1];
      const current200 = ema[200][ema[200].length - 1];

      if (current20 > current50 && current50 > current200) {
        trendScore += 0.4;
        signals.push('EMA bullish alignment');
      } else if (current20 < current50 && current50 < current200) {
        trendScore -= 0.4;
        signals.push('EMA bearish alignment');
      }
    }

    // تحليل MACD
    if (macd.histogram > 0) {
      trendScore += 0.3;
      signals.push('MACD bullish');
    } else {
      trendScore -= 0.3;
      signals.push('MACD bearish');
    }

    return {
      direction: trendScore > 0.1 ? 'bullish' : trendScore < -0.1 ? 'bearish' : 'neutral',
      strength: Math.abs(trendScore),
      score: trendScore,
      signals
    };
  }

  // دمج جميع التحليلات
  combineAnalyses(analyses) {
    const weights = {
      technical: 0.4,
      sentiment: 0.2,
      patterns: 0.2,
      direction: 0.2
    };

    let overallScore = 0;
    let confidenceSum = 0;

    // دمج النتائج
    if (analyses.technical) {
      overallScore += analyses.technical.strength * weights.technical;
      confidenceSum += analyses.technical.confidence * weights.technical;
    }

    if (analyses.sentiment) {
      overallScore += analyses.sentiment.strength * weights.sentiment;
      confidenceSum += analyses.sentiment.confidence * weights.sentiment;
    }

    if (analyses.patterns) {
      overallScore += analyses.patterns.averageStrength * weights.patterns;
      confidenceSum += (analyses.patterns.count > 0 ? 0.8 : 0.3) * weights.patterns;
    }

    if (analyses.direction) {
      overallScore += analyses.direction.strength * weights.direction;
      confidenceSum += analyses.direction.confidence * weights.direction;
    }

    return {
      ...analyses,
      overallScore,
      overallConfidence: confidenceSum,
      recommendation: this.generateRecommendation(overallScore, confidenceSum),
      timestamp: new Date().toISOString()
    };
  }

  // إنشاء التوصية
  generateRecommendation(score, confidence) {
    if (confidence < 0.6) {
      return { action: 'wait', reason: 'Low confidence', confidence };
    }

    if (score > 0.7) {
      return { action: 'strong_buy', reason: 'Strong bullish signals', confidence };
    } else if (score > 0.3) {
      return { action: 'buy', reason: 'Bullish signals', confidence };
    } else if (score < -0.7) {
      return { action: 'strong_sell', reason: 'Strong bearish signals', confidence };
    } else if (score < -0.3) {
      return { action: 'sell', reason: 'Bearish signals', confidence };
    } else {
      return { action: 'hold', reason: 'Neutral signals', confidence };
    }
  }

  // تحديث الأداء
  updatePerformance(prediction, actualResult) {
    this.performanceMetrics.totalPredictions++;
    
    if (prediction === actualResult) {
      this.performanceMetrics.correctPredictions++;
    }

    this.performanceMetrics.accuracy = 
      this.performanceMetrics.correctPredictions / this.performanceMetrics.totalPredictions;
    
    this.performanceMetrics.lastUpdated = new Date().toISOString();

    // حفظ بيانات التعلم
    this.learningData.push({
      prediction,
      actual: actualResult,
      timestamp: new Date().toISOString()
    });

    // الاحتفاظ بآخر 1000 نقطة بيانات فقط
    if (this.learningData.length > 1000) {
      this.learningData = this.learningData.slice(-1000);
    }
  }

  // تحميل بيانات التعلم
  async loadLearningData() {
    try {
      const data = await cacheUtils.get('ai_learning_data');
      if (data) {
        this.learningData = data;
        logger.info(`Loaded ${this.learningData.length} learning data points`);
      }
    } catch (error) {
      logger.error('Failed to load learning data:', error);
    }
  }

  // حفظ بيانات التعلم
  async saveLearningData() {
    try {
      await cacheUtils.set('ai_learning_data', this.learningData, 86400); // 24 ساعة
    } catch (error) {
      logger.error('Failed to save learning data:', error);
    }
  }

  // الحصول على إحصائيات الأداء
  getPerformanceStats() {
    return {
      ...this.performanceMetrics,
      learningDataPoints: this.learningData.length,
      modelsCount: this.models.size,
      strategiesCount: this.strategies.size
    };
  }
}

export default new AdvancedAIEngine();
