import { createClient } from 'redis';
import config from '../config/config.js';
import logger from '../utils/logger.js';

let redisClient = null;
let isConnected = false;

export const initializeRedis = async () => {
  try {
    if (redisClient && isConnected) {
      logger.info('Redis already connected');
      return redisClient;
    }

    redisClient = createClient({
      url: config.database.redis.url,
      ...config.database.redis.options
    });

    // Handle Redis events
    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('✅ Redis client ready');
      isConnected = true;
    });

    redisClient.on('error', (error) => {
      logger.error('Redis client error:', error);
      isConnected = false;
    });

    redisClient.on('end', () => {
      logger.warn('Redis client disconnected');
      isConnected = false;
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis client reconnecting...');
    });

    // Connect to Redis
    await redisClient.connect();

    return redisClient;
  } catch (error) {
    logger.error('❌ Failed to connect to Redis:', error);
    throw error;
  }
};

export const getRedisClient = () => {
  if (!redisClient || !isConnected) {
    throw new Error('Redis client not initialized or not connected');
  }
  return redisClient;
};

export const disconnectRedis = async () => {
  try {
    if (redisClient && isConnected) {
      await redisClient.quit();
      isConnected = false;
      logger.info('✅ Disconnected from Redis');
    }
  } catch (error) {
    logger.error('❌ Error disconnecting from Redis:', error);
    throw error;
  }
};

// Cache utilities
export const cacheUtils = {
  // Set data with expiration
  async set(key, value, expireInSeconds = 3600) {
    try {
      const client = getRedisClient();
      const serializedValue = JSON.stringify(value);
      await client.setEx(key, expireInSeconds, serializedValue);
      return true;
    } catch (error) {
      logger.error('Redis set error:', error);
      return false;
    }
  },

  // Get data
  async get(key) {
    try {
      const client = getRedisClient();
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Redis get error:', error);
      return null;
    }
  },

  // Delete data
  async del(key) {
    try {
      const client = getRedisClient();
      await client.del(key);
      return true;
    } catch (error) {
      logger.error('Redis delete error:', error);
      return false;
    }
  },

  // Check if key exists
  async exists(key) {
    try {
      const client = getRedisClient();
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists error:', error);
      return false;
    }
  },

  // Set expiration
  async expire(key, seconds) {
    try {
      const client = getRedisClient();
      await client.expire(key, seconds);
      return true;
    } catch (error) {
      logger.error('Redis expire error:', error);
      return false;
    }
  },

  // Get TTL
  async ttl(key) {
    try {
      const client = getRedisClient();
      return await client.ttl(key);
    } catch (error) {
      logger.error('Redis TTL error:', error);
      return -1;
    }
  },

  // Increment counter
  async incr(key) {
    try {
      const client = getRedisClient();
      return await client.incr(key);
    } catch (error) {
      logger.error('Redis increment error:', error);
      return 0;
    }
  },

  // Add to set
  async sadd(key, ...members) {
    try {
      const client = getRedisClient();
      return await client.sAdd(key, members);
    } catch (error) {
      logger.error('Redis sadd error:', error);
      return 0;
    }
  },

  // Get set members
  async smembers(key) {
    try {
      const client = getRedisClient();
      return await client.sMembers(key);
    } catch (error) {
      logger.error('Redis smembers error:', error);
      return [];
    }
  },

  // Remove from set
  async srem(key, ...members) {
    try {
      const client = getRedisClient();
      return await client.sRem(key, members);
    } catch (error) {
      logger.error('Redis srem error:', error);
      return 0;
    }
  },

  // Push to list
  async lpush(key, ...elements) {
    try {
      const client = getRedisClient();
      return await client.lPush(key, elements.map(el => JSON.stringify(el)));
    } catch (error) {
      logger.error('Redis lpush error:', error);
      return 0;
    }
  },

  // Get list range
  async lrange(key, start = 0, stop = -1) {
    try {
      const client = getRedisClient();
      const values = await client.lRange(key, start, stop);
      return values.map(val => JSON.parse(val));
    } catch (error) {
      logger.error('Redis lrange error:', error);
      return [];
    }
  },

  // Trim list
  async ltrim(key, start, stop) {
    try {
      const client = getRedisClient();
      await client.lTrim(key, start, stop);
      return true;
    } catch (error) {
      logger.error('Redis ltrim error:', error);
      return false;
    }
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  await disconnectRedis();
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
});

export default {
  initializeRedis,
  getRedisClient,
  disconnectRedis,
  cacheUtils
};
