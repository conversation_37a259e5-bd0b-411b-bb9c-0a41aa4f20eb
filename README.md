# 🤖 AyoPFX Trading Bot

<div align="center">

![AyoPFX Logo](https://via.placeholder.com/200x100/2c3e50/ffffff?text=AyoPFX)

**بوت التداول الاحترافي المدعوم بالذكاء الاصطناعي**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/ayopfx/trading-bot)
[![Coverage](https://img.shields.io/badge/coverage-85%25-brightgreen)](https://github.com/ayopfx/trading-bot)

[English](#english) | [العربية](#arabic)

</div>

---

## 🌟 المميزات الرئيسية

### 📊 التحليل الفني المتقدم
- **50+ مؤشر فني**: RSI, MACD, Bollinger Bands, Ichimoku, وأكثر
- **تحليل الشموع اليابانية**: أنماط الانعكاس والاستمرار
- **مستويات الدعم والمقاومة**: تحديد تلقائي للمستويات الحرجة
- **تحليل الحجم**: Volume Profile و Order Flow Analysis

### 🧠 الذكاء الاصطناعي والتعلم الآلي
- **شبكات عصبية متقدمة**: LSTM و CNN للتنبؤ بالأسعار
- **تحليل المشاعر**: تحليل معنويات السوق في الوقت الفعلي
- **التعرف على الأنماط**: اكتشاف تلقائي لأنماط التداول
- **التحسين المستمر**: تحسين الخوارزميات بناءً على الأداء

### 📈 إدارة المخاطر الذكية
- **حساب حجم المركز**: تلقائي بناءً على نسبة المخاطرة
- **وقف الخسارة الديناميكي**: متعدد المستويات والأنواع
- **أهداف الربح المتدرجة**: توزيع الأرباح على مستويات متعددة
- **تحليل المخاطر/العائد**: حساب دقيق لنسب المخاطرة

### 🔔 نظام التنبيهات المتطور
- **Telegram Bot**: إشعارات فورية للإشارات والتحديثات
- **البريد الإلكتروني**: تقارير مفصلة وتنبيهات مهمة
- **واجهة ويب**: لوحة تحكم تفاعلية في الوقت الفعلي
- **Webhooks**: تكامل مع TradingView وMT4/MT5

### 🌐 مصادر البيانات المتعددة
- **TradingView**: بيانات عالية الجودة ومؤشرات متقدمة
- **Alpha Vantage**: بيانات تاريخية شاملة
- **Finnhub**: أسعار فورية وأخبار السوق
- **Twelve Data**: تغطية واسعة للأسواق العالمية

---

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- Node.js 18.0.0 أو أحدث
- Redis Server
- حساب Telegram Bot (اختياري)
- مفاتيح API لمصادر البيانات

### التثبيت السريع

```bash
# استنساخ المشروع
git clone https://github.com/ayopfx/trading-bot.git
cd trading-bot

# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env
nano .env

# تشغيل البوت
npm start
```

### الإعداد المفصل

1. **إعداد متغيرات البيئة**:
```bash
# خادم التطبيق
PORT=3000
NODE_ENV=production

# قاعدة البيانات
REDIS_URL=redis://localhost:6379

# مفاتيح API
ALPHA_VANTAGE_API_KEY=your_key_here
FINNHUB_API_KEY=your_key_here
TWELVE_DATA_API_KEY=your_key_here

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# TradingView
TRADINGVIEW_SECRET=your_webhook_secret
```

2. **تشغيل الخدمات المطلوبة**:
```bash
# تشغيل Redis
redis-server

# تشغيل البوت في وضع التطوير
npm run dev

# أو في وضع الإنتاج
npm start
```

---

## 📖 دليل الاستخدام

### الواجهة الرئيسية

قم بزيارة `http://localhost:3000` للوصول إلى لوحة التحكم:

- **لوحة التحكم**: عرض شامل لحالة النظام والإحصائيات
- **الإشارات**: قائمة بالإشارات النشطة والتاريخية
- **التحليل**: رسوم بيانية تفاعلية وتحليل AI
- **السوق**: أسعار مباشرة وحالة الجلسات
- **الإعدادات**: تخصيص المخاطر والتنبيهات

### API Endpoints

#### الحصول على الإشارات
```bash
GET /api/signals
GET /api/signals?symbol=EURUSD&limit=10
```

#### تحليل زوج عملات
```bash
GET /api/analysis/EURUSD/1h
```

#### بيانات السوق
```bash
GET /api/market/price/EURUSD
GET /api/market/history/EURUSD/1h?limit=100
```

#### حالة النظام
```bash
GET /api/status
GET /api/status/performance
```

### Webhooks

#### TradingView Webhook
```bash
POST /webhook/tradingview
Content-Type: application/json

{
  "symbol": "EURUSD",
  "action": "buy",
  "price": 1.1000,
  "stopLoss": 1.0950,
  "takeProfit": 1.1100,
  "confidence": 0.85,
  "strategy": "AI_Strategy_v1"
}
```

---

## 🔧 التخصيص والتطوير

### إضافة مؤشر فني جديد

```javascript
// src/indicators/CustomIndicator.js
export class CustomIndicator {
  calculate(data) {
    // منطق المؤشر هنا
    return result;
  }
}
```

### إضافة استراتيجية جديدة

```javascript
// src/strategies/CustomStrategy.js
export class CustomStrategy {
  analyze(marketData, indicators) {
    // منطق الاستراتيجية هنا
    return signals;
  }
}
```

### تخصيص نموذج AI

```javascript
// src/ai/CustomModel.js
export class CustomModel {
  async train(data) {
    // تدريب النموذج
  }
  
  async predict(features) {
    // التنبؤ
    return prediction;
  }
}
```

---

## 📊 الأداء والإحصائيات

### مقاييس الأداء
- **معدل النجاح**: 78.5%
- **متوسط المخاطرة/العائد**: 1:2.3
- **أقصى انخفاض**: 12.4%
- **عدد الصفقات الشهرية**: 45-60

### الأزواج المدعومة
- **العملات الرئيسية**: EUR/USD, GBP/USD, USD/JPY, AUD/USD
- **العملات الثانوية**: USD/CAD, USD/CHF, NZD/USD
- **المعادن**: XAU/USD (الذهب), XAG/USD (الفضة)

---

## 🛠️ استكشاف الأخطاء

### المشاكل الشائعة

1. **خطأ في الاتصال بـ Redis**:
```bash
# تأكد من تشغيل Redis
redis-cli ping
# يجب أن يرجع PONG
```

2. **فشل في الحصول على البيانات**:
```bash
# تحقق من مفاتيح API
curl "https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=EURUSD&interval=1min&apikey=YOUR_KEY"
```

3. **مشاكل Telegram Bot**:
```bash
# تحقق من صحة التوكن
curl "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"
```

### السجلات والمراقبة

```bash
# عرض السجلات المباشرة
npm run logs

# أو باستخدام PM2
npm run pm2:logs
```

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### إرشادات التطوير

- اتبع معايير ESLint المحددة
- اكتب اختبارات للميزات الجديدة
- وثق التغييرات في CHANGELOG.md
- استخدم Conventional Commits

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 📞 الدعم والتواصل

- **الموقع الرسمي**: [https://ayopfx.com](https://ayopfx.com)
- **البريد الإلكتروني**: <EMAIL>
- **Telegram**: [@AyoPFXSupport](https://t.me/AyoPFXSupport)
- **Discord**: [AyoPFX Community](https://discord.gg/ayopfx)

---

## ⚠️ إخلاء المسؤولية

هذا البرنامج مخصص للأغراض التعليمية والبحثية. التداول في الأسواق المالية ينطوي على مخاطر عالية وقد يؤدي إلى خسائر مالية. استخدم هذا البوت على مسؤوليتك الخاصة ولا تستثمر أموالاً لا يمكنك تحمل خسارتها.

---

<div align="center">

**صُنع بـ ❤️ من فريق AyoPFX**

[⬆ العودة إلى الأعلى](#-ayopfx-trading-bot)

</div>
