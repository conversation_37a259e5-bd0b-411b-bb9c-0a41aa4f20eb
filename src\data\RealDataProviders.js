import axios from 'axios';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class RealDataProviders extends EventEmitter {
  constructor() {
    super();
    this.providers = new Map();
    this.isReady = false;
    this.rateLimits = new Map();
    this.failoverOrder = ['alphavantage', 'finnhub', 'twelvedata', 'fxapi'];
  }

  // تهيئة مقدمي البيانات
  async initialize() {
    try {
      logger.info('Initializing Real Data Providers...');

      // تهيئة Alpha Vantage
      if (process.env.ALPHA_VANTAGE_API_KEY && process.env.ALPHA_VANTAGE_API_KEY !== 'demo') {
        await this.initializeAlphaVantage();
      }

      // تهيئة Finnhub
      if (process.env.FINNHUB_API_KEY && process.env.FINNHUB_API_KEY !== 'demo') {
        await this.initializeFinnhub();
      }

      // تهيئة Twelve Data
      if (process.env.TWELVE_DATA_API_KEY && process.env.TWELVE_DATA_API_KEY !== 'demo') {
        await this.initializeTwelveData();
      }

      // تهيئة FX API (مجاني)
      await this.initializeFXAPI();

      this.isReady = true;
      logger.info('✅ Real Data Providers initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Real Data Providers initialization failed:', error);
      throw error;
    }
  }

  // تهيئة Alpha Vantage
  async initializeAlphaVantage() {
    const provider = {
      name: 'Alpha Vantage',
      baseUrl: 'https://www.alphavantage.co/query',
      apiKey: process.env.ALPHA_VANTAGE_API_KEY,
      rateLimit: { requests: 5, period: 60000 }, // 5 requests per minute
      endpoints: {
        forex: 'FX_INTRADAY',
        daily: 'FX_DAILY',
        news: 'NEWS_SENTIMENT'
      },
      status: 'active'
    };

    // اختبار الاتصال
    try {
      const testResponse = await this.testAlphaVantage(provider);
      if (testResponse) {
        this.providers.set('alphavantage', provider);
        logger.info('Alpha Vantage provider initialized');
      }
    } catch (error) {
      logger.warn('Alpha Vantage initialization failed:', error.message);
    }
  }

  // اختبار Alpha Vantage
  async testAlphaVantage(provider) {
    const url = `${provider.baseUrl}?function=CURRENCY_EXCHANGE_RATE&from_currency=USD&to_currency=EUR&apikey=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 10000 });
    
    if (response.data['Error Message']) {
      throw new Error(response.data['Error Message']);
    }
    
    return response.data['Realtime Currency Exchange Rate'] ? true : false;
  }

  // تهيئة Finnhub
  async initializeFinnhub() {
    const provider = {
      name: 'Finnhub',
      baseUrl: 'https://finnhub.io/api/v1',
      apiKey: process.env.FINNHUB_API_KEY,
      rateLimit: { requests: 60, period: 60000 }, // 60 requests per minute
      endpoints: {
        forex: 'forex/candle',
        news: 'news',
        sentiment: 'news-sentiment'
      },
      status: 'active'
    };

    try {
      const testResponse = await this.testFinnhub(provider);
      if (testResponse) {
        this.providers.set('finnhub', provider);
        logger.info('Finnhub provider initialized');
      }
    } catch (error) {
      logger.warn('Finnhub initialization failed:', error.message);
    }
  }

  // اختبار Finnhub
  async testFinnhub(provider) {
    const url = `${provider.baseUrl}/forex/symbol?exchange=oanda&token=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 10000 });
    
    if (response.data.error) {
      throw new Error(response.data.error);
    }
    
    return Array.isArray(response.data) && response.data.length > 0;
  }

  // تهيئة Twelve Data
  async initializeTwelveData() {
    const provider = {
      name: 'Twelve Data',
      baseUrl: 'https://api.twelvedata.com',
      apiKey: process.env.TWELVE_DATA_API_KEY,
      rateLimit: { requests: 800, period: 86400000 }, // 800 requests per day
      endpoints: {
        forex: 'time_series',
        quote: 'quote',
        news: 'news'
      },
      status: 'active'
    };

    try {
      const testResponse = await this.testTwelveData(provider);
      if (testResponse) {
        this.providers.set('twelvedata', provider);
        logger.info('Twelve Data provider initialized');
      }
    } catch (error) {
      logger.warn('Twelve Data initialization failed:', error.message);
    }
  }

  // اختبار Twelve Data
  async testTwelveData(provider) {
    const url = `${provider.baseUrl}/quote?symbol=EUR/USD&apikey=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 10000 });
    
    if (response.data.code && response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    
    return response.data.symbol ? true : false;
  }

  // تهيئة FX API (مجاني)
  async initializeFXAPI() {
    const provider = {
      name: 'FX API',
      baseUrl: 'https://api.fxapi.com/v1',
      apiKey: 'free', // مجاني
      rateLimit: { requests: 1000, period: 86400000 }, // 1000 requests per day
      endpoints: {
        latest: 'latest',
        historical: 'historical',
        currencies: 'currencies'
      },
      status: 'active'
    };

    try {
      const testResponse = await this.testFXAPI(provider);
      if (testResponse) {
        this.providers.set('fxapi', provider);
        logger.info('FX API provider initialized');
      }
    } catch (error) {
      logger.warn('FX API initialization failed:', error.message);
    }
  }

  // اختبار FX API
  async testFXAPI(provider) {
    const url = `${provider.baseUrl}/latest?base=USD&symbols=EUR`;
    
    const response = await axios.get(url, { timeout: 10000 });
    
    if (!response.data.success) {
      throw new Error(response.data.error?.info || 'API test failed');
    }
    
    return response.data.rates ? true : false;
  }

  // الحصول على البيانات المباشرة
  async getRealTimeData(symbol, timeframe = '1h') {
    try {
      const cacheKey = `realtime_${symbol}_${timeframe}`;
      
      // فحص التخزين المؤقت
      const cachedData = await cacheUtils.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // محاولة الحصول على البيانات من المقدمين
      for (const providerName of this.failoverOrder) {
        const provider = this.providers.get(providerName);
        if (!provider || provider.status !== 'active') continue;

        // فحص Rate Limiting
        if (await this.isRateLimited(providerName)) {
          logger.debug(`Rate limited for ${providerName}`);
          continue;
        }

        try {
          const data = await this.fetchFromProvider(provider, symbol, timeframe);
          if (data) {
            // تحديث Rate Limit
            await this.updateRateLimit(providerName);
            
            // حفظ في التخزين المؤقت
            await cacheUtils.set(cacheKey, data, 60); // دقيقة واحدة
            
            logger.info(`Real-time data fetched from ${providerName}`, { symbol, timeframe });
            return data;
          }
        } catch (error) {
          logger.warn(`Failed to fetch from ${providerName}:`, error.message);
          continue;
        }
      }

      // إذا فشلت جميع المقدمين، استخدم البيانات المحاكاة
      logger.warn('All providers failed, using mock data');
      return this.generateMockData(symbol, timeframe);

    } catch (error) {
      logger.error('Failed to get real-time data:', error);
      return this.generateMockData(symbol, timeframe);
    }
  }

  // جلب البيانات من مقدم معين
  async fetchFromProvider(provider, symbol, timeframe) {
    switch (provider.name) {
      case 'Alpha Vantage':
        return await this.fetchFromAlphaVantage(provider, symbol, timeframe);
      case 'Finnhub':
        return await this.fetchFromFinnhub(provider, symbol, timeframe);
      case 'Twelve Data':
        return await this.fetchFromTwelveData(provider, symbol, timeframe);
      case 'FX API':
        return await this.fetchFromFXAPI(provider, symbol, timeframe);
      default:
        throw new Error(`Unknown provider: ${provider.name}`);
    }
  }

  // جلب من Alpha Vantage
  async fetchFromAlphaVantage(provider, symbol, timeframe) {
    const interval = this.convertTimeframe(timeframe, 'alphavantage');
    const fromSymbol = symbol.substring(0, 3);
    const toSymbol = symbol.substring(3, 6);
    
    const url = `${provider.baseUrl}?function=${provider.endpoints.forex}&from_symbol=${fromSymbol}&to_symbol=${toSymbol}&interval=${interval}&apikey=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 15000 });
    
    if (response.data['Error Message']) {
      throw new Error(response.data['Error Message']);
    }

    const timeSeries = response.data[`Time Series FX (${interval})`];
    if (!timeSeries) {
      throw new Error('No time series data found');
    }

    return this.formatAlphaVantageData(timeSeries, symbol);
  }

  // جلب من Finnhub
  async fetchFromFinnhub(provider, symbol, timeframe) {
    const resolution = this.convertTimeframe(timeframe, 'finnhub');
    const to = Math.floor(Date.now() / 1000);
    const from = to - (24 * 60 * 60); // آخر 24 ساعة
    
    const url = `${provider.baseUrl}/${provider.endpoints.forex}?symbol=OANDA:${symbol}&resolution=${resolution}&from=${from}&to=${to}&token=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 15000 });
    
    if (response.data.s !== 'ok') {
      throw new Error('No data available');
    }

    return this.formatFinnhubData(response.data, symbol);
  }

  // جلب من Twelve Data
  async fetchFromTwelveData(provider, symbol, timeframe) {
    const interval = this.convertTimeframe(timeframe, 'twelvedata');
    const formattedSymbol = symbol.substring(0, 3) + '/' + symbol.substring(3, 6);
    
    const url = `${provider.baseUrl}/${provider.endpoints.forex}?symbol=${formattedSymbol}&interval=${interval}&apikey=${provider.apiKey}`;
    
    const response = await axios.get(url, { timeout: 15000 });
    
    if (response.data.code && response.data.code !== 200) {
      throw new Error(response.data.message);
    }

    if (!response.data.values) {
      throw new Error('No values found');
    }

    return this.formatTwelveDataData(response.data.values, symbol);
  }

  // جلب من FX API
  async fetchFromFXAPI(provider, symbol, timeframe) {
    const base = symbol.substring(0, 3);
    const target = symbol.substring(3, 6);
    
    const url = `${provider.baseUrl}/${provider.endpoints.latest}?base=${base}&symbols=${target}`;
    
    const response = await axios.get(url, { timeout: 15000 });
    
    if (!response.data.success) {
      throw new Error(response.data.error?.info || 'API request failed');
    }

    return this.formatFXAPIData(response.data, symbol);
  }

  // تحويل الإطار الزمني
  convertTimeframe(timeframe, provider) {
    const conversions = {
      alphavantage: {
        '1m': '1min',
        '5m': '5min',
        '15m': '15min',
        '1h': '60min',
        '1d': 'daily'
      },
      finnhub: {
        '1m': '1',
        '5m': '5',
        '15m': '15',
        '1h': '60',
        '1d': 'D'
      },
      twelvedata: {
        '1m': '1min',
        '5m': '5min',
        '15m': '15min',
        '1h': '1h',
        '1d': '1day'
      }
    };

    return conversions[provider]?.[timeframe] || timeframe;
  }

  // تنسيق بيانات Alpha Vantage
  formatAlphaVantageData(timeSeries, symbol) {
    const data = [];
    
    Object.entries(timeSeries).forEach(([timestamp, values]) => {
      data.push({
        timestamp: new Date(timestamp).toISOString(),
        open: parseFloat(values['1. open']),
        high: parseFloat(values['2. high']),
        low: parseFloat(values['3. low']),
        close: parseFloat(values['4. close']),
        volume: 1000000 // Alpha Vantage doesn't provide forex volume
      });
    });

    return {
      symbol,
      data: data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)),
      source: 'Alpha Vantage',
      timestamp: new Date().toISOString()
    };
  }

  // تنسيق بيانات Finnhub
  formatFinnhubData(response, symbol) {
    const data = [];
    
    for (let i = 0; i < response.t.length; i++) {
      data.push({
        timestamp: new Date(response.t[i] * 1000).toISOString(),
        open: response.o[i],
        high: response.h[i],
        low: response.l[i],
        close: response.c[i],
        volume: response.v[i] || 1000000
      });
    }

    return {
      symbol,
      data,
      source: 'Finnhub',
      timestamp: new Date().toISOString()
    };
  }

  // تنسيق بيانات Twelve Data
  formatTwelveDataData(values, symbol) {
    const data = values.map(item => ({
      timestamp: new Date(item.datetime).toISOString(),
      open: parseFloat(item.open),
      high: parseFloat(item.high),
      low: parseFloat(item.low),
      close: parseFloat(item.close),
      volume: parseInt(item.volume) || 1000000
    }));

    return {
      symbol,
      data: data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp)),
      source: 'Twelve Data',
      timestamp: new Date().toISOString()
    };
  }

  // تنسيق بيانات FX API
  formatFXAPIData(response, symbol) {
    const rate = Object.values(response.rates)[0];
    const now = new Date();
    
    // إنشاء بيانات محاكاة بناءً على السعر الحالي
    const data = [];
    for (let i = 23; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));
      const variation = (Math.random() - 0.5) * 0.01; // تغيير عشوائي 1%
      const price = rate * (1 + variation);
      
      data.push({
        timestamp: timestamp.toISOString(),
        open: price,
        high: price * 1.005,
        low: price * 0.995,
        close: price,
        volume: Math.floor(Math.random() * 1000000) + 500000
      });
    }

    return {
      symbol,
      data,
      source: 'FX API',
      timestamp: new Date().toISOString()
    };
  }

  // فحص Rate Limiting
  async isRateLimited(providerName) {
    try {
      const key = `rate_limit_${providerName}`;
      const data = await cacheUtils.get(key);
      
      if (!data) return false;
      
      const provider = this.providers.get(providerName);
      if (!provider) return false;
      
      const { count, timestamp } = data;
      const timeDiff = Date.now() - timestamp;
      
      if (timeDiff > provider.rateLimit.period) {
        // انتهت فترة Rate Limit
        await cacheUtils.del(key);
        return false;
      }
      
      return count >= provider.rateLimit.requests;
    } catch (error) {
      logger.error('Error checking rate limit:', error);
      return false;
    }
  }

  // تحديث Rate Limit
  async updateRateLimit(providerName) {
    try {
      const key = `rate_limit_${providerName}`;
      const data = await cacheUtils.get(key) || { count: 0, timestamp: Date.now() };
      
      const provider = this.providers.get(providerName);
      if (!provider) return;
      
      const timeDiff = Date.now() - data.timestamp;
      
      if (timeDiff > provider.rateLimit.period) {
        // بداية فترة جديدة
        data.count = 1;
        data.timestamp = Date.now();
      } else {
        data.count++;
      }
      
      const ttl = Math.ceil(provider.rateLimit.period / 1000);
      await cacheUtils.set(key, data, ttl);
    } catch (error) {
      logger.error('Error updating rate limit:', error);
    }
  }

  // إنشاء بيانات محاكاة
  generateMockData(symbol, timeframe) {
    const data = [];
    const now = new Date();
    const basePrice = 1.1000; // سعر أساسي لـ EURUSD
    
    // تحديد عدد النقاط حسب الإطار الزمني
    const points = {
      '1m': 60,
      '5m': 48,
      '15m': 32,
      '1h': 24,
      '4h': 12,
      '1d': 7
    };
    
    const pointCount = points[timeframe] || 24;
    const intervalMs = this.getIntervalMs(timeframe);
    
    let currentPrice = basePrice;
    
    for (let i = pointCount - 1; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - (i * intervalMs));
      
      // تغيير عشوائي في السعر
      const change = (Math.random() - 0.5) * 0.002; // 0.2% تغيير
      currentPrice = Math.max(0.5, currentPrice * (1 + change));
      
      const open = currentPrice;
      const volatility = 0.001;
      const high = open + (Math.random() * volatility * open);
      const low = open - (Math.random() * volatility * open);
      const close = low + (Math.random() * (high - low));
      
      data.push({
        timestamp: timestamp.toISOString(),
        open: parseFloat(open.toFixed(5)),
        high: parseFloat(high.toFixed(5)),
        low: parseFloat(low.toFixed(5)),
        close: parseFloat(close.toFixed(5)),
        volume: Math.floor(Math.random() * 1000000) + 500000
      });
      
      currentPrice = close;
    }

    return {
      symbol,
      data,
      source: 'Mock Data',
      timestamp: new Date().toISOString()
    };
  }

  // الحصول على الفترة بالميلي ثانية
  getIntervalMs(timeframe) {
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };
    
    return intervals[timeframe] || intervals['1h'];
  }

  // الحصول على حالة المقدمين
  getProvidersStatus() {
    const status = {};
    
    this.providers.forEach((provider, name) => {
      status[name] = {
        name: provider.name,
        status: provider.status,
        rateLimit: provider.rateLimit,
        endpoints: Object.keys(provider.endpoints)
      };
    });

    return {
      providers: status,
      activeCount: Array.from(this.providers.values()).filter(p => p.status === 'active').length,
      totalCount: this.providers.size,
      failoverOrder: this.failoverOrder
    };
  }

  // الحصول على أخبار السوق
  async getMarketNews(symbol = 'FOREX') {
    try {
      // محاولة الحصول على الأخبار من المقدمين
      for (const providerName of this.failoverOrder) {
        const provider = this.providers.get(providerName);
        if (!provider || !provider.endpoints.news) continue;

        try {
          const news = await this.fetchNewsFromProvider(provider, symbol);
          if (news && news.length > 0) {
            return news;
          }
        } catch (error) {
          logger.warn(`Failed to fetch news from ${providerName}:`, error.message);
          continue;
        }
      }

      // أخبار محاكاة
      return this.generateMockNews();
    } catch (error) {
      logger.error('Failed to get market news:', error);
      return this.generateMockNews();
    }
  }

  // إنشاء أخبار محاكاة
  generateMockNews() {
    const mockNews = [
      {
        title: 'EUR/USD rises on positive economic data',
        summary: 'The Euro strengthened against the Dollar following strong GDP growth',
        source: 'Financial Times',
        timestamp: new Date().toISOString(),
        sentiment: 'positive',
        impact: 'medium'
      },
      {
        title: 'Federal Reserve maintains dovish stance',
        summary: 'The Fed signals continued support for economic recovery',
        source: 'Reuters',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        sentiment: 'neutral',
        impact: 'high'
      },
      {
        title: 'European Central Bank policy update',
        summary: 'ECB maintains current monetary policy amid inflation concerns',
        source: 'Bloomberg',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        sentiment: 'neutral',
        impact: 'high'
      }
    ];

    return mockNews;
  }
}

export default new RealDataProviders();
