# 🚀 دليل البدء السريع - AyoPFX Trading Bot

## 📋 المتطلبات الأساسية

قبل البدء، تأكد من توفر:

- **Node.js** 18.0.0 أو أحدث
- **Redis Server** (للتخزين المؤقت)
- **Git** (لاستنساخ المشروع)
- حساب **Telegram Bot** (اختياري للتنبيهات)
- مفاتيح **API** لمصادر البيانات

## ⚡ التثبيت السريع (5 دقائق)

### 1. استنساخ المشروع
```bash
git clone https://github.com/ayopfx/trading-bot.git
cd trading-bot
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد متغيرات البيئة
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات
nano .env
```

### 4. الإعدادات الأساسية المطلوبة
```bash
# في ملف .env
NODE_ENV=development
PORT=3000
REDIS_URL=redis://localhost:6379

# مفاتيح API (احصل عليها مجاناً)
ALPHA_VANTAGE_API_KEY=your_key_here
FINNHUB_API_KEY=your_key_here

# Telegram (اختياري)
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

### 5. تشغيل Redis
```bash
# على Ubuntu/Debian
sudo systemctl start redis

# على macOS
brew services start redis

# على Windows
redis-server
```

### 6. تشغيل البوت
```bash
# وضع التطوير
npm run dev

# أو وضع الإنتاج
npm start
```

### 7. فتح لوحة التحكم
افتح المتصفح وانتقل إلى: `http://localhost:3000`

---

## 🔑 الحصول على مفاتيح API المجانية

### Alpha Vantage (مجاني)
1. اذهب إلى: https://www.alphavantage.co/support/#api-key
2. أدخل بريدك الإلكتروني
3. انسخ المفتاح إلى `ALPHA_VANTAGE_API_KEY`

### Finnhub (مجاني)
1. اذهب إلى: https://finnhub.io/register
2. سجل حساب جديد
3. انسخ المفتاح إلى `FINNHUB_API_KEY`

### Twelve Data (مجاني)
1. اذهب إلى: https://twelvedata.com/pricing
2. اختر الخطة المجانية
3. انسخ المفتاح إلى `TWELVE_DATA_API_KEY`

---

## 🤖 إعداد Telegram Bot

### 1. إنشاء البوت
```
1. ابحث عن @BotFather في Telegram
2. أرسل /newbot
3. اختر اسم للبوت
4. احفظ التوكن
```

### 2. الحصول على Chat ID
```
1. أرسل رسالة لبوتك
2. اذهب إلى: https://api.telegram.org/bot<TOKEN>/getUpdates
3. ابحث عن "chat":{"id": رقم
4. انسخ الرقم إلى TELEGRAM_CHAT_ID
```

---

## 🐳 التشغيل باستخدام Docker

### تشغيل سريع
```bash
# بناء وتشغيل
docker-compose up -d

# عرض السجلات
docker-compose logs -f ayopfx-trading-bot
```

### مع المراقبة
```bash
# تشغيل مع Grafana و Prometheus
docker-compose --profile monitoring up -d
```

---

## 📊 اختبار النظام

### 1. فحص الصحة
```bash
curl http://localhost:3000/health
```

### 2. اختبار API
```bash
# الحصول على حالة النظام
curl http://localhost:3000/api/status

# الحصول على الإشارات
curl http://localhost:3000/api/signals

# تحليل زوج عملات
curl http://localhost:3000/api/analysis/EURUSD/1h
```

### 3. اختبار التنبيهات
```bash
curl -X GET http://localhost:3000/api/notifications/test
```

---

## 🔧 الإعدادات المتقدمة

### إدارة المخاطر
```bash
# في ملف .env
DEFAULT_RISK_PERCENTAGE=2.0
MAX_RISK_PERCENTAGE=5.0
MIN_RISK_REWARD_RATIO=1.5
```

### الأزواج المدعومة
```bash
SUPPORTED_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,USDCHF,NZDUSD,XAUUSD
```

### الإطارات الزمنية
```bash
ANALYSIS_TIMEFRAMES=1m,5m,15m,1h,4h,1d
DEFAULT_TIMEFRAME=1h
```

---

## 🚨 استكشاف الأخطاء

### مشكلة: البوت لا يبدأ
```bash
# تحقق من Redis
redis-cli ping

# تحقق من المنفذ
netstat -tulpn | grep :3000

# تحقق من السجلات
npm run logs
```

### مشكلة: لا توجد بيانات
```bash
# تحقق من مفاتيح API
curl "https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol=EURUSD&interval=1min&apikey=YOUR_KEY"
```

### مشكلة: التنبيهات لا تعمل
```bash
# تحقق من Telegram Bot
curl "https://api.telegram.org/bot<TOKEN>/getMe"
```

---

## 📈 الخطوات التالية

بعد التشغيل الناجح:

1. **راقب الأداء**: تابع لوحة التحكم لمدة ساعة
2. **اضبط المخاطر**: عدل إعدادات المخاطر حسب احتياجك
3. **فعل التنبيهات**: اربط Telegram والبريد الإلكتروني
4. **اختبر الإشارات**: راقب الإشارات المولدة
5. **اقرأ الوثائق**: راجع README.md للتفاصيل الكاملة

---

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:

- **الوثائق**: راجع README.md
- **المشاكل الشائعة**: ابحث في GitHub Issues
- **الدعم**: راسلنا على <EMAIL>
- **المجتمع**: انضم إلى Discord أو Telegram

---

## ⚠️ تنبيه مهم

هذا البوت للأغراض التعليمية والبحثية. لا تستخدمه بأموال حقيقية دون فهم كامل للمخاطر!

---

**🎉 مبروك! بوت التداول الخاص بك جاهز الآن!**
