# =================================
# AyoPFX Trading Bot Configuration
# =================================

# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =================================
# Database Configuration
# =================================

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# =================================
# Data Provider API Keys
# =================================

# Alpha Vantage (Free tier: 5 calls/minute, 500 calls/day)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Finnhub (Free tier: 60 calls/minute)
FINNHUB_API_KEY=your_finnhub_api_key_here

# Twelve Data (Free tier: 800 calls/day)
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here

# =================================
# Telegram Bot Configuration
# =================================

# Telegram Bot Settings
TELEGRAM_ENABLED=true
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# =================================
# Email Notification Configuration
# =================================

# Email Settings
EMAIL_ENABLED=true
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM=AyoPFX Trading Bot <<EMAIL>>

# =================================
# TradingView Integration
# =================================

# TradingView Webhook Settings
TRADINGVIEW_SECRET=your_webhook_secret_here
TRADINGVIEW_API_KEY=your_tradingview_api_key_here

# =================================
# Trading Configuration
# =================================

# Supported Trading Pairs (comma-separated)
SUPPORTED_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,USDCHF,NZDUSD,XAUUSD

# Analysis Timeframes (comma-separated)
ANALYSIS_TIMEFRAMES=1m,5m,15m,1h,4h,1d

# Default Timeframe
DEFAULT_TIMEFRAME=1h

# Update Interval (milliseconds)
UPDATE_INTERVAL=60000

# =================================
# Risk Management
# =================================

# Default Risk Settings
DEFAULT_RISK_PERCENTAGE=2.0
MAX_RISK_PERCENTAGE=5.0
MIN_RISK_REWARD_RATIO=1.5

# Position Sizing
MAX_POSITION_SIZE=100000
MIN_POSITION_SIZE=1000

# =================================
# AI/ML Configuration
# =================================

# AI Model Settings
AI_ENABLED=true
AI_MODEL_PATH=./models/trading_models.json
AI_TRAINING_ENABLED=false
AI_PREDICTION_CONFIDENCE_THRESHOLD=0.6

# TensorFlow Settings
TF_CPP_MIN_LOG_LEVEL=2

# =================================
# Notification Settings
# =================================

# Notification Cooldown (minutes)
NOTIFICATION_COOLDOWN_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Strict Rate Limiting for Webhooks
STRICT_RATE_LIMIT_WINDOW_MS=60000
STRICT_RATE_LIMIT_MAX_REQUESTS=10

# =================================
# Logging Configuration
# =================================

# Log Level (error, warn, info, http, verbose, debug, silly)
LOG_LEVEL=info

# Log File Settings
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# =================================
# Security Settings
# =================================

# JWT Secret (for future authentication features)
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# Session Secret
SESSION_SECRET=your_session_secret_here

# =================================
# Trading Sessions Configuration
# =================================

# London Session
LONDON_SESSION_START=08:00
LONDON_SESSION_END=17:00
LONDON_SESSION_TIMEZONE=Europe/London

# New York Session
NEWYORK_SESSION_START=13:00
NEWYORK_SESSION_END=22:00
NEWYORK_SESSION_TIMEZONE=America/New_York

# Tokyo Session
TOKYO_SESSION_START=00:00
TOKYO_SESSION_END=09:00
TOKYO_SESSION_TIMEZONE=Asia/Tokyo

# Sydney Session
SYDNEY_SESSION_START=22:00
SYDNEY_SESSION_END=07:00
SYDNEY_SESSION_TIMEZONE=Australia/Sydney

# =================================
# Development Settings
# =================================

# Debug Mode
DEBUG_MODE=false
VERBOSE_LOGGING=false

# Mock Data (for testing without real API calls)
USE_MOCK_DATA=false

# Test Mode (disables real trading operations)
TEST_MODE=true

# =================================
# Feature Flags
# =================================

# Enable/Disable Features
FEATURE_AI_PREDICTIONS=true
FEATURE_VOLUME_ANALYSIS=true
FEATURE_PATTERN_RECOGNITION=true
FEATURE_SENTIMENT_ANALYSIS=true
FEATURE_ADVANCED_INDICATORS=true
FEATURE_WEBHOOK_INTEGRATION=true
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_TELEGRAM_NOTIFICATIONS=true
FOREX_FACTORY_API_KEY=your_forex_factory_key

# Risk Management
DEFAULT_RISK_PERCENTAGE=2
MAX_RISK_PERCENTAGE=5
MIN_RISK_REWARD_RATIO=1.5

# AI Model Configuration
AI_MODEL_PATH=./models/trading_model.json
RETRAIN_INTERVAL_HOURS=24
PREDICTION_CONFIDENCE_THRESHOLD=0.75

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/trading_bot.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Trading Configuration
SUPPORTED_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,USDCHF,NZDUSD,XAUUSD,BTCUSD,ETHUSD
DEFAULT_TIMEFRAME=1h
ANALYSIS_TIMEFRAMES=1m,5m,15m,1h,4h,1d

# Notification Settings
ENABLE_TELEGRAM_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
NOTIFICATION_COOLDOWN_MINUTES=5

# Security
CORS_ORIGIN=http://localhost:3000
HELMET_ENABLED=true
RATE_LIMITING_ENABLED=true
