import moment from 'moment-timezone';
import logger from '../utils/logger.js';
import config from '../config/config.js';

class MarketAnalyzer {
  constructor() {
    this.tradingSessions = config.tradingSessions;
    this.cache = new Map();
  }

  // Analyze current market state
  analyzeMarketState(marketData, symbol) {
    try {
      const currentTime = moment().utc();
      
      const analysis = {
        symbol,
        timestamp: currentTime.toISOString(),
        trend: this.analyzeTrend(marketData),
        session: this.getCurrentTradingSession(currentTime),
        liquidity: this.analyzeLiquidity(marketData),
        momentum: this.analyzeMomentum(marketData),
        volatility: this.analyzeVolatility(marketData),
        marketStructure: this.analyzeMarketStructure(marketData),
        sessionOverlap: this.getSessionOverlap(currentTime),
        timeAnalysis: this.analyzeTimeFactors(currentTime, symbol)
      };

      // Calculate overall market score
      analysis.marketScore = this.calculateMarketScore(analysis);
      
      // Generate market summary
      analysis.summary = this.generateMarketSummary(analysis);

      logger.analysis(`Market state analyzed for ${symbol}`, {
        trend: analysis.trend.direction,
        session: analysis.session.current,
        liquidity: analysis.liquidity.level,
        momentum: analysis.momentum.strength
      });

      return analysis;
    } catch (error) {
      logger.error('Market state analysis error:', error);
      return null;
    }
  }

  // Analyze market trend
  analyzeTrend(marketData) {
    try {
      const { highs, lows, closes } = marketData;
      const periods = [20, 50, 100]; // Short, medium, long term
      const trends = {};

      periods.forEach(period => {
        if (closes.length >= period) {
          const recentCloses = closes.slice(-period);
          const slope = this.calculateSlope(recentCloses);
          const strength = this.calculateTrendStrength(recentCloses);
          
          trends[`${period}period`] = {
            direction: slope > 0.001 ? 'bullish' : slope < -0.001 ? 'bearish' : 'sideways',
            slope: slope,
            strength: strength
          };
        }
      });

      // Determine overall trend
      const overallTrend = this.determineOverallTrend(trends);
      
      // Calculate trend consistency
      const consistency = this.calculateTrendConsistency(trends);

      return {
        overall: overallTrend,
        shortTerm: trends['20period'],
        mediumTerm: trends['50period'],
        longTerm: trends['100period'],
        consistency: consistency,
        alignment: this.checkTrendAlignment(trends)
      };
    } catch (error) {
      logger.error('Trend analysis error:', error);
      return { overall: 'unknown', consistency: 0 };
    }
  }

  // Get current trading session
  getCurrentTradingSession(currentTime) {
    try {
      const sessions = [];
      
      Object.entries(this.tradingSessions).forEach(([name, session]) => {
        const sessionStart = moment.tz(session.start, 'HH:mm', session.timezone);
        const sessionEnd = moment.tz(session.end, 'HH:mm', session.timezone);
        
        // Convert to UTC for comparison
        const startUTC = sessionStart.utc();
        const endUTC = sessionEnd.utc();
        
        // Handle sessions that cross midnight
        if (endUTC.isBefore(startUTC)) {
          endUTC.add(1, 'day');
        }
        
        const currentUTC = currentTime.clone();
        
        if (currentUTC.isBetween(startUTC, endUTC)) {
          sessions.push({
            name: name,
            start: startUTC.format('HH:mm'),
            end: endUTC.format('HH:mm'),
            timezone: session.timezone,
            active: true
          });
        }
      });

      // Determine primary session
      const primarySession = this.determinePrimarySession(sessions);
      
      return {
        current: primarySession?.name || 'off_hours',
        active: sessions,
        overlap: sessions.length > 1,
        overlapCount: sessions.length,
        nextSession: this.getNextSession(currentTime)
      };
    } catch (error) {
      logger.error('Trading session analysis error:', error);
      return { current: 'unknown', active: [], overlap: false };
    }
  }

  // Analyze market liquidity
  analyzeLiquidity(marketData) {
    try {
      const { volumes, highs, lows, closes } = marketData;
      
      if (!volumes || volumes.length === 0) {
        return {
          level: 'unknown',
          score: 0,
          reason: 'Volume data not available'
        };
      }

      const recentVolumes = volumes.slice(-20);
      const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
      const currentVolume = volumes[volumes.length - 1];
      
      // Calculate volume ratio
      const volumeRatio = currentVolume / avgVolume;
      
      // Calculate spread analysis
      const spreads = highs.slice(-20).map((high, i) => high - lows[lows.length - 20 + i]);
      const avgSpread = spreads.reduce((sum, spread) => sum + spread, 0) / spreads.length;
      const currentSpread = highs[highs.length - 1] - lows[lows.length - 1];
      const spreadRatio = currentSpread / avgSpread;

      // Determine liquidity level
      let level = 'normal';
      let score = 0.5;
      
      if (volumeRatio > 1.5 && spreadRatio < 1.2) {
        level = 'high';
        score = 0.8;
      } else if (volumeRatio < 0.5 || spreadRatio > 2) {
        level = 'low';
        score = 0.2;
      } else if (volumeRatio > 1.2 || spreadRatio < 0.8) {
        level = 'above_normal';
        score = 0.7;
      } else if (volumeRatio < 0.8 || spreadRatio > 1.5) {
        level = 'below_normal';
        score = 0.3;
      }

      return {
        level: level,
        score: score,
        volumeRatio: volumeRatio,
        spreadRatio: spreadRatio,
        currentVolume: currentVolume,
        avgVolume: avgVolume,
        assessment: this.generateLiquidityAssessment(level, volumeRatio, spreadRatio)
      };
    } catch (error) {
      logger.error('Liquidity analysis error:', error);
      return { level: 'unknown', score: 0 };
    }
  }

  // Analyze market momentum
  analyzeMomentum(marketData) {
    try {
      const { closes, volumes } = marketData;
      const periods = [5, 10, 20];
      const momentum = {};

      periods.forEach(period => {
        if (closes.length >= period + 1) {
          const currentPrice = closes[closes.length - 1];
          const pastPrice = closes[closes.length - 1 - period];
          const priceChange = (currentPrice - pastPrice) / pastPrice;
          
          momentum[`${period}period`] = {
            change: priceChange,
            direction: priceChange > 0 ? 'bullish' : 'bearish',
            strength: Math.abs(priceChange)
          };
        }
      });

      // Calculate volume-weighted momentum if volume available
      let volumeMomentum = null;
      if (volumes && volumes.length >= 10) {
        volumeMomentum = this.calculateVolumeMomentum(closes.slice(-10), volumes.slice(-10));
      }

      // Determine overall momentum
      const overallMomentum = this.determineOverallMomentum(momentum);
      
      return {
        overall: overallMomentum,
        shortTerm: momentum['5period'],
        mediumTerm: momentum['10period'],
        longTerm: momentum['20period'],
        volume: volumeMomentum,
        acceleration: this.calculateMomentumAcceleration(momentum),
        divergence: this.checkMomentumDivergence(closes.slice(-20), volumes?.slice(-20))
      };
    } catch (error) {
      logger.error('Momentum analysis error:', error);
      return { overall: { direction: 'neutral', strength: 0 } };
    }
  }

  // Analyze market volatility
  analyzeVolatility(marketData) {
    try {
      const { highs, lows, closes } = marketData;
      const periods = [10, 20, 50];
      const volatility = {};

      periods.forEach(period => {
        if (closes.length >= period) {
          const recentCloses = closes.slice(-period);
          const returns = this.calculateReturns(recentCloses);
          const stdDev = this.calculateStandardDeviation(returns);
          const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
          
          volatility[`${period}period`] = {
            standardDeviation: stdDev,
            coefficient: Math.abs(avgReturn) > 0 ? stdDev / Math.abs(avgReturn) : 0,
            level: this.classifyVolatility(stdDev)
          };
        }
      });

      // Calculate True Range based volatility
      const trueRanges = this.calculateTrueRanges(highs.slice(-20), lows.slice(-20), closes.slice(-20));
      const avgTrueRange = trueRanges.reduce((sum, tr) => sum + tr, 0) / trueRanges.length;
      const currentTrueRange = trueRanges[trueRanges.length - 1];

      return {
        current: volatility['10period'],
        shortTerm: volatility['10period'],
        mediumTerm: volatility['20period'],
        longTerm: volatility['50period'],
        trueRange: {
          current: currentTrueRange,
          average: avgTrueRange,
          ratio: currentTrueRange / avgTrueRange,
          level: this.classifyTrueRangeVolatility(currentTrueRange / avgTrueRange)
        },
        trend: this.analyzeVolatilityTrend(volatility),
        regime: this.determineVolatilityRegime(volatility)
      };
    } catch (error) {
      logger.error('Volatility analysis error:', error);
      return { current: { level: 'unknown' } };
    }
  }

  // Analyze market structure
  analyzeMarketStructure(marketData) {
    try {
      const { highs, lows, closes } = marketData;
      
      // Identify swing points
      const swingPoints = this.identifySwingPoints(highs, lows, 5);
      
      // Analyze structure patterns
      const patterns = this.identifyStructurePatterns(swingPoints);
      
      // Determine market phase
      const phase = this.determineMarketPhase(swingPoints, closes);
      
      // Calculate structure strength
      const strength = this.calculateStructureStrength(swingPoints);

      return {
        phase: phase,
        patterns: patterns,
        swingPoints: swingPoints.slice(-10), // Last 10 swing points
        strength: strength,
        breakouts: this.identifyRecentBreakouts(swingPoints, closes),
        support: this.identifyKeySupport(swingPoints, closes[closes.length - 1]),
        resistance: this.identifyKeyResistance(swingPoints, closes[closes.length - 1])
      };
    } catch (error) {
      logger.error('Market structure analysis error:', error);
      return { phase: 'unknown', patterns: [], strength: 0 };
    }
  }

  // Get session overlap information
  getSessionOverlap(currentTime) {
    try {
      const overlaps = [
        {
          name: 'London-New York',
          sessions: ['london', 'newYork'],
          description: 'High liquidity overlap',
          importance: 'high'
        },
        {
          name: 'Tokyo-London',
          sessions: ['tokyo', 'london'],
          description: 'Asian-European overlap',
          importance: 'medium'
        },
        {
          name: 'Sydney-Tokyo',
          sessions: ['sydney', 'tokyo'],
          description: 'Pacific overlap',
          importance: 'low'
        }
      ];

      const activeOverlaps = overlaps.filter(overlap => {
        return overlap.sessions.every(sessionName => {
          const session = this.tradingSessions[sessionName];
          if (!session) return false;
          
          const sessionStart = moment.tz(session.start, 'HH:mm', session.timezone).utc();
          const sessionEnd = moment.tz(session.end, 'HH:mm', session.timezone).utc();
          
          if (sessionEnd.isBefore(sessionStart)) {
            sessionEnd.add(1, 'day');
          }
          
          return currentTime.isBetween(sessionStart, sessionEnd);
        });
      });

      return {
        active: activeOverlaps,
        count: activeOverlaps.length,
        highImportance: activeOverlaps.filter(o => o.importance === 'high').length > 0,
        description: activeOverlaps.map(o => o.name).join(', ') || 'No active overlaps'
      };
    } catch (error) {
      logger.error('Session overlap analysis error:', error);
      return { active: [], count: 0, highImportance: false };
    }
  }

  // Analyze time-based factors
  analyzeTimeFactors(currentTime, symbol) {
    try {
      const dayOfWeek = currentTime.day(); // 0 = Sunday, 6 = Saturday
      const hour = currentTime.hour();
      
      // Determine market activity level based on time
      let activityLevel = 'normal';
      let timeRisk = 'normal';
      
      // Weekend or Friday late/Monday early
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        activityLevel = 'closed';
        timeRisk = 'high';
      } else if (dayOfWeek === 1 && hour < 6) {
        activityLevel = 'low';
        timeRisk = 'medium';
      } else if (dayOfWeek === 5 && hour > 20) {
        activityLevel = 'low';
        timeRisk = 'medium';
      }
      
      // News and event times (typically around major session opens)
      const newsRiskHours = [8, 9, 13, 14, 15]; // London open, US open, etc.
      if (newsRiskHours.includes(hour)) {
        timeRisk = 'high';
      }

      return {
        dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek],
        hour: hour,
        activityLevel: activityLevel,
        timeRisk: timeRisk,
        isWeekend: dayOfWeek === 0 || dayOfWeek === 6,
        isNewsTime: newsRiskHours.includes(hour),
        recommendation: this.getTimeBasedRecommendation(activityLevel, timeRisk)
      };
    } catch (error) {
      logger.error('Time factors analysis error:', error);
      return { activityLevel: 'unknown', timeRisk: 'unknown' };
    }
  }

  // Calculate overall market score
  calculateMarketScore(analysis) {
    try {
      let score = 0;
      let maxScore = 0;

      // Trend score (25%)
      if (analysis.trend.consistency) {
        score += analysis.trend.consistency * 25;
        maxScore += 25;
      }

      // Liquidity score (20%)
      if (analysis.liquidity.score) {
        score += analysis.liquidity.score * 20;
        maxScore += 20;
      }

      // Momentum score (20%)
      if (analysis.momentum.overall) {
        score += analysis.momentum.overall.strength * 20;
        maxScore += 20;
      }

      // Session score (15%)
      const sessionScore = analysis.session.overlap ? 0.8 : 0.5;
      score += sessionScore * 15;
      maxScore += 15;

      // Volatility score (10%)
      const volScore = analysis.volatility.current?.level === 'normal' ? 0.8 : 
                      analysis.volatility.current?.level === 'high' ? 0.6 : 0.4;
      score += volScore * 10;
      maxScore += 10;

      // Time factors score (10%)
      const timeScore = analysis.timeAnalysis.activityLevel === 'normal' ? 0.8 : 
                       analysis.timeAnalysis.activityLevel === 'low' ? 0.4 : 0.2;
      score += timeScore * 10;
      maxScore += 10;

      const finalScore = maxScore > 0 ? score / maxScore : 0;

      return {
        overall: Math.round(finalScore * 100) / 100,
        level: finalScore > 0.7 ? 'excellent' : 
               finalScore > 0.5 ? 'good' : 
               finalScore > 0.3 ? 'fair' : 'poor',
        components: {
          trend: analysis.trend.consistency * 100,
          liquidity: analysis.liquidity.score * 100,
          momentum: analysis.momentum.overall?.strength * 100 || 0,
          session: sessionScore * 100,
          volatility: volScore * 100,
          time: timeScore * 100
        }
      };
    } catch (error) {
      logger.error('Market score calculation error:', error);
      return { overall: 0, level: 'unknown' };
    }
  }

  // Generate market summary
  generateMarketSummary(analysis) {
    try {
      const summary = {
        headline: '',
        keyPoints: [],
        risks: [],
        opportunities: [],
        recommendation: ''
      };

      // Generate headline
      const trendDesc = analysis.trend.overall?.direction || 'neutral';
      const sessionDesc = analysis.session.current;
      const liquidityDesc = analysis.liquidity.level;
      
      summary.headline = `${trendDesc.toUpperCase()} trend during ${sessionDesc} session with ${liquidityDesc} liquidity`;

      // Key points
      if (analysis.trend.alignment) {
        summary.keyPoints.push('Multiple timeframes aligned');
      }
      
      if (analysis.session.overlap) {
        summary.keyPoints.push(`${analysis.sessionOverlap.count} session overlap active`);
      }
      
      if (analysis.momentum.overall?.strength > 0.6) {
        summary.keyPoints.push(`Strong ${analysis.momentum.overall.direction} momentum`);
      }

      // Risks
      if (analysis.volatility.current?.level === 'high') {
        summary.risks.push('High volatility environment');
      }
      
      if (analysis.timeAnalysis.timeRisk === 'high') {
        summary.risks.push('High-risk time period');
      }
      
      if (analysis.liquidity.level === 'low') {
        summary.risks.push('Low liquidity conditions');
      }

      // Opportunities
      if (analysis.marketScore.overall > 0.7) {
        summary.opportunities.push('Excellent market conditions for trading');
      }
      
      if (analysis.sessionOverlap.highImportance) {
        summary.opportunities.push('High-importance session overlap');
      }

      // Recommendation
      if (analysis.marketScore.overall > 0.6 && analysis.timeAnalysis.activityLevel === 'normal') {
        summary.recommendation = 'Favorable conditions for active trading';
      } else if (analysis.marketScore.overall < 0.3 || analysis.timeAnalysis.activityLevel === 'low') {
        summary.recommendation = 'Consider reducing trading activity';
      } else {
        summary.recommendation = 'Proceed with normal caution';
      }

      return summary;
    } catch (error) {
      logger.error('Market summary generation error:', error);
      return { headline: 'Analysis unavailable', keyPoints: [], risks: [], opportunities: [] };
    }
  }

  // Helper methods
  calculateSlope(values) {
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + (i * val), 0);
    const sumX2 = values.reduce((sum, val, i) => sum + (i * i), 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }

  calculateTrendStrength(values) {
    const slope = Math.abs(this.calculateSlope(values));
    const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;
    return Math.min(slope / avgValue, 1);
  }

  determineOverallTrend(trends) {
    const trendValues = Object.values(trends);
    const bullishCount = trendValues.filter(t => t.direction === 'bullish').length;
    const bearishCount = trendValues.filter(t => t.direction === 'bearish').length;
    
    if (bullishCount > bearishCount) {
      return { direction: 'bullish', strength: bullishCount / trendValues.length };
    } else if (bearishCount > bullishCount) {
      return { direction: 'bearish', strength: bearishCount / trendValues.length };
    } else {
      return { direction: 'sideways', strength: 0.5 };
    }
  }

  calculateTrendConsistency(trends) {
    const directions = Object.values(trends).map(t => t.direction);
    const uniqueDirections = [...new Set(directions)];
    return 1 - (uniqueDirections.length - 1) / 2; // 1 if all same, 0.5 if mixed
  }

  checkTrendAlignment(trends) {
    const directions = Object.values(trends).map(t => t.direction);
    return directions.every(dir => dir === directions[0]);
  }

  determinePrimarySession(sessions) {
    // Priority: London-NY overlap > London > NY > Tokyo > Sydney
    const priority = ['london', 'newYork', 'tokyo', 'sydney'];
    
    for (const sessionName of priority) {
      const session = sessions.find(s => s.name === sessionName);
      if (session) return session;
    }
    
    return sessions[0] || null;
  }

  getNextSession(currentTime) {
    // Implementation to find next trading session
    // This is a simplified version
    const sessions = ['sydney', 'tokyo', 'london', 'newYork'];
    return sessions[0]; // Placeholder
  }

  generateLiquidityAssessment(level, volumeRatio, spreadRatio) {
    if (level === 'high') {
      return 'Excellent trading conditions with high volume and tight spreads';
    } else if (level === 'low') {
      return 'Poor trading conditions - consider reducing position sizes';
    } else {
      return 'Normal trading conditions';
    }
  }

  determineOverallMomentum(momentum) {
    const periods = Object.values(momentum);
    const avgStrength = periods.reduce((sum, p) => sum + p.strength, 0) / periods.length;
    const bullishCount = periods.filter(p => p.direction === 'bullish').length;
    
    return {
      direction: bullishCount > periods.length / 2 ? 'bullish' : 'bearish',
      strength: avgStrength
    };
  }

  calculateVolumeMomentum(prices, volumes) {
    // Volume-weighted price momentum
    let weightedSum = 0;
    let volumeSum = 0;
    
    for (let i = 0; i < prices.length; i++) {
      weightedSum += prices[i] * volumes[i];
      volumeSum += volumes[i];
    }
    
    const vwap = weightedSum / volumeSum;
    const currentPrice = prices[prices.length - 1];
    
    return {
      vwap: vwap,
      momentum: (currentPrice - vwap) / vwap,
      direction: currentPrice > vwap ? 'bullish' : 'bearish'
    };
  }

  calculateMomentumAcceleration(momentum) {
    const periods = Object.keys(momentum).sort((a, b) => 
      parseInt(a.replace('period', '')) - parseInt(b.replace('period', ''))
    );
    
    if (periods.length < 2) return 0;
    
    const shortTerm = momentum[periods[0]].change;
    const longTerm = momentum[periods[periods.length - 1]].change;
    
    return shortTerm - longTerm; // Positive = accelerating
  }

  checkMomentumDivergence(prices, volumes) {
    if (!volumes || volumes.length < prices.length) return false;
    
    const priceSlope = this.calculateSlope(prices);
    const volumeSlope = this.calculateSlope(volumes);
    
    // Divergence if price and volume move in opposite directions
    return (priceSlope > 0 && volumeSlope < 0) || (priceSlope < 0 && volumeSlope > 0);
  }

  calculateReturns(prices) {
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    return returns;
  }

  calculateStandardDeviation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / squaredDiffs.length;
    return Math.sqrt(avgSquaredDiff);
  }

  classifyVolatility(stdDev) {
    if (stdDev > 0.02) return 'high';
    if (stdDev > 0.01) return 'medium';
    return 'low';
  }

  calculateTrueRanges(highs, lows, closes) {
    const trueRanges = [];
    for (let i = 1; i < highs.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    return trueRanges;
  }

  classifyTrueRangeVolatility(ratio) {
    if (ratio > 1.5) return 'high';
    if (ratio > 1.2) return 'above_normal';
    if (ratio < 0.8) return 'below_normal';
    if (ratio < 0.5) return 'low';
    return 'normal';
  }

  analyzeVolatilityTrend(volatility) {
    const periods = ['10period', '20period', '50period'];
    const values = periods.map(p => volatility[p]?.standardDeviation || 0);
    const slope = this.calculateSlope(values);
    
    return slope > 0.001 ? 'increasing' : slope < -0.001 ? 'decreasing' : 'stable';
  }

  determineVolatilityRegime(volatility) {
    const current = volatility['10period']?.standardDeviation || 0;
    const longTerm = volatility['50period']?.standardDeviation || 0;
    
    if (current > longTerm * 1.5) return 'high_vol_regime';
    if (current < longTerm * 0.5) return 'low_vol_regime';
    return 'normal_vol_regime';
  }

  identifySwingPoints(highs, lows, lookback = 5) {
    const swingPoints = [];
    
    for (let i = lookback; i < highs.length - lookback; i++) {
      const isSwingHigh = highs.slice(i - lookback, i + lookback + 1)
        .every((val, idx) => idx === lookback || val <= highs[i]);
      
      const isSwingLow = lows.slice(i - lookback, i + lookback + 1)
        .every((val, idx) => idx === lookback || val >= lows[i]);
      
      if (isSwingHigh) {
        swingPoints.push({ type: 'high', value: highs[i], index: i });
      }
      
      if (isSwingLow) {
        swingPoints.push({ type: 'low', value: lows[i], index: i });
      }
    }
    
    return swingPoints.sort((a, b) => a.index - b.index);
  }

  identifyStructurePatterns(swingPoints) {
    // Simplified pattern recognition
    const patterns = [];
    
    if (swingPoints.length >= 4) {
      const recent = swingPoints.slice(-4);
      
      // Higher highs and higher lows = uptrend
      if (this.isUptrend(recent)) {
        patterns.push({ type: 'uptrend', strength: 0.8 });
      }
      
      // Lower highs and lower lows = downtrend
      if (this.isDowntrend(recent)) {
        patterns.push({ type: 'downtrend', strength: 0.8 });
      }
      
      // Consolidation pattern
      if (this.isConsolidation(recent)) {
        patterns.push({ type: 'consolidation', strength: 0.6 });
      }
    }
    
    return patterns;
  }

  determineMarketPhase(swingPoints, closes) {
    if (swingPoints.length < 4) return 'unknown';
    
    const recent = swingPoints.slice(-4);
    
    if (this.isUptrend(recent)) return 'trending_up';
    if (this.isDowntrend(recent)) return 'trending_down';
    if (this.isConsolidation(recent)) return 'consolidating';
    
    return 'transitional';
  }

  calculateStructureStrength(swingPoints) {
    if (swingPoints.length < 4) return 0;
    
    const recent = swingPoints.slice(-4);
    const ranges = recent.map((point, i) => 
      i > 0 ? Math.abs(point.value - recent[i - 1].value) : 0
    ).slice(1);
    
    const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length;
    const consistency = 1 - (this.calculateStandardDeviation(ranges) / avgRange);
    
    return Math.max(0, Math.min(1, consistency));
  }

  identifyRecentBreakouts(swingPoints, closes) {
    // Simplified breakout detection
    const breakouts = [];
    const currentPrice = closes[closes.length - 1];
    const recentSwings = swingPoints.slice(-5);
    
    recentSwings.forEach(swing => {
      if (swing.type === 'high' && currentPrice > swing.value) {
        breakouts.push({
          type: 'resistance_break',
          level: swing.value,
          strength: (currentPrice - swing.value) / swing.value
        });
      } else if (swing.type === 'low' && currentPrice < swing.value) {
        breakouts.push({
          type: 'support_break',
          level: swing.value,
          strength: (swing.value - currentPrice) / swing.value
        });
      }
    });
    
    return breakouts;
  }

  identifyKeySupport(swingPoints, currentPrice) {
    return swingPoints
      .filter(point => point.type === 'low' && point.value < currentPrice)
      .sort((a, b) => b.value - a.value)
      .slice(0, 3);
  }

  identifyKeyResistance(swingPoints, currentPrice) {
    return swingPoints
      .filter(point => point.type === 'high' && point.value > currentPrice)
      .sort((a, b) => a.value - b.value)
      .slice(0, 3);
  }

  isUptrend(swingPoints) {
    const highs = swingPoints.filter(p => p.type === 'high');
    const lows = swingPoints.filter(p => p.type === 'low');
    
    return highs.length >= 2 && lows.length >= 2 &&
           highs[highs.length - 1].value > highs[highs.length - 2].value &&
           lows[lows.length - 1].value > lows[lows.length - 2].value;
  }

  isDowntrend(swingPoints) {
    const highs = swingPoints.filter(p => p.type === 'high');
    const lows = swingPoints.filter(p => p.type === 'low');
    
    return highs.length >= 2 && lows.length >= 2 &&
           highs[highs.length - 1].value < highs[highs.length - 2].value &&
           lows[lows.length - 1].value < lows[lows.length - 2].value;
  }

  isConsolidation(swingPoints) {
    const values = swingPoints.map(p => p.value);
    const range = Math.max(...values) - Math.min(...values);
    const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    return range / avgValue < 0.02; // Less than 2% range
  }

  getTimeBasedRecommendation(activityLevel, timeRisk) {
    if (activityLevel === 'closed') {
      return 'Markets closed - no trading recommended';
    } else if (timeRisk === 'high') {
      return 'High-risk time period - trade with extra caution';
    } else if (activityLevel === 'low') {
      return 'Low activity period - consider smaller positions';
    } else {
      return 'Normal trading conditions';
    }
  }
}

export default MarketAnalyzer;
