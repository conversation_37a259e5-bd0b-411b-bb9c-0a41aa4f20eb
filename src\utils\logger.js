import winston from 'winston';
import path from 'path';
import fs from 'fs';
import config from '../config/config.js';

// Create logs directory if it doesn't exist
const logsDir = path.dirname(config.logging.filePath);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      metaStr = ' ' + JSON.stringify(meta, null, 2);
    }
    return `${timestamp} [${level}]: ${message}${metaStr}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create the logger
const logger = winston.createLogger({
  level: config.logging.level,
  format: fileFormat,
  defaultMeta: { service: 'ayopfx-trading-bot' },
  transports: [
    // File transport for all logs
    new winston.transports.File({
      filename: config.logging.filePath,
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Separate file for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Add console transport in development
if (config.server.env !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Custom logging methods for trading-specific events
logger.trade = (message, data = {}) => {
  logger.info(message, { type: 'TRADE', ...data });
};

logger.signal = (message, data = {}) => {
  logger.info(message, { type: 'SIGNAL', ...data });
};

logger.analysis = (message, data = {}) => {
  logger.info(message, { type: 'ANALYSIS', ...data });
};

logger.notification = (message, data = {}) => {
  logger.info(message, { type: 'NOTIFICATION', ...data });
};

logger.ai = (message, data = {}) => {
  logger.info(message, { type: 'AI', ...data });
};

logger.api = (message, data = {}) => {
  logger.info(message, { type: 'API', ...data });
};

logger.performance = (message, data = {}) => {
  logger.info(message, { type: 'PERFORMANCE', ...data });
};

// Error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

export default logger;
