import TelegramBot from 'node-telegram-bot-api';
import nodemailer from 'nodemailer';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class AdvancedNotificationService extends EventEmitter {
  constructor() {
    super();
    this.telegramBot = null;
    this.emailTransporter = null;
    this.isReady = false;
    this.notificationQueue = [];
    this.rateLimits = new Map();
    
    // أنواع التنبيهات المختلفة
    this.notificationTypes = {
      SIGNAL_NEW: {
        name: 'إشارة جديدة',
        priority: 'high',
        channels: ['telegram', 'email', 'web'],
        cooldown: 0 // بدون تهدئة للإشارات الجديدة
      },
      SIGNAL_UPDATE: {
        name: 'تحديث الإشارة',
        priority: 'medium',
        channels: ['telegram', 'web'],
        cooldown: 300000 // 5 دقائق
      },
      POSITION_OPENED: {
        name: 'فتح مركز',
        priority: 'high',
        channels: ['telegram', 'email'],
        cooldown: 0
      },
      POSITION_CLOSED: {
        name: 'إغلاق مركز',
        priority: 'high',
        channels: ['telegram', 'email'],
        cooldown: 0
      },
      STOP_LOSS_HIT: {
        name: 'وقف الخسارة',
        priority: 'critical',
        channels: ['telegram', 'email', 'sms'],
        cooldown: 0
      },
      TAKE_PROFIT_HIT: {
        name: 'هدف الربح',
        priority: 'high',
        channels: ['telegram', 'email'],
        cooldown: 0
      },
      RISK_WARNING: {
        name: 'تحذير المخاطر',
        priority: 'critical',
        channels: ['telegram', 'email'],
        cooldown: 600000 // 10 دقائق
      },
      MARKET_NEWS: {
        name: 'أخبار السوق',
        priority: 'medium',
        channels: ['telegram'],
        cooldown: 1800000 // 30 دقيقة
      },
      SYSTEM_STATUS: {
        name: 'حالة النظام',
        priority: 'low',
        channels: ['email'],
        cooldown: 3600000 // ساعة واحدة
      },
      DAILY_REPORT: {
        name: 'التقرير اليومي',
        priority: 'medium',
        channels: ['email'],
        cooldown: 86400000 // 24 ساعة
      }
    };
  }

  // تهيئة خدمة التنبيهات
  async initialize() {
    try {
      logger.info('Initializing Advanced Notification Service...');

      // تهيئة Telegram Bot
      if (config.notifications?.telegram?.enabled) {
        await this.initializeTelegram();
      }

      // تهيئة البريد الإلكتروني
      if (config.notifications?.email?.enabled) {
        await this.initializeEmail();
      }

      // بدء معالج قائمة التنبيهات
      this.startNotificationProcessor();

      this.isReady = true;
      logger.info('✅ Advanced Notification Service initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ Advanced Notification Service initialization failed:', error);
      throw error;
    }
  }

  // تهيئة Telegram Bot
  async initializeTelegram() {
    try {
      const token = process.env.TELEGRAM_BOT_TOKEN;
      if (!token) {
        logger.warn('Telegram bot token not provided');
        return;
      }

      this.telegramBot = new TelegramBot(token, { polling: false });

      // اختبار الاتصال
      const botInfo = await this.telegramBot.getMe();
      logger.info(`Telegram bot connected: @${botInfo.username}`);

      // إعداد الأوامر
      await this.setupTelegramCommands();

    } catch (error) {
      logger.error('Failed to initialize Telegram bot:', error);
      this.telegramBot = null;
    }
  }

  // إعداد أوامر Telegram
  async setupTelegramCommands() {
    const commands = [
      { command: 'start', description: 'بدء البوت' },
      { command: 'status', description: 'حالة النظام' },
      { command: 'signals', description: 'الإشارات النشطة' },
      { command: 'portfolio', description: 'ملخص المحفظة' },
      { command: 'settings', description: 'الإعدادات' },
      { command: 'help', description: 'المساعدة' }
    ];

    try {
      await this.telegramBot.setMyCommands(commands);
      logger.info('Telegram commands set successfully');
    } catch (error) {
      logger.error('Failed to set Telegram commands:', error);
    }
  }

  // تهيئة البريد الإلكتروني
  async initializeEmail() {
    try {
      const emailConfig = {
        host: process.env.EMAIL_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.EMAIL_PORT) || 587,
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      };

      if (!emailConfig.auth.user || !emailConfig.auth.pass) {
        logger.warn('Email credentials not provided');
        return;
      }

      this.emailTransporter = nodemailer.createTransporter(emailConfig);

      // اختبار الاتصال
      await this.emailTransporter.verify();
      logger.info('Email service connected successfully');

    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      this.emailTransporter = null;
    }
  }

  // إرسال تنبيه
  async sendNotification(type, data, options = {}) {
    try {
      if (!this.isReady) {
        logger.warn('Notification service not ready');
        return false;
      }

      const notificationType = this.notificationTypes[type];
      if (!notificationType) {
        logger.error(`Unknown notification type: ${type}`);
        return false;
      }

      // فحص Rate Limiting
      if (await this.isRateLimited(type, data)) {
        logger.debug(`Notification rate limited: ${type}`);
        return false;
      }

      // إنشاء التنبيه
      const notification = {
        id: this.generateNotificationId(),
        type,
        data,
        options,
        timestamp: new Date().toISOString(),
        priority: notificationType.priority,
        channels: options.channels || notificationType.channels,
        attempts: 0,
        maxAttempts: 3
      };

      // إضافة للقائمة
      this.notificationQueue.push(notification);

      // تحديث Rate Limit
      await this.updateRateLimit(type, data);

      logger.info(`Notification queued: ${type}`, { id: notification.id });
      return true;

    } catch (error) {
      logger.error('Failed to send notification:', error);
      return false;
    }
  }

  // معالج قائمة التنبيهات
  startNotificationProcessor() {
    setInterval(async () => {
      if (this.notificationQueue.length === 0) return;

      // ترتيب حسب الأولوية
      this.notificationQueue.sort((a, b) => {
        const priorities = { critical: 4, high: 3, medium: 2, low: 1 };
        return priorities[b.priority] - priorities[a.priority];
      });

      const notification = this.notificationQueue.shift();
      await this.processNotification(notification);

    }, 1000); // معالجة كل ثانية
  }

  // معالجة تنبيه واحد
  async processNotification(notification) {
    try {
      const results = [];

      for (const channel of notification.channels) {
        let result = false;

        switch (channel) {
          case 'telegram':
            result = await this.sendTelegramNotification(notification);
            break;
          case 'email':
            result = await this.sendEmailNotification(notification);
            break;
          case 'web':
            result = await this.sendWebNotification(notification);
            break;
          case 'sms':
            result = await this.sendSMSNotification(notification);
            break;
        }

        results.push({ channel, success: result });
      }

      // تسجيل النتائج
      const successCount = results.filter(r => r.success).length;
      logger.info(`Notification processed: ${notification.id}`, {
        type: notification.type,
        channels: results,
        success: successCount > 0
      });

      // إعادة المحاولة في حالة الفشل
      if (successCount === 0 && notification.attempts < notification.maxAttempts) {
        notification.attempts++;
        setTimeout(() => {
          this.notificationQueue.unshift(notification);
        }, 5000 * notification.attempts); // تأخير متزايد
      }

    } catch (error) {
      logger.error('Failed to process notification:', error);
    }
  }

  // إرسال تنبيه Telegram
  async sendTelegramNotification(notification) {
    try {
      if (!this.telegramBot) return false;

      const chatId = process.env.TELEGRAM_CHAT_ID;
      if (!chatId) return false;

      const message = this.formatTelegramMessage(notification);
      const options = this.getTelegramOptions(notification);

      await this.telegramBot.sendMessage(chatId, message, options);
      return true;

    } catch (error) {
      logger.error('Failed to send Telegram notification:', error);
      return false;
    }
  }

  // تنسيق رسالة Telegram
  formatTelegramMessage(notification) {
    const { type, data } = notification;
    const typeInfo = this.notificationTypes[type];
    
    let message = `🤖 *AyoPFX Trading Bot*\n\n`;
    message += `📢 *${typeInfo.name}*\n`;
    message += `⏰ ${new Date().toLocaleString('ar-EG')}\n\n`;

    switch (type) {
      case 'SIGNAL_NEW':
        message += `💹 *${data.symbol}*\n`;
        message += `📈 الاتجاه: ${data.direction === 'buy' ? '🟢 شراء' : '🔴 بيع'}\n`;
        message += `💰 الدخول: ${data.entry.price}\n`;
        message += `🛑 وقف الخسارة: ${data.stopLoss.price}\n`;
        message += `🎯 الهدف: ${data.takeProfits[0]?.price}\n`;
        message += `📊 الثقة: ${(data.confidence * 100).toFixed(1)}%\n`;
        break;

      case 'POSITION_OPENED':
        message += `💼 تم فتح مركز جديد\n`;
        message += `💹 ${data.symbol} - ${data.direction === 'buy' ? 'شراء' : 'بيع'}\n`;
        message += `💰 الحجم: ${data.size} لوت\n`;
        message += `💵 السعر: ${data.entryPrice}\n`;
        break;

      case 'POSITION_CLOSED':
        message += `💼 تم إغلاق المركز\n`;
        message += `💹 ${data.symbol}\n`;
        message += `💰 الربح/الخسارة: ${data.pnl > 0 ? '🟢' : '🔴'} $${Math.abs(data.pnl).toFixed(2)}\n`;
        message += `📝 السبب: ${data.reason}\n`;
        break;

      case 'RISK_WARNING':
        message += `⚠️ *تحذير مخاطر*\n`;
        message += `📊 ${data.message}\n`;
        message += `💯 مستوى المخاطر: ${data.level}\n`;
        break;

      default:
        message += `📝 ${data.message || 'تنبيه عام'}\n`;
    }

    return message;
  }

  // خيارات Telegram
  getTelegramOptions(notification) {
    return {
      parse_mode: 'Markdown',
      disable_web_page_preview: true,
      reply_markup: this.getTelegramKeyboard(notification)
    };
  }

  // لوحة مفاتيح Telegram
  getTelegramKeyboard(notification) {
    const { type } = notification;
    
    const keyboards = {
      SIGNAL_NEW: {
        inline_keyboard: [
          [
            { text: '📊 التحليل', callback_data: `analysis_${notification.data.symbol}` },
            { text: '📈 الرسم البياني', callback_data: `chart_${notification.data.symbol}` }
          ],
          [
            { text: '✅ قبول', callback_data: `accept_${notification.id}` },
            { text: '❌ رفض', callback_data: `reject_${notification.id}` }
          ]
        ]
      },
      POSITION_OPENED: {
        inline_keyboard: [
          [
            { text: '📊 حالة المركز', callback_data: `position_${notification.data.id}` },
            { text: '🔄 تعديل', callback_data: `modify_${notification.data.id}` }
          ]
        ]
      }
    };

    return keyboards[type] || null;
  }

  // إرسال تنبيه بالبريد الإلكتروني
  async sendEmailNotification(notification) {
    try {
      if (!this.emailTransporter) return false;

      const emailContent = this.formatEmailContent(notification);
      
      const mailOptions = {
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
        to: process.env.EMAIL_TO || process.env.EMAIL_USER,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text
      };

      await this.emailTransporter.sendMail(mailOptions);
      return true;

    } catch (error) {
      logger.error('Failed to send email notification:', error);
      return false;
    }
  }

  // تنسيق محتوى البريد الإلكتروني
  formatEmailContent(notification) {
    const { type, data } = notification;
    const typeInfo = this.notificationTypes[type];
    
    const subject = `AyoPFX - ${typeInfo.name}`;
    
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center;">
          <h1>🤖 AyoPFX Trading Bot</h1>
          <h2>${typeInfo.name}</h2>
        </div>
        <div style="padding: 20px; background: #f8f9fa;">
    `;

    switch (type) {
      case 'SIGNAL_NEW':
        html += `
          <h3>إشارة تداول جديدة</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>زوج العملات:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${data.symbol}</td></tr>
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>الاتجاه:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${data.direction === 'buy' ? 'شراء' : 'بيع'}</td></tr>
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>سعر الدخول:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${data.entry.price}</td></tr>
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>وقف الخسارة:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${data.stopLoss.price}</td></tr>
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>هدف الربح:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${data.takeProfits[0]?.price}</td></tr>
            <tr><td style="padding: 8px; border: 1px solid #ddd;"><strong>مستوى الثقة:</strong></td><td style="padding: 8px; border: 1px solid #ddd;">${(data.confidence * 100).toFixed(1)}%</td></tr>
          </table>
        `;
        break;

      case 'DAILY_REPORT':
        html += `
          <h3>التقرير اليومي</h3>
          <p><strong>إجمالي الصفقات:</strong> ${data.totalTrades}</p>
          <p><strong>الصفقات الرابحة:</strong> ${data.winningTrades}</p>
          <p><strong>معدل النجاح:</strong> ${data.winRate.toFixed(1)}%</p>
          <p><strong>الربح/الخسارة:</strong> $${data.netProfit.toFixed(2)}</p>
        `;
        break;

      default:
        html += `<p>${data.message || 'تنبيه عام من نظام التداول'}</p>`;
    }

    html += `
        </div>
        <div style="background: #343a40; color: white; padding: 15px; text-align: center; font-size: 12px;">
          <p>هذا تنبيه تلقائي من AyoPFX Trading Bot</p>
          <p>الوقت: ${new Date().toLocaleString('ar-EG')}</p>
        </div>
      </div>
    `;

    const text = html.replace(/<[^>]*>/g, ''); // إزالة HTML للنص العادي

    return { subject, html, text };
  }

  // إرسال تنبيه ويب
  async sendWebNotification(notification) {
    try {
      // إرسال عبر Socket.IO إذا كان متاحاً
      if (global.io) {
        global.io.emit('notification', {
          id: notification.id,
          type: notification.type,
          data: notification.data,
          timestamp: notification.timestamp
        });
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to send web notification:', error);
      return false;
    }
  }

  // إرسال SMS (يحتاج تكامل مع خدمة SMS)
  async sendSMSNotification(notification) {
    try {
      // TODO: تكامل مع خدمة SMS مثل Twilio
      logger.info('SMS notification would be sent here');
      return true;
    } catch (error) {
      logger.error('Failed to send SMS notification:', error);
      return false;
    }
  }

  // فحص Rate Limiting
  async isRateLimited(type, data) {
    try {
      const notificationType = this.notificationTypes[type];
      if (!notificationType.cooldown) return false;

      const key = `rate_limit_${type}_${data.symbol || 'general'}`;
      const lastSent = await cacheUtils.get(key);
      
      if (lastSent) {
        const timeDiff = Date.now() - new Date(lastSent).getTime();
        return timeDiff < notificationType.cooldown;
      }

      return false;
    } catch (error) {
      logger.error('Error checking rate limit:', error);
      return false;
    }
  }

  // تحديث Rate Limit
  async updateRateLimit(type, data) {
    try {
      const notificationType = this.notificationTypes[type];
      if (!notificationType.cooldown) return;

      const key = `rate_limit_${type}_${data.symbol || 'general'}`;
      const ttl = Math.ceil(notificationType.cooldown / 1000);
      
      await cacheUtils.set(key, new Date().toISOString(), ttl);
    } catch (error) {
      logger.error('Error updating rate limit:', error);
    }
  }

  // إنشاء معرف التنبيه
  generateNotificationId() {
    return `NOTIF_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // إرسال تقرير يومي
  async sendDailyReport(reportData) {
    await this.sendNotification('DAILY_REPORT', reportData);
  }

  // إرسال تحذير مخاطر
  async sendRiskWarning(riskData) {
    await this.sendNotification('RISK_WARNING', riskData);
  }

  // إرسال تنبيه إشارة جديدة
  async sendSignalNotification(signal) {
    await this.sendNotification('SIGNAL_NEW', signal);
  }

  // إرسال تنبيه فتح مركز
  async sendPositionOpenedNotification(position) {
    await this.sendNotification('POSITION_OPENED', position);
  }

  // إرسال تنبيه إغلاق مركز
  async sendPositionClosedNotification(position, pnl, reason) {
    await this.sendNotification('POSITION_CLOSED', { ...position, pnl, reason });
  }
}

export default new AdvancedNotificationService();
