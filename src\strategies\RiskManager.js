import logger from '../utils/logger.js';
import config from '../config/config.js';

class RiskManager {
  constructor() {
    this.defaultRiskPercentage = config.risk.defaultRiskPercentage;
    this.maxRiskPercentage = config.risk.maxRiskPercentage;
    this.minRiskRewardRatio = config.risk.minRiskRewardRatio;
  }

  // Calculate position size based on risk parameters
  calculatePositionSize(accountBalance, entryPrice, stopLoss, riskPercentage = null) {
    try {
      const riskPercent = riskPercentage || this.defaultRiskPercentage;
      
      if (riskPercent > this.maxRiskPercentage) {
        logger.warn(`Risk percentage ${riskPercent}% exceeds maximum ${this.maxRiskPercentage}%`);
        return null;
      }

      const riskAmount = accountBalance * (riskPercent / 100);
      const stopLossDistance = Math.abs(entryPrice - stopLoss);
      
      if (stopLossDistance === 0) {
        logger.error('Stop loss distance cannot be zero');
        return null;
      }

      const positionSize = riskAmount / stopLossDistance;
      
      return {
        positionSize: Math.round(positionSize * 100000) / 100000, // Round to 5 decimal places
        riskAmount: riskAmount,
        riskPercentage: riskPercent,
        stopLossDistance: stopLossDistance,
        maxLoss: riskAmount
      };
    } catch (error) {
      logger.error('Position size calculation error:', error);
      return null;
    }
  }

  // Determine optimal entry price based on market analysis
  determineEntryPrice(analysis, signal) {
    try {
      const { technical, advanced, volume } = analysis;
      const currentPrice = this.getCurrentPrice(analysis);
      
      let entryPrice = currentPrice;
      let entryType = 'market';
      let confidence = 0.5;

      // Market order entry for strong signals
      if (signal.confluence && signal.confluence.score > 0.7) {
        entryPrice = currentPrice;
        entryType = 'market';
        confidence = 0.8;
      }
      // Limit order entry for medium signals
      else if (signal.confluence && signal.confluence.score > 0.4) {
        entryPrice = this.calculateLimitEntryPrice(currentPrice, signal.direction, technical);
        entryType = 'limit';
        confidence = 0.6;
      }
      // Wait for better setup
      else {
        return {
          action: 'wait',
          reason: 'Insufficient signal strength for entry',
          confidence: confidence
        };
      }

      // Adjust entry based on support/resistance levels
      if (advanced.supportResistance) {
        entryPrice = this.adjustEntryForSupportResistance(
          entryPrice, 
          signal.direction, 
          advanced.supportResistance,
          currentPrice
        );
      }

      // Adjust entry based on volume profile
      if (volume.available && volume.profile) {
        entryPrice = this.adjustEntryForVolumeProfile(
          entryPrice,
          signal.direction,
          volume.profile,
          currentPrice
        );
      }

      return {
        price: Math.round(entryPrice * 100000) / 100000,
        type: entryType,
        confidence: confidence,
        reasoning: this.generateEntryReasoning(signal, technical, advanced)
      };
    } catch (error) {
      logger.error('Entry price determination error:', error);
      return null;
    }
  }

  // Calculate stop loss levels
  calculateStopLoss(entryPrice, direction, analysis, atrMultiplier = 2) {
    try {
      const { technical, advanced } = analysis;
      const stopLossLevels = [];

      // ATR-based stop loss
      if (technical.atr) {
        const atrStopLoss = direction === 'buy' 
          ? entryPrice - (technical.atr.current * atrMultiplier)
          : entryPrice + (technical.atr.current * atrMultiplier);
        
        stopLossLevels.push({
          type: 'atr',
          price: atrStopLoss,
          distance: Math.abs(entryPrice - atrStopLoss),
          confidence: 0.7
        });
      }

      // Support/Resistance based stop loss
      if (advanced.supportResistance) {
        const srStopLoss = this.calculateSRStopLoss(entryPrice, direction, advanced.supportResistance);
        if (srStopLoss) {
          stopLossLevels.push(srStopLoss);
        }
      }

      // Supertrend based stop loss
      if (technical.supertrend) {
        const supertrendStopLoss = {
          type: 'supertrend',
          price: technical.supertrend.current,
          distance: Math.abs(entryPrice - technical.supertrend.current),
          confidence: 0.8
        };
        stopLossLevels.push(supertrendStopLoss);
      }

      // Order block based stop loss
      if (advanced.orderBlocks) {
        const obStopLoss = this.calculateOrderBlockStopLoss(entryPrice, direction, advanced.orderBlocks);
        if (obStopLoss) {
          stopLossLevels.push(obStopLoss);
        }
      }

      // Select the best stop loss
      const optimalStopLoss = this.selectOptimalStopLoss(stopLossLevels, entryPrice, direction);
      
      return optimalStopLoss;
    } catch (error) {
      logger.error('Stop loss calculation error:', error);
      return null;
    }
  }

  // Calculate take profit levels
  calculateTakeProfits(entryPrice, stopLoss, direction, analysis) {
    try {
      const { technical, advanced, volume } = analysis;
      const stopLossDistance = Math.abs(entryPrice - stopLoss);
      const takeProfits = [];

      // Calculate multiple take profit levels
      const baseTP1 = direction === 'buy' 
        ? entryPrice + (stopLossDistance * 1.5)
        : entryPrice - (stopLossDistance * 1.5);
      
      const baseTP2 = direction === 'buy' 
        ? entryPrice + (stopLossDistance * 2.5)
        : entryPrice - (stopLossDistance * 2.5);
      
      const baseTP3 = direction === 'buy' 
        ? entryPrice + (stopLossDistance * 4)
        : entryPrice - (stopLossDistance * 4);

      // TP1 - Conservative target
      let tp1 = baseTP1;
      if (advanced.supportResistance) {
        tp1 = this.adjustTPForSupportResistance(tp1, direction, advanced.supportResistance);
      }
      
      takeProfits.push({
        level: 'TP1',
        price: Math.round(tp1 * 100000) / 100000,
        riskReward: Math.abs(tp1 - entryPrice) / stopLossDistance,
        percentage: 50, // Close 50% of position
        confidence: 0.8
      });

      // TP2 - Moderate target
      let tp2 = baseTP2;
      if (volume.available && volume.profile) {
        tp2 = this.adjustTPForVolumeProfile(tp2, direction, volume.profile);
      }
      
      takeProfits.push({
        level: 'TP2',
        price: Math.round(tp2 * 100000) / 100000,
        riskReward: Math.abs(tp2 - entryPrice) / stopLossDistance,
        percentage: 30, // Close 30% of position
        confidence: 0.6
      });

      // TP3 - Aggressive target
      let tp3 = baseTP3;
      if (technical.supertrend) {
        // Use trend continuation for TP3
        tp3 = this.adjustTPForTrend(tp3, direction, technical);
      }
      
      takeProfits.push({
        level: 'TP3',
        price: Math.round(tp3 * 100000) / 100000,
        riskReward: Math.abs(tp3 - entryPrice) / stopLossDistance,
        percentage: 20, // Close remaining 20%
        confidence: 0.4
      });

      // Validate risk-reward ratios
      return takeProfits.filter(tp => tp.riskReward >= this.minRiskRewardRatio);
    } catch (error) {
      logger.error('Take profit calculation error:', error);
      return [];
    }
  }

  // Generate complete trade setup
  generateTradeSetup(analysis, accountBalance, customRisk = null) {
    try {
      const signal = analysis.signals[0]; // Use the strongest signal
      if (!signal) {
        return {
          action: 'no_trade',
          reason: 'No valid trading signals detected'
        };
      }

      // Determine entry
      const entry = this.determineEntryPrice(analysis, signal);
      if (!entry || entry.action === 'wait') {
        return entry;
      }

      // Calculate stop loss
      const stopLoss = this.calculateStopLoss(entry.price, signal.type, analysis);
      if (!stopLoss) {
        return {
          action: 'no_trade',
          reason: 'Unable to determine appropriate stop loss'
        };
      }

      // Calculate position size
      const positionInfo = this.calculatePositionSize(
        accountBalance, 
        entry.price, 
        stopLoss.price, 
        customRisk
      );
      if (!positionInfo) {
        return {
          action: 'no_trade',
          reason: 'Unable to calculate appropriate position size'
        };
      }

      // Calculate take profits
      const takeProfits = this.calculateTakeProfits(
        entry.price, 
        stopLoss.price, 
        signal.type, 
        analysis
      );

      // Calculate overall risk-reward
      const avgTakeProfit = takeProfits.reduce((sum, tp) => 
        sum + (tp.price * tp.percentage / 100), 0);
      const overallRiskReward = Math.abs(avgTakeProfit - entry.price) / 
        Math.abs(entry.price - stopLoss.price);

      const tradeSetup = {
        action: 'trade',
        direction: signal.type,
        entry: {
          price: entry.price,
          type: entry.type,
          confidence: entry.confidence
        },
        stopLoss: {
          price: stopLoss.price,
          type: stopLoss.type,
          distance: stopLoss.distance
        },
        takeProfits: takeProfits,
        position: positionInfo,
        riskReward: overallRiskReward,
        confidence: Math.min(entry.confidence + (analysis.confluence.score * 0.3), 1),
        reasoning: {
          entry: entry.reasoning,
          stopLoss: stopLoss.type,
          confluence: analysis.confluence.score,
          sentiment: analysis.sentiment.overall
        },
        metadata: {
          symbol: analysis.symbol,
          timeframe: analysis.timeframe,
          timestamp: new Date().toISOString(),
          analysisId: analysis.timestamp
        }
      };

      // Validate trade setup
      if (this.validateTradeSetup(tradeSetup)) {
        logger.trade('Trade setup generated', {
          symbol: analysis.symbol,
          direction: signal.type,
          entry: entry.price,
          stopLoss: stopLoss.price,
          riskReward: overallRiskReward,
          confidence: tradeSetup.confidence
        });
        
        return tradeSetup;
      } else {
        return {
          action: 'no_trade',
          reason: 'Trade setup failed validation'
        };
      }
    } catch (error) {
      logger.error('Trade setup generation error:', error);
      return {
        action: 'error',
        reason: 'Error generating trade setup',
        error: error.message
      };
    }
  }

  // Helper methods
  getCurrentPrice(analysis) {
    // Try to get current price from various sources
    if (analysis.technical.vwap) {
      return analysis.technical.vwap.current;
    }
    if (analysis.technical.ema20) {
      return analysis.technical.ema20.current;
    }
    // Fallback to a default price (this should be replaced with actual current price)
    return 1.0000;
  }

  calculateLimitEntryPrice(currentPrice, direction, technical) {
    // Calculate a slightly better entry price for limit orders
    const spread = currentPrice * 0.0001; // Approximate spread
    
    if (direction === 'buy') {
      return currentPrice - spread;
    } else {
      return currentPrice + spread;
    }
  }

  adjustEntryForSupportResistance(entryPrice, direction, supportResistance, currentPrice) {
    const relevantLevels = direction === 'buy' 
      ? supportResistance.support 
      : supportResistance.resistance;
    
    // Find the nearest level
    const nearestLevel = relevantLevels
      .filter(level => Math.abs(level.price - currentPrice) < currentPrice * 0.01) // Within 1%
      .sort((a, b) => Math.abs(a.price - currentPrice) - Math.abs(b.price - currentPrice))[0];
    
    if (nearestLevel) {
      // Adjust entry to be slightly beyond the level
      const adjustment = currentPrice * 0.0002; // 0.02%
      return direction === 'buy' 
        ? nearestLevel.price + adjustment
        : nearestLevel.price - adjustment;
    }
    
    return entryPrice;
  }

  adjustEntryForVolumeProfile(entryPrice, direction, volumeProfile, currentPrice) {
    const { poc, valueAreaHigh, valueAreaLow } = volumeProfile;
    
    // If price is near POC, use it as entry reference
    if (Math.abs(currentPrice - poc.priceLevel) < (valueAreaHigh - valueAreaLow) * 0.1) {
      return poc.priceLevel;
    }
    
    return entryPrice;
  }

  calculateSRStopLoss(entryPrice, direction, supportResistance) {
    const relevantLevels = direction === 'buy' 
      ? supportResistance.support 
      : supportResistance.resistance;
    
    // Find the nearest level below/above entry
    const protectiveLevel = relevantLevels
      .filter(level => direction === 'buy' ? level.price < entryPrice : level.price > entryPrice)
      .sort((a, b) => direction === 'buy' 
        ? b.price - a.price  // Highest support below entry
        : a.price - b.price  // Lowest resistance above entry
      )[0];
    
    if (protectiveLevel) {
      const buffer = entryPrice * 0.0001; // Small buffer
      return {
        type: 'support_resistance',
        price: direction === 'buy' 
          ? protectiveLevel.price - buffer
          : protectiveLevel.price + buffer,
        distance: Math.abs(entryPrice - protectiveLevel.price),
        confidence: protectiveLevel.strength || 0.6
      };
    }
    
    return null;
  }

  calculateOrderBlockStopLoss(entryPrice, direction, orderBlocks) {
    const relevantBlocks = orderBlocks.blocks
      .filter(block => block.type === direction && !block.tested)
      .filter(block => direction === 'buy' ? block.low < entryPrice : block.high > entryPrice);
    
    if (relevantBlocks.length > 0) {
      const nearestBlock = relevantBlocks[0];
      const buffer = entryPrice * 0.0001;
      
      return {
        type: 'order_block',
        price: direction === 'buy' 
          ? nearestBlock.low - buffer
          : nearestBlock.high + buffer,
        distance: Math.abs(entryPrice - (direction === 'buy' ? nearestBlock.low : nearestBlock.high)),
        confidence: nearestBlock.strength || 0.7
      };
    }
    
    return null;
  }

  selectOptimalStopLoss(stopLossLevels, entryPrice, direction) {
    if (stopLossLevels.length === 0) {
      // Fallback to percentage-based stop loss
      const fallbackDistance = entryPrice * 0.01; // 1%
      return {
        type: 'percentage',
        price: direction === 'buy' 
          ? entryPrice - fallbackDistance
          : entryPrice + fallbackDistance,
        distance: fallbackDistance,
        confidence: 0.5
      };
    }
    
    // Select stop loss with highest confidence and reasonable distance
    return stopLossLevels
      .filter(sl => sl.distance < entryPrice * 0.05) // Max 5% distance
      .sort((a, b) => b.confidence - a.confidence)[0] || stopLossLevels[0];
  }

  adjustTPForSupportResistance(tp, direction, supportResistance) {
    const relevantLevels = direction === 'buy' 
      ? supportResistance.resistance 
      : supportResistance.support;
    
    // Find resistance/support near the TP level
    const nearLevel = relevantLevels
      .filter(level => Math.abs(level.price - tp) < tp * 0.005) // Within 0.5%
      .sort((a, b) => Math.abs(a.price - tp) - Math.abs(b.price - tp))[0];
    
    if (nearLevel) {
      // Adjust TP to be just before the level
      const buffer = tp * 0.0001;
      return direction === 'buy' 
        ? nearLevel.price - buffer
        : nearLevel.price + buffer;
    }
    
    return tp;
  }

  adjustTPForVolumeProfile(tp, direction, volumeProfile) {
    const { valueAreaHigh, valueAreaLow } = volumeProfile;
    
    // If TP is near value area boundaries, use them
    if (direction === 'buy' && Math.abs(tp - valueAreaHigh) < tp * 0.005) {
      return valueAreaHigh * 0.999; // Just below value area high
    }
    if (direction === 'sell' && Math.abs(tp - valueAreaLow) < tp * 0.005) {
      return valueAreaLow * 1.001; // Just above value area low
    }
    
    return tp;
  }

  adjustTPForTrend(tp, direction, technical) {
    // Use trend strength to adjust TP3
    if (technical.supertrend && technical.supertrend.bullish === (direction === 'buy')) {
      // Strong trend, extend TP3
      const extension = tp * 0.002; // 0.2% extension
      return direction === 'buy' ? tp + extension : tp - extension;
    }
    
    return tp;
  }

  generateEntryReasoning(signal, technical, advanced) {
    const reasons = [];
    
    reasons.push(`${signal.indicator} signal: ${signal.reason}`);
    
    if (technical.supertrend && technical.supertrend.signal) {
      reasons.push(`Supertrend: ${technical.supertrend.signal}`);
    }
    
    if (advanced.choch && advanced.choch.lastSignal) {
      reasons.push('Change of Character detected');
    }
    
    return reasons.join('; ');
  }

  validateTradeSetup(tradeSetup) {
    // Basic validation checks
    if (!tradeSetup.entry || !tradeSetup.stopLoss) {
      return false;
    }
    
    if (tradeSetup.riskReward < this.minRiskRewardRatio) {
      logger.warn(`Risk-reward ratio ${tradeSetup.riskReward} below minimum ${this.minRiskRewardRatio}`);
      return false;
    }
    
    if (tradeSetup.confidence < 0.3) {
      logger.warn(`Trade confidence ${tradeSetup.confidence} too low`);
      return false;
    }
    
    return true;
  }
}

export default RiskManager;
