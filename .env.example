# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/ayopfx_trading_bot
REDIS_URL=redis://localhost:6379

# TradingView Configuration
TRADINGVIEW_API_KEY=your_tradingview_api_key
TRADINGVIEW_SECRET=your_tradingview_secret
TRADINGVIEW_WEBHOOK_URL=https://your-domain.com/webhook/tradingview

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
EMAIL_FROM=AyoPFX Trading Bot <<EMAIL>>

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# API Keys for Data Sources
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key
POLYGON_API_KEY=your_polygon_key
TWELVE_DATA_API_KEY=your_twelve_data_key

# Forex Factory Configuration
FOREX_FACTORY_API_KEY=your_forex_factory_key

# Risk Management
DEFAULT_RISK_PERCENTAGE=2
MAX_RISK_PERCENTAGE=5
MIN_RISK_REWARD_RATIO=1.5

# AI Model Configuration
AI_MODEL_PATH=./models/trading_model.json
RETRAIN_INTERVAL_HOURS=24
PREDICTION_CONFIDENCE_THRESHOLD=0.75

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/trading_bot.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Trading Configuration
SUPPORTED_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,USDCHF,NZDUSD,XAUUSD,BTCUSD,ETHUSD
DEFAULT_TIMEFRAME=1h
ANALYSIS_TIMEFRAMES=1m,5m,15m,1h,4h,1d

# Notification Settings
ENABLE_TELEGRAM_NOTIFICATIONS=true
ENABLE_EMAIL_NOTIFICATIONS=true
NOTIFICATION_COOLDOWN_MINUTES=5

# Security
CORS_ORIGIN=http://localhost:3000
HELMET_ENABLED=true
RATE_LIMITING_ENABLED=true
