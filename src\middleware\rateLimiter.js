import { RateLimiterRedis, RateLimiterMemory } from 'rate-limiter-flexible';
import { getRedisClient } from '../database/redis.js';
import config from '../config/config.js';
import logger from '../utils/logger.js';

let rateLimiter;

// Initialize rate limiter
const initializeRateLimiter = () => {
  try {
    // Try to use Redis if available
    const redisClient = getRedisClient();
    
    rateLimiter = new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'ayopfx_rl',
      points: config.rateLimiting.maxRequests, // Number of requests
      duration: Math.floor(config.rateLimiting.windowMs / 1000), // Per duration in seconds
      blockDuration: 60, // Block for 60 seconds if limit exceeded
      execEvenly: true, // Spread requests evenly across duration
    });

    logger.info('✅ Rate limiter initialized with Redis');
  } catch (error) {
    // Fallback to memory-based rate limiter
    rateLimiter = new RateLimiterMemory({
      keyPrefix: 'ayopfx_rl',
      points: config.rateLimiting.maxRequests,
      duration: Math.floor(config.rateLimiting.windowMs / 1000),
      blockDuration: 60,
      execEvenly: true,
    });

    logger.warn('⚠️ Rate limiter initialized with memory (Redis not available)');
  }
};

// Rate limiting middleware
const rateLimitMiddleware = async (req, res, next) => {
  if (!config.rateLimiting.enabled) {
    return next();
  }

  if (!rateLimiter) {
    initializeRateLimiter();
  }

  try {
    const key = req.ip || req.connection.remoteAddress;
    await rateLimiter.consume(key);
    next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set('Retry-After', String(secs));
    res.set('X-RateLimit-Limit', config.rateLimiting.maxRequests);
    res.set('X-RateLimit-Remaining', rejRes.remainingPoints || 0);
    res.set('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));

    logger.warn(`Rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: req.path,
      remainingPoints: rejRes.remainingPoints,
      msBeforeNext: rejRes.msBeforeNext
    });

    res.status(429).json({
      status: 'error',
      message: 'Too many requests, please try again later.',
      retryAfter: secs
    });
  }
};

// Specific rate limiters for different endpoints
const createSpecificRateLimiter = (points, duration, blockDuration = 60) => {
  try {
    const redisClient = getRedisClient();
    
    return new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'ayopfx_specific_rl',
      points,
      duration,
      blockDuration,
      execEvenly: true,
    });
  } catch (error) {
    return new RateLimiterMemory({
      keyPrefix: 'ayopfx_specific_rl',
      points,
      duration,
      blockDuration,
      execEvenly: true,
    });
  }
};

// Strict rate limiter for sensitive endpoints
const strictRateLimiter = createSpecificRateLimiter(5, 60, 300); // 5 requests per minute, block for 5 minutes

const strictRateLimitMiddleware = async (req, res, next) => {
  try {
    const key = req.ip || req.connection.remoteAddress;
    await strictRateLimiter.consume(key);
    next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set('Retry-After', String(secs));
    res.set('X-RateLimit-Limit', 5);
    res.set('X-RateLimit-Remaining', rejRes.remainingPoints || 0);
    res.set('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));

    logger.warn(`Strict rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: req.path,
      remainingPoints: rejRes.remainingPoints,
      msBeforeNext: rejRes.msBeforeNext
    });

    res.status(429).json({
      status: 'error',
      message: 'Too many requests to sensitive endpoint, please try again later.',
      retryAfter: secs
    });
  }
};

// API rate limiter for high-frequency endpoints
const apiRateLimiter = createSpecificRateLimiter(100, 60, 60); // 100 requests per minute

const apiRateLimitMiddleware = async (req, res, next) => {
  try {
    const key = req.ip || req.connection.remoteAddress;
    await apiRateLimiter.consume(key);
    next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set('Retry-After', String(secs));
    res.set('X-RateLimit-Limit', 100);
    res.set('X-RateLimit-Remaining', rejRes.remainingPoints || 0);
    res.set('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));

    logger.warn(`API rate limit exceeded for IP: ${req.ip}`, {
      ip: req.ip,
      path: req.path,
      remainingPoints: rejRes.remainingPoints,
      msBeforeNext: rejRes.msBeforeNext
    });

    res.status(429).json({
      status: 'error',
      message: 'API rate limit exceeded, please try again later.',
      retryAfter: secs
    });
  }
};

export default rateLimitMiddleware;
export { strictRateLimitMiddleware, apiRateLimitMiddleware };
