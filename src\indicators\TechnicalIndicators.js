import { 
  EMA, 
  RSI, 
  MACD, 
  BollingerBands,
  Stochastic,
  ATR,
  ADX
} from 'technicalindicators';
import logger from '../utils/logger.js';

class TechnicalIndicators {
  constructor() {
    this.cache = new Map();
  }

  // Exponential Moving Average
  calculateEMA(prices, period = 20) {
    try {
      if (!prices || prices.length < period) {
        throw new Error(`Insufficient data for EMA calculation. Need ${period} periods, got ${prices.length}`);
      }

      const emaValues = EMA.calculate({
        period: period,
        values: prices
      });

      return {
        values: emaValues,
        current: emaValues[emaValues.length - 1],
        previous: emaValues[emaValues.length - 2],
        trend: this.determineTrend(emaValues.slice(-5))
      };
    } catch (error) {
      logger.error('EMA calculation error:', error);
      return null;
    }
  }

  // Relative Strength Index
  calculateRSI(prices, period = 14) {
    try {
      if (!prices || prices.length < period + 1) {
        throw new Error(`Insufficient data for RSI calculation. Need ${period + 1} periods, got ${prices.length}`);
      }

      const rsiValues = RSI.calculate({
        period: period,
        values: prices
      });

      const current = rsiValues[rsiValues.length - 1];
      
      return {
        values: rsiValues,
        current: current,
        previous: rsiValues[rsiValues.length - 2],
        signal: this.getRSISignal(current),
        overbought: current > 70,
        oversold: current < 30,
        divergence: this.checkRSIDivergence(prices.slice(-10), rsiValues.slice(-10))
      };
    } catch (error) {
      logger.error('RSI calculation error:', error);
      return null;
    }
  }

  // MACD (Moving Average Convergence Divergence)
  calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
    try {
      if (!prices || prices.length < slowPeriod + signalPeriod) {
        throw new Error(`Insufficient data for MACD calculation`);
      }

      const macdData = MACD.calculate({
        fastPeriod: fastPeriod,
        slowPeriod: slowPeriod,
        signalPeriod: signalPeriod,
        values: prices,
        SimpleMAOscillator: false,
        SimpleMASignal: false
      });

      if (!macdData || macdData.length === 0) {
        return null;
      }

      const current = macdData[macdData.length - 1];
      const previous = macdData[macdData.length - 2];

      return {
        values: macdData,
        current: current,
        previous: previous,
        signal: this.getMACDSignal(current, previous),
        bullishCrossover: previous && current.MACD > current.signal && previous.MACD <= previous.signal,
        bearishCrossover: previous && current.MACD < current.signal && previous.MACD >= previous.signal,
        histogram: current.histogram,
        divergence: this.checkMACDDivergence(prices.slice(-10), macdData.slice(-10))
      };
    } catch (error) {
      logger.error('MACD calculation error:', error);
      return null;
    }
  }

  // Volume Weighted Average Price
  calculateVWAP(highs, lows, closes, volumes) {
    try {
      if (!highs || !lows || !closes || !volumes || 
          highs.length !== lows.length || 
          lows.length !== closes.length || 
          closes.length !== volumes.length) {
        throw new Error('Invalid data for VWAP calculation');
      }

      const vwapValues = [];
      let cumulativeTPV = 0; // Typical Price * Volume
      let cumulativeVolume = 0;

      for (let i = 0; i < closes.length; i++) {
        const typicalPrice = (highs[i] + lows[i] + closes[i]) / 3;
        const tpv = typicalPrice * volumes[i];
        
        cumulativeTPV += tpv;
        cumulativeVolume += volumes[i];
        
        const vwap = cumulativeVolume > 0 ? cumulativeTPV / cumulativeVolume : typicalPrice;
        vwapValues.push(vwap);
      }

      const current = vwapValues[vwapValues.length - 1];
      const currentPrice = closes[closes.length - 1];

      return {
        values: vwapValues,
        current: current,
        signal: currentPrice > current ? 'bullish' : 'bearish',
        deviation: ((currentPrice - current) / current) * 100,
        support: currentPrice > current,
        resistance: currentPrice < current
      };
    } catch (error) {
      logger.error('VWAP calculation error:', error);
      return null;
    }
  }

  // Supertrend Indicator
  calculateSupertrend(highs, lows, closes, period = 10, multiplier = 3) {
    try {
      if (!highs || !lows || !closes || 
          highs.length !== lows.length || 
          lows.length !== closes.length ||
          closes.length < period) {
        throw new Error('Invalid data for Supertrend calculation');
      }

      // Calculate ATR first
      const atrValues = this.calculateATR(highs, lows, closes, period);
      if (!atrValues) return null;

      const supertrend = [];
      const trend = [];
      
      for (let i = period - 1; i < closes.length; i++) {
        const hl2 = (highs[i] + lows[i]) / 2;
        const atr = atrValues.values[i - period + 1];
        
        const upperBand = hl2 + (multiplier * atr);
        const lowerBand = hl2 - (multiplier * atr);
        
        let currentTrend = 1; // 1 for uptrend, -1 for downtrend
        let supertrendValue;
        
        if (i === period - 1) {
          currentTrend = closes[i] > hl2 ? 1 : -1;
          supertrendValue = currentTrend === 1 ? lowerBand : upperBand;
        } else {
          const prevTrend = trend[trend.length - 1];
          const prevSupertrend = supertrend[supertrend.length - 1];
          
          if (prevTrend === 1) {
            currentTrend = closes[i] > lowerBand ? 1 : -1;
            supertrendValue = currentTrend === 1 ? Math.max(lowerBand, prevSupertrend) : upperBand;
          } else {
            currentTrend = closes[i] < upperBand ? -1 : 1;
            supertrendValue = currentTrend === -1 ? Math.min(upperBand, prevSupertrend) : lowerBand;
          }
        }
        
        supertrend.push(supertrendValue);
        trend.push(currentTrend);
      }

      const currentTrend = trend[trend.length - 1];
      const previousTrend = trend[trend.length - 2];
      
      return {
        values: supertrend,
        trend: trend,
        current: supertrend[supertrend.length - 1],
        signal: currentTrend === 1 ? 'bullish' : 'bearish',
        trendChange: previousTrend && currentTrend !== previousTrend,
        bullish: currentTrend === 1,
        bearish: currentTrend === -1
      };
    } catch (error) {
      logger.error('Supertrend calculation error:', error);
      return null;
    }
  }

  // Average True Range
  calculateATR(highs, lows, closes, period = 14) {
    try {
      if (!highs || !lows || !closes || 
          highs.length !== lows.length || 
          lows.length !== closes.length ||
          closes.length < period + 1) {
        throw new Error('Invalid data for ATR calculation');
      }

      const atrValues = ATR.calculate({
        period: period,
        high: highs,
        low: lows,
        close: closes
      });

      return {
        values: atrValues,
        current: atrValues[atrValues.length - 1],
        average: atrValues.reduce((sum, val) => sum + val, 0) / atrValues.length,
        volatility: this.getVolatilityLevel(atrValues[atrValues.length - 1], atrValues)
      };
    } catch (error) {
      logger.error('ATR calculation error:', error);
      return null;
    }
  }

  // Helper methods
  determineTrend(values) {
    if (!values || values.length < 3) return 'neutral';
    
    const recent = values.slice(-3);
    const increasing = recent.every((val, i) => i === 0 || val > recent[i - 1]);
    const decreasing = recent.every((val, i) => i === 0 || val < recent[i - 1]);
    
    if (increasing) return 'bullish';
    if (decreasing) return 'bearish';
    return 'neutral';
  }

  getRSISignal(rsi) {
    if (rsi > 70) return 'overbought';
    if (rsi < 30) return 'oversold';
    if (rsi > 50) return 'bullish';
    return 'bearish';
  }

  getMACDSignal(current, previous) {
    if (!current || !previous) return 'neutral';
    
    if (current.MACD > current.signal) {
      return current.MACD > previous.MACD ? 'strong_bullish' : 'bullish';
    } else {
      return current.MACD < previous.MACD ? 'strong_bearish' : 'bearish';
    }
  }

  checkRSIDivergence(prices, rsiValues) {
    if (!prices || !rsiValues || prices.length < 5 || rsiValues.length < 5) {
      return { bullish: false, bearish: false };
    }

    const priceSlope = this.calculateSlope(prices.slice(-5));
    const rsiSlope = this.calculateSlope(rsiValues.slice(-5));

    return {
      bullish: priceSlope < 0 && rsiSlope > 0, // Price falling, RSI rising
      bearish: priceSlope > 0 && rsiSlope < 0   // Price rising, RSI falling
    };
  }

  checkMACDDivergence(prices, macdValues) {
    if (!prices || !macdValues || prices.length < 5 || macdValues.length < 5) {
      return { bullish: false, bearish: false };
    }

    const priceSlope = this.calculateSlope(prices.slice(-5));
    const macdSlope = this.calculateSlope(macdValues.slice(-5).map(m => m.MACD));

    return {
      bullish: priceSlope < 0 && macdSlope > 0,
      bearish: priceSlope > 0 && macdSlope < 0
    };
  }

  calculateSlope(values) {
    if (!values || values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + (i * val), 0);
    const sumX2 = values.reduce((sum, val, i) => sum + (i * i), 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }

  getVolatilityLevel(currentATR, atrHistory) {
    if (!atrHistory || atrHistory.length === 0) return 'normal';
    
    const average = atrHistory.reduce((sum, val) => sum + val, 0) / atrHistory.length;
    const ratio = currentATR / average;
    
    if (ratio > 1.5) return 'high';
    if (ratio < 0.5) return 'low';
    return 'normal';
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
  }
}

export default TechnicalIndicators;
