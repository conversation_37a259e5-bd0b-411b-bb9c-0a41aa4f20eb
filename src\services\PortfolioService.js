import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class PortfolioService extends EventEmitter {
  constructor() {
    super();
    this.positions = new Map();
    this.portfolio = {
      balance: 10000, // Starting balance
      equity: 10000,
      margin: 0,
      freeMargin: 10000,
      marginLevel: 0,
      totalProfit: 0,
      totalLoss: 0,
      winRate: 0,
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0
    };
    this.riskSettings = {
      maxRiskPerTrade: 0.02, // 2% per trade
      maxDailyRisk: 0.06, // 6% per day
      maxDrawdown: 0.20, // 20% max drawdown
      maxPositions: 5, // Max concurrent positions
      riskRewardRatio: 2.0 // Minimum risk/reward ratio
    };
  }

  // Initialize portfolio service
  async initialize() {
    try {
      logger.info('Initializing Portfolio Service...');
      
      // Load portfolio data from cache
      await this.loadPortfolioData();
      
      logger.info('✅ Portfolio Service initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Portfolio Service initialization failed:', error);
      throw error;
    }
  }

  // Load portfolio data from cache
  async loadPortfolioData() {
    try {
      const cachedPortfolio = await cacheUtils.get('portfolio_data');
      if (cachedPortfolio) {
        this.portfolio = { ...this.portfolio, ...cachedPortfolio };
      }

      const cachedPositions = await cacheUtils.get('open_positions');
      if (cachedPositions) {
        this.positions = new Map(cachedPositions);
      }
    } catch (error) {
      logger.error('Error loading portfolio data:', error);
    }
  }

  // Save portfolio data to cache
  async savePortfolioData() {
    try {
      await cacheUtils.set('portfolio_data', this.portfolio, 3600);
      await cacheUtils.set('open_positions', Array.from(this.positions.entries()), 3600);
    } catch (error) {
      logger.error('Error saving portfolio data:', error);
    }
  }

  // Calculate position size based on risk management
  calculatePositionSize(signal) {
    try {
      const { entry, stopLoss, symbol } = signal;
      const accountBalance = this.portfolio.balance;
      const riskAmount = accountBalance * this.riskSettings.maxRiskPerTrade;
      
      // Calculate pip value (simplified for major pairs)
      const pipValue = this.getPipValue(symbol);
      
      // Calculate stop loss distance in pips
      const stopLossDistance = Math.abs(entry.price - stopLoss.price) * 10000; // Convert to pips
      
      // Calculate position size
      const positionSize = riskAmount / (stopLossDistance * pipValue);
      
      // Apply maximum position size limits
      const maxPositionSize = accountBalance * 0.1; // Max 10% of balance per trade
      
      return {
        size: Math.min(positionSize, maxPositionSize),
        riskAmount,
        stopLossDistance,
        pipValue,
        maxSize: maxPositionSize
      };
    } catch (error) {
      logger.error('Error calculating position size:', error);
      return null;
    }
  }

  // Get pip value for currency pair
  getPipValue(symbol) {
    // Simplified pip values for major pairs (per 1 lot)
    const pipValues = {
      'EURUSD': 10,
      'GBPUSD': 10,
      'AUDUSD': 10,
      'NZDUSD': 10,
      'USDCAD': 10,
      'USDCHF': 10,
      'USDJPY': 10,
      'EURJPY': 10,
      'GBPJPY': 10,
      'XAUUSD': 1 // Gold
    };
    
    return pipValues[symbol] || 10;
  }

  // Open a new position
  async openPosition(signal) {
    try {
      // Check if we can open new positions
      if (this.positions.size >= this.riskSettings.maxPositions) {
        throw new Error('Maximum number of positions reached');
      }

      // Calculate position size
      const positionCalc = this.calculatePositionSize(signal);
      if (!positionCalc) {
        throw new Error('Unable to calculate position size');
      }

      // Create position object
      const position = {
        id: this.generatePositionId(),
        symbol: signal.metadata.symbol,
        direction: signal.direction,
        entryPrice: signal.entry.price,
        stopLoss: signal.stopLoss.price,
        takeProfits: signal.takeProfits,
        size: positionCalc.size,
        openTime: new Date().toISOString(),
        status: 'open',
        currentPrice: signal.entry.price,
        unrealizedPnL: 0,
        commission: this.calculateCommission(positionCalc.size),
        swap: 0,
        confidence: signal.confidence
      };

      // Add position to portfolio
      this.positions.set(position.id, position);

      // Update portfolio metrics
      this.updatePortfolioMetrics();

      // Save data
      await this.savePortfolioData();

      logger.info('Position opened', {
        id: position.id,
        symbol: position.symbol,
        direction: position.direction,
        size: position.size
      });

      this.emit('positionOpened', position);
      return position;
    } catch (error) {
      logger.error('Error opening position:', error);
      throw error;
    }
  }

  // Close a position
  async closePosition(positionId, closePrice, reason = 'manual') {
    try {
      const position = this.positions.get(positionId);
      if (!position) {
        throw new Error('Position not found');
      }

      // Calculate final P&L
      const pnl = this.calculatePnL(position, closePrice);

      // Update position
      position.status = 'closed';
      position.closePrice = closePrice;
      position.closeTime = new Date().toISOString();
      position.realizedPnL = pnl;
      position.closeReason = reason;

      // Update portfolio
      this.portfolio.equity += pnl;
      this.portfolio.totalTrades++;

      if (pnl > 0) {
        this.portfolio.totalProfit += pnl;
        this.portfolio.winningTrades++;
      } else {
        this.portfolio.totalLoss += Math.abs(pnl);
        this.portfolio.losingTrades++;
      }

      // Calculate win rate
      this.portfolio.winRate = (this.portfolio.winningTrades / this.portfolio.totalTrades) * 100;

      // Remove from active positions
      this.positions.delete(positionId);

      // Update metrics
      this.updatePortfolioMetrics();

      // Save data
      await this.savePortfolioData();

      logger.info('Position closed', {
        id: positionId,
        pnl,
        reason
      });

      this.emit('positionClosed', { position, pnl, reason });
      return { position, pnl };
    } catch (error) {
      logger.error('Error closing position:', error);
      throw error;
    }
  }

  // Calculate P&L for a position
  calculatePnL(position, currentPrice) {
    const { direction, entryPrice, size } = position;
    const priceChange = currentPrice - entryPrice;
    
    let pnl = 0;
    if (direction === 'buy') {
      pnl = priceChange * size * 100000; // Convert to account currency
    } else {
      pnl = -priceChange * size * 100000;
    }

    // Subtract commission and swap
    pnl -= position.commission + position.swap;

    return pnl;
  }

  // Update unrealized P&L for all positions
  async updateUnrealizedPnL(marketPrices) {
    try {
      let totalUnrealizedPnL = 0;

      for (const [id, position] of this.positions) {
        const currentPrice = marketPrices[position.symbol];
        if (currentPrice) {
          position.currentPrice = currentPrice;
          position.unrealizedPnL = this.calculatePnL(position, currentPrice);
          totalUnrealizedPnL += position.unrealizedPnL;

          // Check for stop loss or take profit
          await this.checkPositionLevels(position);
        }
      }

      // Update portfolio equity
      this.portfolio.equity = this.portfolio.balance + totalUnrealizedPnL;
      
      // Update metrics
      this.updatePortfolioMetrics();

      return totalUnrealizedPnL;
    } catch (error) {
      logger.error('Error updating unrealized P&L:', error);
      return 0;
    }
  }

  // Check if position should be closed due to SL/TP
  async checkPositionLevels(position) {
    try {
      const { direction, currentPrice, stopLoss, takeProfits } = position;

      // Check stop loss
      if (direction === 'buy' && currentPrice <= stopLoss) {
        await this.closePosition(position.id, stopLoss, 'stop_loss');
        return;
      } else if (direction === 'sell' && currentPrice >= stopLoss) {
        await this.closePosition(position.id, stopLoss, 'stop_loss');
        return;
      }

      // Check take profits
      for (const tp of takeProfits) {
        if (direction === 'buy' && currentPrice >= tp.price) {
          await this.closePosition(position.id, tp.price, 'take_profit');
          return;
        } else if (direction === 'sell' && currentPrice <= tp.price) {
          await this.closePosition(position.id, tp.price, 'take_profit');
          return;
        }
      }
    } catch (error) {
      logger.error('Error checking position levels:', error);
    }
  }

  // Update portfolio metrics
  updatePortfolioMetrics() {
    // Calculate margin used
    let marginUsed = 0;
    for (const position of this.positions.values()) {
      marginUsed += position.size * position.currentPrice * 0.01; // 1% margin requirement
    }

    this.portfolio.margin = marginUsed;
    this.portfolio.freeMargin = this.portfolio.equity - marginUsed;
    this.portfolio.marginLevel = marginUsed > 0 ? (this.portfolio.equity / marginUsed) * 100 : 0;
  }

  // Calculate commission
  calculateCommission(size) {
    return size * 7; // $7 per lot (simplified)
  }

  // Generate unique position ID
  generatePositionId() {
    return `POS_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get portfolio summary
  getPortfolioSummary() {
    return {
      ...this.portfolio,
      openPositions: this.positions.size,
      positions: Array.from(this.positions.values()),
      riskSettings: this.riskSettings,
      timestamp: new Date().toISOString()
    };
  }

  // Get risk analysis
  getRiskAnalysis() {
    const totalRisk = Array.from(this.positions.values())
      .reduce((total, pos) => total + Math.abs(pos.unrealizedPnL), 0);

    const dailyRisk = totalRisk / this.portfolio.balance;
    const drawdown = (this.portfolio.balance - this.portfolio.equity) / this.portfolio.balance;

    return {
      totalRisk,
      dailyRiskPercentage: dailyRisk * 100,
      maxDailyRisk: this.riskSettings.maxDailyRisk * 100,
      currentDrawdown: drawdown * 100,
      maxDrawdown: this.riskSettings.maxDrawdown * 100,
      riskStatus: this.getRiskStatus(dailyRisk, drawdown),
      timestamp: new Date().toISOString()
    };
  }

  // Get risk status
  getRiskStatus(dailyRisk, drawdown) {
    if (dailyRisk > this.riskSettings.maxDailyRisk || drawdown > this.riskSettings.maxDrawdown) {
      return 'high';
    } else if (dailyRisk > this.riskSettings.maxDailyRisk * 0.7 || drawdown > this.riskSettings.maxDrawdown * 0.7) {
      return 'medium';
    } else {
      return 'low';
    }
  }
}

export default new PortfolioService();
