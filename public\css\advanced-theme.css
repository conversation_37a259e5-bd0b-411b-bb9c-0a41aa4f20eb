/* AyoPFX Trading Bot - Advanced Theme */

:root {
  /* Primary Colors */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --primary-light: #7c8ef0;
  
  /* Secondary Colors */
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --secondary-color: #f093fb;
  
  /* Success/Danger Colors */
  --success-color: #28a745;
  --success-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  --danger-color: #dc3545;
  --danger-gradient: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
  --warning-color: #ffc107;
  --warning-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  
  /* Neutral Colors */
  --dark-bg: #1a1d29;
  --dark-card: #252836;
  --dark-border: #3a3f51;
  --light-text: #ffffff;
  --muted-text: #8b92b2;
  
  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Animations */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: var(--primary-gradient);
  color: var(--light-text);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(5deg); }
}

/* Glass Card Effect */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* Navigation Styles */
.navbar {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-tabs {
  border: none;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 0.5rem;
  margin-bottom: 2rem;
}

.nav-tabs .nav-link {
  border: none;
  color: var(--muted-text);
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  margin: 0 0.25rem;
  transition: var(--transition-smooth);
  font-weight: 500;
}

.nav-tabs .nav-link:hover {
  color: var(--light-text);
  background: rgba(255, 255, 255, 0.1);
}

.nav-tabs .nav-link.active {
  background: var(--primary-gradient);
  color: var(--light-text);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* Card Styles */
.card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  box-shadow: var(--glass-shadow);
  transition: var(--transition-smooth);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.card-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-bottom: 1px solid var(--glass-border);
  padding: 1.25rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

/* Button Styles */
.btn-primary-custom {
  background: var(--primary-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  color: white;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary-custom:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, #7c8ef0 0%, #8a5fb8 100%);
}

.btn-success-custom {
  background: var(--success-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  color: white;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-danger-custom {
  background: var(--danger-gradient);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  color: white;
  transition: var(--transition-smooth);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

/* Status Indicators */
.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 8px;
  animation: pulse 2s infinite;
}

.status-online {
  background: var(--success-color);
  box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-offline {
  background: var(--danger-color);
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

/* Badge Styles */
.badge {
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  font-size: 0.85rem;
}

.badge-success-custom {
  background: var(--success-gradient);
  color: white;
}

.badge-danger-custom {
  background: var(--danger-gradient);
  color: white;
}

.badge-warning-custom {
  background: var(--warning-gradient);
  color: white;
}

.badge-primary-custom {
  background: var(--primary-gradient);
  color: white;
}

/* Progress Bars */
.progress {
  height: 8px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.progress-bar {
  border-radius: 10px;
  transition: var(--transition-smooth);
}

.progress-bar-success {
  background: var(--success-gradient);
}

.progress-bar-danger {
  background: var(--danger-gradient);
}

.progress-bar-warning {
  background: var(--warning-gradient);
}

/* Chart Container */
.chart-container {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 1.5rem;
  margin: 1rem 0;
  border: 1px solid var(--glass-border);
}

/* Signal Cards */
.signal-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.signal-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-gradient);
}

.signal-card.buy::before {
  background: var(--success-gradient);
}

.signal-card.sell::before {
  background: var(--danger-gradient);
}

.signal-card:hover {
  transform: translateX(5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Confidence Meter */
.confidence-meter {
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.confidence-fill {
  height: 100%;
  border-radius: 10px;
  background: var(--primary-gradient);
  transition: width 1s ease-in-out;
  position: relative;
}

.confidence-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Loading Animations */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 15px;
  padding: 1rem 1.5rem;
  color: white;
  z-index: 9999;
  transform: translateX(400px);
  transition: var(--transition-bounce);
  box-shadow: var(--glass-shadow);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--success-color);
}

.notification.error {
  border-left: 4px solid var(--danger-color);
}

.notification.warning {
  border-left: 4px solid var(--warning-color);
}

/* Form Styles */
.form-control {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  border-radius: 10px;
  color: white;
  padding: 0.75rem 1rem;
  transition: var(--transition-smooth);
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  color: white;
}

.form-control::placeholder {
  color: var(--muted-text);
}

.form-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  border-radius: 10px;
  color: white;
  padding: 0.75rem 1rem;
}

.form-label {
  color: var(--light-text);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Table Styles */
.table {
  color: white;
  background: transparent;
}

.table th {
  border-color: var(--glass-border);
  background: rgba(255, 255, 255, 0.05);
  font-weight: 600;
}

.table td {
  border-color: var(--glass-border);
}

.table-hover tbody tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #7c8ef0 0%, #8a5fb8 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }
  
  .nav-tabs .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .btn-primary-custom,
  .btn-success-custom,
  .btn-danger-custom {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .signal-card {
    padding: 1rem;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
  }
}

/* Print Styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass-card,
  .card {
    background: white !important;
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
}
