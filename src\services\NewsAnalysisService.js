import axios from 'axios';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import { cacheUtils } from '../database/redis.js';

class NewsAnalysisService extends EventEmitter {
  constructor() {
    super();
    this.isReady = false;
    this.newsProviders = new Map();
    this.sentimentKeywords = {
      positive: [
        'صعود', 'ارتفاع', 'نمو', 'تحسن', 'إيجابي', 'قوي', 'مكاسب', 'تفاؤل',
        'bullish', 'rise', 'growth', 'positive', 'strong', 'gains', 'optimistic'
      ],
      negative: [
        'هبوط', 'انخفاض', 'تراجع', 'ضعف', 'سلبي', 'خسائر', 'تشاؤم', 'أزمة',
        'bearish', 'fall', 'decline', 'negative', 'weak', 'losses', 'pessimistic', 'crisis'
      ],
      neutral: [
        'استقرار', 'ثبات', 'توازن', 'محايد', 'انتظار',
        'stable', 'steady', 'balanced', 'neutral', 'waiting'
      ]
    };
  }

  // Initialize the news analysis service
  async initialize() {
    try {
      logger.info('Initializing News Analysis Service...');

      // Initialize news providers
      this.initializeNewsProviders();

      this.isReady = true;
      logger.info('✅ News Analysis Service initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ News Analysis Service initialization failed:', error);
      throw error;
    }
  }

  // Initialize news providers
  initializeNewsProviders() {
    // Add news providers
    this.newsProviders.set('forexFactory', {
      name: 'Forex Factory',
      url: 'https://www.forexfactory.com/calendar',
      enabled: true
    });

    this.newsProviders.set('investing', {
      name: 'Investing.com',
      url: 'https://www.investing.com/news/forex-news',
      enabled: true
    });

    this.newsProviders.set('fxstreet', {
      name: 'FXStreet',
      url: 'https://www.fxstreet.com/news',
      enabled: true
    });
  }

  // Analyze market sentiment from news
  async analyzeMarketSentiment(symbol = 'EURUSD') {
    try {
      if (!this.isReady) {
        throw new Error('News Analysis Service not ready');
      }

      const cacheKey = `news_sentiment_${symbol}`;
      
      // Check cache first
      const cachedSentiment = await cacheUtils.get(cacheKey);
      if (cachedSentiment) {
        return cachedSentiment;
      }

      // Get news data
      const newsData = await this.fetchNewsData(symbol);
      
      if (!newsData || newsData.length === 0) {
        return this.getDefaultSentiment();
      }

      // Analyze sentiment
      const sentiment = this.calculateSentiment(newsData);

      // Cache the result for 30 minutes
      await cacheUtils.set(cacheKey, sentiment, 1800);

      logger.info('Market sentiment analyzed', {
        symbol,
        sentiment: sentiment.overall,
        confidence: sentiment.confidence
      });

      return sentiment;
    } catch (error) {
      logger.error('Market sentiment analysis error:', error);
      return this.getDefaultSentiment();
    }
  }

  // Fetch news data (mock implementation)
  async fetchNewsData(symbol) {
    try {
      // Mock news data for development
      const mockNews = [
        {
          title: 'EUR/USD rises on positive economic data',
          content: 'The Euro strengthened against the Dollar following strong GDP growth',
          timestamp: new Date().toISOString(),
          source: 'ForexNews',
          impact: 'high'
        },
        {
          title: 'Federal Reserve maintains dovish stance',
          content: 'The Fed signals continued support for economic recovery',
          timestamp: new Date().toISOString(),
          source: 'CentralBank',
          impact: 'medium'
        },
        {
          title: 'European Central Bank policy update',
          content: 'ECB maintains current monetary policy amid inflation concerns',
          timestamp: new Date().toISOString(),
          source: 'ECB',
          impact: 'high'
        }
      ];

      return mockNews;
    } catch (error) {
      logger.error('Error fetching news data:', error);
      return [];
    }
  }

  // Calculate sentiment from news data
  calculateSentiment(newsData) {
    let positiveScore = 0;
    let negativeScore = 0;
    let neutralScore = 0;
    let totalWeight = 0;

    newsData.forEach(news => {
      const text = `${news.title} ${news.content}`.toLowerCase();
      const weight = this.getNewsWeight(news.impact);
      
      let newsScore = 0;
      
      // Check positive keywords
      this.sentimentKeywords.positive.forEach(keyword => {
        if (text.includes(keyword.toLowerCase())) {
          newsScore += 1;
        }
      });

      // Check negative keywords
      this.sentimentKeywords.negative.forEach(keyword => {
        if (text.includes(keyword.toLowerCase())) {
          newsScore -= 1;
        }
      });

      // Apply weight and add to totals
      if (newsScore > 0) {
        positiveScore += newsScore * weight;
      } else if (newsScore < 0) {
        negativeScore += Math.abs(newsScore) * weight;
      } else {
        neutralScore += weight;
      }

      totalWeight += weight;
    });

    // Calculate overall sentiment
    const totalPositive = positiveScore;
    const totalNegative = negativeScore;
    const totalNeutral = neutralScore;

    let overall = 'neutral';
    let confidence = 0.5;

    if (totalPositive > totalNegative && totalPositive > totalNeutral) {
      overall = 'bullish';
      confidence = Math.min(0.9, 0.5 + (totalPositive / totalWeight) * 0.4);
    } else if (totalNegative > totalPositive && totalNegative > totalNeutral) {
      overall = 'bearish';
      confidence = Math.min(0.9, 0.5 + (totalNegative / totalWeight) * 0.4);
    } else {
      confidence = 0.3 + (totalNeutral / totalWeight) * 0.2;
    }

    return {
      overall,
      confidence,
      scores: {
        positive: totalPositive,
        negative: totalNegative,
        neutral: totalNeutral
      },
      newsCount: newsData.length,
      timestamp: new Date().toISOString()
    };
  }

  // Get news weight based on impact
  getNewsWeight(impact) {
    switch (impact?.toLowerCase()) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  }

  // Get default sentiment when no data available
  getDefaultSentiment() {
    return {
      overall: 'neutral',
      confidence: 0.5,
      scores: {
        positive: 0,
        negative: 0,
        neutral: 1
      },
      newsCount: 0,
      timestamp: new Date().toISOString()
    };
  }

  // Get economic calendar events
  async getEconomicCalendar(date = new Date()) {
    try {
      const cacheKey = `economic_calendar_${date.toDateString()}`;
      
      // Check cache first
      const cachedEvents = await cacheUtils.get(cacheKey);
      if (cachedEvents) {
        return cachedEvents;
      }

      // Mock economic events
      const mockEvents = [
        {
          time: '10:00',
          currency: 'USD',
          event: 'Non-Farm Payrolls',
          impact: 'high',
          forecast: '200K',
          previous: '180K',
          actual: null
        },
        {
          time: '14:30',
          currency: 'EUR',
          event: 'ECB Interest Rate Decision',
          impact: 'high',
          forecast: '0.00%',
          previous: '0.00%',
          actual: null
        },
        {
          time: '16:00',
          currency: 'GBP',
          event: 'GDP Growth Rate',
          impact: 'medium',
          forecast: '0.2%',
          previous: '0.1%',
          actual: null
        }
      ];

      // Cache for 4 hours
      await cacheUtils.set(cacheKey, mockEvents, 14400);

      return mockEvents;
    } catch (error) {
      logger.error('Error getting economic calendar:', error);
      return [];
    }
  }

  // Analyze news impact on specific currency pair
  async analyzeNewsImpact(symbol, timeframe = '1h') {
    try {
      const sentiment = await this.analyzeMarketSentiment(symbol);
      const calendar = await this.getEconomicCalendar();

      // Filter relevant events for the currency pair
      const relevantEvents = calendar.filter(event => {
        const currencies = symbol.replace('/', '').match(/.{1,3}/g);
        return currencies.includes(event.currency);
      });

      // Calculate impact score
      let impactScore = 0;
      relevantEvents.forEach(event => {
        switch (event.impact) {
          case 'high': impactScore += 3; break;
          case 'medium': impactScore += 2; break;
          case 'low': impactScore += 1; break;
        }
      });

      return {
        sentiment,
        upcomingEvents: relevantEvents,
        impactScore,
        recommendation: this.getNewsRecommendation(sentiment, impactScore),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error analyzing news impact:', error);
      return null;
    }
  }

  // Get trading recommendation based on news analysis
  getNewsRecommendation(sentiment, impactScore) {
    let recommendation = 'hold';
    let confidence = sentiment.confidence;

    if (sentiment.overall === 'bullish' && impactScore >= 3) {
      recommendation = 'buy';
      confidence = Math.min(0.9, confidence + 0.1);
    } else if (sentiment.overall === 'bearish' && impactScore >= 3) {
      recommendation = 'sell';
      confidence = Math.min(0.9, confidence + 0.1);
    } else if (impactScore >= 5) {
      recommendation = 'caution';
      confidence = 0.7;
    }

    return {
      action: recommendation,
      confidence,
      reason: `Based on ${sentiment.overall} sentiment and impact score of ${impactScore}`
    };
  }
}

export default new NewsAnalysisService();
