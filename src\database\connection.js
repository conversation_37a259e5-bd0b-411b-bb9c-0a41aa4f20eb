import mongoose from 'mongoose';
import config from '../config/config.js';
import logger from '../utils/logger.js';

let isConnected = false;

export const connectDatabase = async () => {
  try {
    if (isConnected) {
      logger.info('Database already connected');
      return;
    }

    // Set mongoose options
    mongoose.set('strictQuery', false);

    // Connect to MongoDB
    await mongoose.connect(config.database.mongodb.uri, config.database.mongodb.options);

    isConnected = true;
    logger.info('✅ Successfully connected to MongoDB');

    // Handle connection events
    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB connection error:', error);
      isConnected = false;
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
      isConnected = false;
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('MongoDB reconnected');
      isConnected = true;
    });

  } catch (error) {
    logger.error('❌ Failed to connect to MongoDB:', error);
    throw error;
  }
};

export const disconnectDatabase = async () => {
  try {
    if (!isConnected) {
      return;
    }

    await mongoose.disconnect();
    isConnected = false;
    logger.info('✅ Disconnected from MongoDB');
  } catch (error) {
    logger.error('❌ Error disconnecting from MongoDB:', error);
    throw error;
  }
};

export const getConnectionStatus = () => {
  return {
    isConnected,
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name
  };
};

// Graceful shutdown
process.on('SIGINT', async () => {
  await disconnectDatabase();
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
});

export default {
  connectDatabase,
  disconnectDatabase,
  getConnectionStatus
};
