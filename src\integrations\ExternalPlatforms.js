import express from 'express';
import axios from 'axios';
import crypto from 'crypto';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';
import AdvancedNotificationService from '../services/AdvancedNotificationService.js';

class ExternalPlatforms extends EventEmitter {
  constructor() {
    super();
    this.platforms = new Map();
    this.webhookRoutes = new Map();
    this.isReady = false;
  }

  // تهيئة التكامل مع المنصات الخارجية
  async initialize(app) {
    try {
      logger.info('Initializing External Platforms Integration...');

      // تهيئة TradingView
      await this.initializeTradingView(app);

      // تهيئة MT4/MT5
      await this.initializeMetaTrader();

      // تهيئة منصات التداول الأخرى
      await this.initializeOtherPlatforms();

      // إعداد Webhook Routes
      this.setupWebhookRoutes(app);

      this.isReady = true;
      logger.info('✅ External Platforms Integration initialized successfully');

      return true;
    } catch (error) {
      logger.error('❌ External Platforms Integration initialization failed:', error);
      throw error;
    }
  }

  // تهيئة TradingView
  async initializeTradingView(app) {
    const tradingViewConfig = {
      name: 'TradingView',
      webhookPath: '/webhook/tradingview',
      secretKey: process.env.TRADINGVIEW_SECRET || 'ayopfx-tradingview-secret',
      supportedActions: ['buy', 'sell', 'close', 'alert'],
      status: 'active'
    };

    this.platforms.set('tradingview', tradingViewConfig);

    // إعداد Webhook لـ TradingView
    app.post('/webhook/tradingview', express.json(), async (req, res) => {
      try {
        await this.handleTradingViewWebhook(req, res);
      } catch (error) {
        logger.error('TradingView webhook error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    logger.info('TradingView integration initialized');
  }

  // معالجة Webhook من TradingView
  async handleTradingViewWebhook(req, res) {
    const { body, headers } = req;
    
    // التحقق من التوقيع
    if (!this.verifyTradingViewSignature(body, headers)) {
      logger.warn('Invalid TradingView webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // تحليل البيانات
    const signal = this.parseTradingViewSignal(body);
    if (!signal) {
      logger.warn('Invalid TradingView signal format');
      return res.status(400).json({ error: 'Invalid signal format' });
    }

    // معالجة الإشارة
    await this.processTradingViewSignal(signal);

    res.status(200).json({ 
      status: 'success', 
      message: 'Signal received and processed',
      signalId: signal.id 
    });
  }

  // التحقق من توقيع TradingView
  verifyTradingViewSignature(body, headers) {
    const signature = headers['x-tradingview-signature'];
    if (!signature) return false;

    const platform = this.platforms.get('tradingview');
    const expectedSignature = crypto
      .createHmac('sha256', platform.secretKey)
      .update(JSON.stringify(body))
      .digest('hex');

    return signature === expectedSignature;
  }

  // تحليل إشارة TradingView
  parseTradingViewSignal(data) {
    try {
      // تنسيق إشارة TradingView المتوقع
      const requiredFields = ['action', 'symbol', 'price'];
      
      for (const field of requiredFields) {
        if (!data[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      return {
        id: crypto.randomUUID(),
        source: 'TradingView',
        action: data.action.toLowerCase(),
        symbol: data.symbol.toUpperCase(),
        price: parseFloat(data.price),
        quantity: parseFloat(data.quantity) || 1,
        stopLoss: data.stop_loss ? parseFloat(data.stop_loss) : null,
        takeProfit: data.take_profit ? parseFloat(data.take_profit) : null,
        timeframe: data.timeframe || '1h',
        strategy: data.strategy || 'TradingView Alert',
        timestamp: new Date().toISOString(),
        metadata: {
          exchange: data.exchange,
          indicator: data.indicator,
          message: data.message
        }
      };
    } catch (error) {
      logger.error('Failed to parse TradingView signal:', error);
      return null;
    }
  }

  // معالجة إشارة TradingView
  async processTradingViewSignal(signal) {
    try {
      logger.info('Processing TradingView signal', signal);

      // إرسال تنبيه
      await AdvancedNotificationService.sendNotification('SIGNAL_NEW', signal);

      // إضافة الإشارة لقاعدة البيانات أو النظام
      this.emit('signalReceived', {
        source: 'TradingView',
        signal
      });

      // تنفيذ الإشارة إذا كان التداول التلقائي مفعل
      if (process.env.AUTO_TRADING_ENABLED === 'true') {
        await this.executeSignal(signal);
      }

    } catch (error) {
      logger.error('Failed to process TradingView signal:', error);
      throw error;
    }
  }

  // تهيئة MetaTrader
  async initializeMetaTrader() {
    const mt4Config = {
      name: 'MetaTrader 4',
      apiEndpoint: process.env.MT4_API_ENDPOINT,
      accountId: process.env.MT4_ACCOUNT_ID,
      password: process.env.MT4_PASSWORD,
      server: process.env.MT4_SERVER,
      status: process.env.MT4_API_ENDPOINT ? 'active' : 'disabled'
    };

    const mt5Config = {
      name: 'MetaTrader 5',
      apiEndpoint: process.env.MT5_API_ENDPOINT,
      accountId: process.env.MT5_ACCOUNT_ID,
      password: process.env.MT5_PASSWORD,
      server: process.env.MT5_SERVER,
      status: process.env.MT5_API_ENDPOINT ? 'active' : 'disabled'
    };

    this.platforms.set('mt4', mt4Config);
    this.platforms.set('mt5', mt5Config);

    if (mt4Config.status === 'active') {
      await this.testMetaTraderConnection('mt4');
    }

    if (mt5Config.status === 'active') {
      await this.testMetaTraderConnection('mt5');
    }

    logger.info('MetaTrader integration initialized');
  }

  // اختبار اتصال MetaTrader
  async testMetaTraderConnection(platform) {
    try {
      const config = this.platforms.get(platform);
      
      const response = await axios.post(`${config.apiEndpoint}/login`, {
        account: config.accountId,
        password: config.password,
        server: config.server
      }, { timeout: 10000 });

      if (response.data.success) {
        logger.info(`${config.name} connection successful`);
        config.status = 'connected';
      } else {
        throw new Error(response.data.error || 'Connection failed');
      }
    } catch (error) {
      logger.error(`${platform.toUpperCase()} connection failed:`, error.message);
      const config = this.platforms.get(platform);
      config.status = 'error';
    }
  }

  // تهيئة منصات التداول الأخرى
  async initializeOtherPlatforms() {
    // تكامل مع Binance
    if (process.env.BINANCE_API_KEY) {
      const binanceConfig = {
        name: 'Binance',
        apiKey: process.env.BINANCE_API_KEY,
        secretKey: process.env.BINANCE_SECRET_KEY,
        baseUrl: 'https://api.binance.com',
        status: 'active'
      };
      this.platforms.set('binance', binanceConfig);
      await this.testBinanceConnection();
    }

    // تكامل مع OANDA
    if (process.env.OANDA_API_KEY) {
      const oandaConfig = {
        name: 'OANDA',
        apiKey: process.env.OANDA_API_KEY,
        accountId: process.env.OANDA_ACCOUNT_ID,
        baseUrl: process.env.OANDA_ENVIRONMENT === 'live' 
          ? 'https://api-fxtrade.oanda.com' 
          : 'https://api-fxpractice.oanda.com',
        status: 'active'
      };
      this.platforms.set('oanda', oandaConfig);
      await this.testOandaConnection();
    }

    // تكامل مع Interactive Brokers
    if (process.env.IB_GATEWAY_HOST) {
      const ibConfig = {
        name: 'Interactive Brokers',
        host: process.env.IB_GATEWAY_HOST,
        port: process.env.IB_GATEWAY_PORT || 7497,
        clientId: process.env.IB_CLIENT_ID || 1,
        status: 'active'
      };
      this.platforms.set('ib', ibConfig);
    }

    logger.info('Other trading platforms integration initialized');
  }

  // اختبار اتصال Binance
  async testBinanceConnection() {
    try {
      const config = this.platforms.get('binance');
      const timestamp = Date.now();
      const queryString = `timestamp=${timestamp}`;
      
      const signature = crypto
        .createHmac('sha256', config.secretKey)
        .update(queryString)
        .digest('hex');

      const response = await axios.get(`${config.baseUrl}/api/v3/account`, {
        headers: {
          'X-MBX-APIKEY': config.apiKey
        },
        params: {
          timestamp,
          signature
        },
        timeout: 10000
      });

      if (response.status === 200) {
        logger.info('Binance connection successful');
        config.status = 'connected';
      }
    } catch (error) {
      logger.error('Binance connection failed:', error.message);
      const config = this.platforms.get('binance');
      config.status = 'error';
    }
  }

  // اختبار اتصال OANDA
  async testOandaConnection() {
    try {
      const config = this.platforms.get('oanda');
      
      const response = await axios.get(`${config.baseUrl}/v3/accounts/${config.accountId}`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.status === 200) {
        logger.info('OANDA connection successful');
        config.status = 'connected';
      }
    } catch (error) {
      logger.error('OANDA connection failed:', error.message);
      const config = this.platforms.get('oanda');
      config.status = 'error';
    }
  }

  // إعداد Webhook Routes
  setupWebhookRoutes(app) {
    // Webhook عام للمنصات المختلفة
    app.post('/webhook/:platform', express.json(), async (req, res) => {
      try {
        const platform = req.params.platform.toLowerCase();
        
        if (!this.platforms.has(platform)) {
          return res.status(404).json({ error: 'Platform not supported' });
        }

        await this.handleGenericWebhook(platform, req, res);
      } catch (error) {
        logger.error(`Webhook error for ${req.params.platform}:`, error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Webhook للتحديثات المباشرة
    app.post('/webhook/price-update', express.json(), async (req, res) => {
      try {
        await this.handlePriceUpdate(req.body);
        res.status(200).json({ status: 'success' });
      } catch (error) {
        logger.error('Price update webhook error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    logger.info('Webhook routes configured');
  }

  // معالجة Webhook عام
  async handleGenericWebhook(platform, req, res) {
    const { body } = req;
    
    logger.info(`Received webhook from ${platform}`, body);

    // معالجة حسب المنصة
    switch (platform) {
      case 'mt4':
      case 'mt5':
        await this.handleMetaTraderWebhook(platform, body);
        break;
      case 'binance':
        await this.handleBinanceWebhook(body);
        break;
      case 'oanda':
        await this.handleOandaWebhook(body);
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    res.status(200).json({ 
      status: 'success', 
      message: 'Webhook processed successfully' 
    });
  }

  // معالجة تحديث الأسعار
  async handlePriceUpdate(data) {
    const { symbol, price, timestamp } = data;
    
    // إرسال التحديث للعملاء المتصلين
    if (global.io) {
      global.io.emit('price_update', {
        symbol,
        price,
        timestamp: timestamp || new Date().toISOString()
      });
    }

    this.emit('priceUpdate', { symbol, price, timestamp });
  }

  // تنفيذ الإشارة
  async executeSignal(signal) {
    try {
      logger.info('Executing signal', signal);

      // اختيار المنصة المناسبة للتنفيذ
      const platform = this.selectExecutionPlatform(signal.symbol);
      
      if (!platform) {
        throw new Error('No suitable platform found for execution');
      }

      // تنفيذ الصفقة
      const result = await this.executeTrade(platform, signal);
      
      logger.info('Signal executed successfully', result);
      
      // إرسال تنبيه بالتنفيذ
      await AdvancedNotificationService.sendPositionOpenedNotification(result);

      return result;
    } catch (error) {
      logger.error('Failed to execute signal:', error);
      
      // إرسال تنبيه بالخطأ
      await AdvancedNotificationService.sendNotification('EXECUTION_ERROR', {
        signal,
        error: error.message
      });
      
      throw error;
    }
  }

  // اختيار منصة التنفيذ
  selectExecutionPlatform(symbol) {
    // منطق اختيار المنصة حسب الرمز والتوفر
    const availablePlatforms = Array.from(this.platforms.entries())
      .filter(([name, config]) => config.status === 'connected');

    if (availablePlatforms.length === 0) {
      return null;
    }

    // اختيار المنصة الأولى المتاحة (يمكن تحسين هذا المنطق)
    return availablePlatforms[0][0];
  }

  // تنفيذ الصفقة
  async executeTrade(platform, signal) {
    const config = this.platforms.get(platform);
    
    switch (platform) {
      case 'mt4':
      case 'mt5':
        return await this.executeMetaTraderTrade(config, signal);
      case 'binance':
        return await this.executeBinanceTrade(config, signal);
      case 'oanda':
        return await this.executeOandaTrade(config, signal);
      default:
        throw new Error(`Execution not implemented for platform: ${platform}`);
    }
  }

  // تنفيذ صفقة MetaTrader
  async executeMetaTraderTrade(config, signal) {
    const orderData = {
      symbol: signal.symbol,
      action: signal.action === 'buy' ? 'BUY' : 'SELL',
      volume: signal.quantity,
      price: signal.price,
      stopLoss: signal.stopLoss,
      takeProfit: signal.takeProfit,
      comment: `AyoPFX - ${signal.strategy}`
    };

    const response = await axios.post(`${config.apiEndpoint}/order`, orderData);
    
    if (!response.data.success) {
      throw new Error(response.data.error || 'Order execution failed');
    }

    return {
      platform: config.name,
      orderId: response.data.orderId,
      symbol: signal.symbol,
      action: signal.action,
      quantity: signal.quantity,
      price: response.data.executionPrice || signal.price,
      timestamp: new Date().toISOString()
    };
  }

  // الحصول على حالة المنصات
  getPlatformsStatus() {
    const status = {};
    
    this.platforms.forEach((config, name) => {
      status[name] = {
        name: config.name,
        status: config.status,
        lastCheck: config.lastCheck || null,
        features: config.supportedActions || []
      };
    });

    return {
      platforms: status,
      totalPlatforms: this.platforms.size,
      activePlatforms: Array.from(this.platforms.values())
        .filter(p => p.status === 'connected').length,
      isReady: this.isReady
    };
  }

  // إنشاء إشارة تجريبية
  createTestSignal() {
    return {
      id: crypto.randomUUID(),
      source: 'Test',
      action: 'buy',
      symbol: 'EURUSD',
      price: 1.1000,
      quantity: 1,
      stopLoss: 1.0950,
      takeProfit: 1.1100,
      timeframe: '1h',
      strategy: 'Test Signal',
      timestamp: new Date().toISOString()
    };
  }
}

export default new ExternalPlatforms();
