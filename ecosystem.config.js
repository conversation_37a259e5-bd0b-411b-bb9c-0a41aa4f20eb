module.exports = {
  apps: [
    {
      // إعدادات التطبيق الرئيسي
      name: 'ayopfx-trading-bot',
      script: 'src/app.js',
      
      // إعدادات البيئة
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // إعدادات الأداء
      instances: 1, // عدد النسخ المتوازية
      exec_mode: 'fork', // وضع التشغيل
      
      // إعدادات إعادة التشغيل
      autorestart: true,
      watch: false, // مراقبة الملفات للتطوير فقط
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // إعدادات السجلات
      log_file: './logs/pm2-combined.log',
      out_file: './logs/pm2-out.log',
      error_file: './logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // إعدادات المراقبة
      min_uptime: '10s',
      max_restarts: 10,
      
      // متغيرات البيئة الإضافية
      env_vars: {
        TZ: 'UTC'
      },
      
      // إعدادات الصحة
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // إعدادات التطوير
      ignore_watch: [
        'node_modules',
        'logs',
        'models',
        'backups',
        '.git'
      ],
      
      // إعدادات إضافية
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // معلومات إضافية
      vizion: false,
      automation: false
    }
  ],

  // إعدادات النشر (اختيارية)
  deploy: {
    production: {
      user: 'ayopfx',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/ayopfx/trading-bot.git',
      path: '/var/www/ayopfx-trading-bot',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'ayopfx',
      host: ['staging-server.com'],
      ref: 'origin/develop',
      repo: 'https://github.com/ayopfx/trading-bot.git',
      path: '/var/www/ayopfx-trading-bot-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
