# 🚀 كيفية تشغيل AyoPFX Trading Bot

<div align="center">

![AyoPFX](https://img.shields.io/badge/AyoPFX-Trading%20Bot-blue?style=for-the-badge&logo=robot)
![Ready](https://img.shields.io/badge/Ready%20to%20Run-green?style=for-the-badge&logo=play)

**دليل شامل لتشغيل البوت بجميع الطرق الممكنة**

</div>

---

## ⚡ التشغيل السريع (30 ثانية)

### 🎯 **الطريقة الأسهل:**

#### على Windows:
```cmd
# انقر مرتين على:
start-bot.bat
```

#### على Mac/Linux:
```bash
# في Terminal:
./start-bot.sh
```

#### أو ببساطة:
```bash
npm start
```

### 🌐 **افتح المتصفح على:**
**http://localhost:3000**

---

## 🛠️ جميع طرق التشغيل

### 1️⃣ **التشغيل العادي (للمبتدئين)**
```bash
# البوت البسيط (موصى به للبداية)
npm start

# أو مباشرة:
node simple-bot.js
```

### 2️⃣ **التشغيل المتقدم (للمحترفين)**
```bash
# البوت المتقدم مع جميع الميزات
npm run start:advanced

# أو مباشرة:
node src/app.js
```

### 3️⃣ **التشغيل مع مراقبة الملفات (للتطوير)**
```bash
# يعيد تشغيل البوت عند تغيير الملفات
npm run dev

# أو للبوت المتقدم:
npm run dev:advanced
```

### 4️⃣ **التشغيل الاحترافي (PM2)**
```bash
# تثبيت PM2 أولاً
npm install -g pm2

# تشغيل البوت
npm run pm2:start

# مراقبة الحالة
npm run pm2:logs

# إيقاف البوت
npm run pm2:stop
```

### 5️⃣ **التشغيل بـ Docker**
```bash
# بناء وتشغيل
npm run docker:build
npm run docker:run

# أو مع Docker Compose (كامل)
npm run docker:compose
```

---

## 🔧 إعدادات التشغيل

### تغيير المنفذ:
```bash
# Windows:
set PORT=8080 && npm start

# Mac/Linux:
PORT=8080 npm start
```

### تفعيل وضع التشخيص:
```bash
# Windows:
set DEBUG=ayopfx:* && npm start

# Mac/Linux:
DEBUG=ayopfx:* npm start
```

### تشغيل في الخلفية:
```bash
# Windows:
start /B npm start

# Mac/Linux:
nohup npm start &
```

---

## 🌐 الروابط المتاحة

بعد تشغيل البوت، ستكون هذه الروابط متاحة:

### 🏠 **الواجهة الرئيسية:**
- **الصفحة الرئيسية**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3000/api/dashboard

### 🔍 **فحص النظام:**
- **فحص الصحة**: http://localhost:3000/health
- **حالة النظام**: http://localhost:3000/api/status

### 📊 **البيانات والتحليل:**
- **الإشارات**: http://localhost:3000/api/signals
- **التحليل**: http://localhost:3000/api/analysis/EURUSD/1h
- **الإحصائيات**: http://localhost:3000/api/analytics

### 🔗 **Webhooks:**
- **TradingView**: http://localhost:3000/webhook/tradingview
- **عام**: http://localhost:3000/webhook/:platform

---

## 🛠️ حل المشاكل الشائعة

### ❌ **"npm is not recognized"**
**المشكلة:** Node.js غير مثبت
**الحل:**
1. حمل Node.js من: https://nodejs.org
2. ثبت البرنامج
3. أعد تشغيل Command Prompt/Terminal

### ❌ **"Port 3000 is already in use"**
**المشكلة:** المنفذ مستخدم من برنامج آخر
**الحل:**
```bash
# استخدم منفذ آخر:
PORT=3001 npm start

# أو أوقف البرنامج الآخر:
# Windows: netstat -ano | findstr :3000
# Mac/Linux: lsof -i :3000
```

### ❌ **"Cannot find module"**
**المشكلة:** التبعيات غير مثبتة
**الحل:**
```bash
# احذف وأعد التثبيت:
rm -rf node_modules package-lock.json
npm install
```

### ❌ **"Permission denied" (Mac/Linux)**
**المشكلة:** أذونات الملف
**الحل:**
```bash
chmod +x start-bot.sh
./start-bot.sh
```

### ❌ **البوت يتوقف فجأة**
**المشكلة:** خطأ في الكود أو نقص ذاكرة
**الحل:**
```bash
# تحقق من السجلات:
tail -f logs/combined.log

# أو زيد الذاكرة:
export NODE_OPTIONS="--max-old-space-size=4096"
npm start
```

---

## 📱 التحكم في البوت

### ⏹️ **إيقاف البوت:**
- اضغط `Ctrl + C` في Terminal/Command Prompt
- أو أغلق نافذة Terminal

### 🔄 **إعادة التشغيل:**
```bash
# أوقف البوت أولاً (Ctrl+C)
# ثم شغله مرة أخرى:
npm start
```

### 📊 **مراقبة البوت:**
```bash
# عرض السجلات:
tail -f logs/combined.log

# مع PM2:
pm2 logs ayopfx-trading-bot

# مراقبة الموارد:
pm2 monit
```

---

## ⚙️ التخصيص والإعدادات

### 📧 **تفعيل التنبيهات:**
```bash
# انسخ ملف الإعدادات:
cp .env.example .env

# حرر الملف وأضف:
TELEGRAM_BOT_TOKEN=your_bot_token
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

### 🔑 **إضافة مفاتيح API:**
```env
# في ملف .env:
ALPHA_VANTAGE_API_KEY=your_key
FINNHUB_API_KEY=your_key
TWELVE_DATA_API_KEY=your_key
```

### 🎯 **تخصيص إعدادات التداول:**
```env
# في ملف .env:
AUTO_TRADING_ENABLED=false
MAX_CONCURRENT_TRADES=5
MAX_RISK_PER_TRADE=0.02
```

---

## 📋 قائمة التحقق

قبل التشغيل، تأكد من:

- [ ] ✅ **Node.js مثبت** (`node --version`)
- [ ] ✅ **npm يعمل** (`npm --version`)
- [ ] ✅ **الملفات موجودة** (package.json, simple-bot.js)
- [ ] ✅ **التبعيات مثبتة** (`npm install`)
- [ ] ✅ **المنفذ 3000 متاح** (أو استخدم منفذ آخر)

بعد التشغيل، تحقق من:

- [ ] ✅ **البوت يعمل** (لا توجد أخطاء في Terminal)
- [ ] ✅ **الواجهة تفتح** (http://localhost:3000)
- [ ] ✅ **API يعمل** (http://localhost:3000/health)
- [ ] ✅ **البيانات تظهر** (إشارات، أخبار، إحصائيات)

---

## 🆘 الحصول على المساعدة

### 📞 **الدعم السريع:**

| المشكلة | الحل |
|---------|------|
| **البوت لا يبدأ** | `npm install && npm start` |
| **الصفحة لا تفتح** | تحقق من http://localhost:3000 |
| **خطأ في المنفذ** | `PORT=3001 npm start` |
| **أخطاء في الكود** | راجع logs/combined.log |
| **مشاكل أخرى** | راجع [الدليل الشامل](INSTALLATION.md) |

### 💬 **تواصل معنا:**
- **📧 البريد الإلكتروني**: <EMAIL>
- **💬 Telegram**: [@ayopfx_support](https://t.me/ayopfx_support)
- **🐛 GitHub**: [إبلاغ عن مشكلة](https://github.com/ayopfx/trading-bot/issues)
- **📚 الوثائق**: [دليل المستخدم](README.md)

---

## 🎯 الخطوات التالية

بعد تشغيل البوت بنجاح:

### 1️⃣ **استكشف الميزات:**
- جرب لوحة التحكم
- راقب الإشارات
- اقرأ الأخبار
- تحقق من الإحصائيات

### 2️⃣ **خصص الإعدادات:**
- فعل التنبيهات
- أضف مفاتيح API
- اختر أزواج العملات المفضلة

### 3️⃣ **تعلم وطور:**
- اقرأ الوثائق
- انضم للمجتمع
- شارك في المناقشات
- طور استراتيجيات جديدة

---

<div align="center">

## 🎉 مبروك! البوت يعمل الآن

![Success](https://img.shields.io/badge/Status-Running-success?style=for-the-badge&logo=check-circle)
![Trading](https://img.shields.io/badge/Ready-To%20Trade-blue?style=for-the-badge&logo=trending-up)

**استمتع بالتداول الذكي مع AyoPFX! 🚀**

---

### 🔗 **روابط سريعة:**
[![Dashboard](https://img.shields.io/badge/Dashboard-Open-blue?style=flat-square)](http://localhost:3000) 
[![Health](https://img.shields.io/badge/Health-Check-green?style=flat-square)](http://localhost:3000/health) 
[![API](https://img.shields.io/badge/API-Test-orange?style=flat-square)](http://localhost:3000/api/status)
[![Support](https://img.shields.io/badge/Support-Get%20Help-red?style=flat-square)](https://t.me/ayopfx_support)

</div>
