import express from 'express';
import crypto from 'crypto';
import { catchAsync, AppError } from '../middleware/errorHandler.js';
import { strictRateLimitMiddleware } from '../middleware/rateLimiter.js';
import logger from '../utils/logger.js';
import config from '../config/config.js';

const router = express.Router();

// Apply strict rate limiting to webhook endpoints
router.use(strictRateLimitMiddleware);

// Webhook signature verification middleware
const verifyWebhookSignature = (req, res, next) => {
  if (!config.tradingView.secret) {
    logger.warn('TradingView webhook secret not configured');
    return next();
  }

  const signature = req.headers['x-tradingview-signature'];
  if (!signature) {
    throw new AppError('Missing webhook signature', 401);
  }

  const expectedSignature = crypto
    .createHmac('sha256', config.tradingView.secret)
    .update(JSON.stringify(req.body))
    .digest('hex');

  if (signature !== expectedSignature) {
    throw new AppError('Invalid webhook signature', 401);
  }

  next();
};

// TradingView webhook endpoint
router.post('/tradingview', verifyWebhookSignature, catchAsync(async (req, res) => {
  const webhookData = req.body;
  
  logger.info('TradingView webhook received', {
    symbol: webhookData.symbol,
    action: webhookData.action,
    timestamp: webhookData.timestamp
  });

  try {
    // Validate webhook data
    if (!webhookData.symbol || !webhookData.action) {
      throw new AppError('Invalid webhook data: missing symbol or action', 400);
    }

    // Get services from app locals
    const tradingEngine = req.app.locals.tradingEngine;
    const notificationService = req.app.locals.notificationService;

    // Process the webhook based on action type
    switch (webhookData.action.toLowerCase()) {
      case 'buy':
      case 'sell':
        await processTradingSignal(webhookData, tradingEngine, notificationService);
        break;
      
      case 'alert':
        await processPriceAlert(webhookData, notificationService);
        break;
      
      case 'analysis':
        await processAnalysisRequest(webhookData, tradingEngine);
        break;
      
      default:
        logger.warn(`Unknown webhook action: ${webhookData.action}`);
        throw new AppError(`Unsupported webhook action: ${webhookData.action}`, 400);
    }

    res.json({
      status: 'success',
      message: 'Webhook processed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('TradingView webhook processing error:', error);
    throw new AppError('Webhook processing failed', 500);
  }
}));

// Process trading signal from TradingView
async function processTradingSignal(webhookData, tradingEngine, notificationService) {
  try {
    const {
      symbol,
      action,
      price,
      stopLoss,
      takeProfit,
      confidence,
      timeframe = '1h',
      strategy,
      message
    } = webhookData;

    // Validate required fields
    if (!price) {
      throw new Error('Price is required for trading signals');
    }

    // Create trade setup from webhook data
    const tradeSetup = {
      action: 'trade',
      direction: action.toLowerCase(),
      entry: {
        price: parseFloat(price),
        type: 'market',
        confidence: confidence ? parseFloat(confidence) : 0.7
      },
      stopLoss: stopLoss ? {
        price: parseFloat(stopLoss),
        type: 'webhook',
        distance: Math.abs(parseFloat(price) - parseFloat(stopLoss))
      } : null,
      takeProfits: takeProfit ? [{
        level: 'TP1',
        price: parseFloat(takeProfit),
        percentage: 100,
        confidence: 0.8
      }] : [],
      confidence: confidence ? parseFloat(confidence) : 0.7,
      reasoning: {
        entry: message || `TradingView ${strategy || 'strategy'} signal`,
        source: 'TradingView Webhook'
      },
      metadata: {
        symbol: symbol,
        timeframe: timeframe,
        timestamp: new Date().toISOString(),
        source: 'tradingview_webhook',
        strategy: strategy
      }
    };

    // Calculate risk-reward if both SL and TP are provided
    if (tradeSetup.stopLoss && tradeSetup.takeProfits.length > 0) {
      const entryPrice = tradeSetup.entry.price;
      const slPrice = tradeSetup.stopLoss.price;
      const tpPrice = tradeSetup.takeProfits[0].price;
      
      const risk = Math.abs(entryPrice - slPrice);
      const reward = Math.abs(tpPrice - entryPrice);
      tradeSetup.riskReward = reward / risk;
    }

    // Validate the trade setup
    if (!validateTradeSetup(tradeSetup)) {
      throw new Error('Invalid trade setup from webhook');
    }

    // Send notification
    await notificationService.sendTradeSignal(tradeSetup);

    // Emit signal event
    tradingEngine.emit('tradingSignal', tradeSetup);

    logger.trade('TradingView signal processed', {
      symbol: symbol,
      direction: action,
      price: price,
      source: 'webhook'
    });

  } catch (error) {
    logger.error('Trading signal processing error:', error);
    throw error;
  }
}

// Process price alert from TradingView
async function processPriceAlert(webhookData, notificationService) {
  try {
    const {
      symbol,
      price,
      targetPrice,
      direction,
      message,
      alertName
    } = webhookData;

    const alertData = {
      symbol: symbol,
      currentPrice: parseFloat(price),
      targetPrice: targetPrice ? parseFloat(targetPrice) : parseFloat(price),
      direction: direction || 'both',
      message: message || `Price alert for ${symbol}`,
      alertName: alertName,
      timestamp: new Date().toISOString()
    };

    // Calculate price change
    if (targetPrice) {
      alertData.change = alertData.currentPrice - alertData.targetPrice;
      alertData.changePercent = (alertData.change / alertData.targetPrice) * 100;
    } else {
      alertData.change = 0;
      alertData.changePercent = 0;
    }

    // Send price alert notification
    await notificationService.sendPriceAlert(alertData);

    logger.notification('TradingView price alert processed', {
      symbol: symbol,
      price: price,
      alertName: alertName
    });

  } catch (error) {
    logger.error('Price alert processing error:', error);
    throw error;
  }
}

// Process analysis request from TradingView
async function processAnalysisRequest(webhookData, tradingEngine) {
  try {
    const {
      symbol,
      timeframe = '1h',
      requestType = 'full'
    } = webhookData;

    // Validate symbol
    if (!config.trading.supportedPairs.includes(symbol)) {
      throw new Error(`Unsupported trading pair: ${symbol}`);
    }

    // Perform analysis
    const analysis = await tradingEngine.analyzeSymbol(symbol, timeframe);

    if (!analysis) {
      throw new Error(`Analysis failed for ${symbol} ${timeframe}`);
    }

    // Emit analysis event
    tradingEngine.emit('analysisUpdate', analysis);

    logger.analysis('TradingView analysis request processed', {
      symbol: symbol,
      timeframe: timeframe,
      requestType: requestType
    });

  } catch (error) {
    logger.error('Analysis request processing error:', error);
    throw error;
  }
}

// Validate trade setup
function validateTradeSetup(tradeSetup) {
  try {
    // Check required fields
    if (!tradeSetup.direction || !tradeSetup.entry || !tradeSetup.entry.price) {
      return false;
    }

    // Check direction
    if (!['buy', 'sell'].includes(tradeSetup.direction)) {
      return false;
    }

    // Check price validity
    if (tradeSetup.entry.price <= 0) {
      return false;
    }

    // Check stop loss validity
    if (tradeSetup.stopLoss) {
      if (tradeSetup.stopLoss.price <= 0) {
        return false;
      }
      
      // Stop loss should be in the opposite direction of the trade
      if (tradeSetup.direction === 'buy' && tradeSetup.stopLoss.price >= tradeSetup.entry.price) {
        return false;
      }
      if (tradeSetup.direction === 'sell' && tradeSetup.stopLoss.price <= tradeSetup.entry.price) {
        return false;
      }
    }

    // Check take profit validity
    if (tradeSetup.takeProfits && tradeSetup.takeProfits.length > 0) {
      for (const tp of tradeSetup.takeProfits) {
        if (tp.price <= 0) {
          return false;
        }
        
        // Take profit should be in the same direction as the trade
        if (tradeSetup.direction === 'buy' && tp.price <= tradeSetup.entry.price) {
          return false;
        }
        if (tradeSetup.direction === 'sell' && tp.price >= tradeSetup.entry.price) {
          return false;
        }
      }
    }

    // Check confidence
    if (tradeSetup.confidence < 0 || tradeSetup.confidence > 1) {
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Trade setup validation error:', error);
    return false;
  }
}

// Generic webhook endpoint for other services
router.post('/generic', catchAsync(async (req, res) => {
  const webhookData = req.body;
  const source = req.headers['x-webhook-source'] || 'unknown';
  
  logger.info('Generic webhook received', {
    source: source,
    data: webhookData
  });

  // Process based on source
  try {
    switch (source.toLowerCase()) {
      case 'mt4':
      case 'mt5':
        await processMetaTraderWebhook(webhookData, req.app.locals);
        break;
      
      case 'custom':
        await processCustomWebhook(webhookData, req.app.locals);
        break;
      
      default:
        logger.warn(`Unknown webhook source: ${source}`);
        // Still process as generic webhook
        await processGenericWebhook(webhookData, req.app.locals);
    }

    res.json({
      status: 'success',
      message: 'Generic webhook processed successfully',
      source: source,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Generic webhook processing error:', error);
    throw new AppError('Generic webhook processing failed', 500);
  }
}));

// Process MetaTrader webhook
async function processMetaTraderWebhook(webhookData, services) {
  try {
    const { tradingEngine, notificationService } = services;
    
    // MetaTrader webhook processing logic
    logger.info('Processing MetaTrader webhook', webhookData);
    
    // This would contain specific logic for MetaTrader webhooks
    // For now, we'll just log and notify
    await notificationService.sendSystemAlert({
      type: 'metatrader_webhook',
      message: `MetaTrader webhook received: ${JSON.stringify(webhookData)}`,
      level: 'info'
    });

  } catch (error) {
    logger.error('MetaTrader webhook processing error:', error);
    throw error;
  }
}

// Process custom webhook
async function processCustomWebhook(webhookData, services) {
  try {
    const { tradingEngine, notificationService } = services;
    
    // Custom webhook processing logic
    logger.info('Processing custom webhook', webhookData);
    
    // This would contain specific logic for custom webhooks
    // For now, we'll just log and notify
    await notificationService.sendSystemAlert({
      type: 'custom_webhook',
      message: `Custom webhook received: ${JSON.stringify(webhookData)}`,
      level: 'info'
    });

  } catch (error) {
    logger.error('Custom webhook processing error:', error);
    throw error;
  }
}

// Process generic webhook
async function processGenericWebhook(webhookData, services) {
  try {
    const { notificationService } = services;
    
    // Generic webhook processing logic
    logger.info('Processing generic webhook', webhookData);
    
    // Basic processing - just log and optionally notify
    if (webhookData.notify !== false) {
      await notificationService.sendSystemAlert({
        type: 'generic_webhook',
        message: `Generic webhook received: ${JSON.stringify(webhookData)}`,
        level: 'info'
      });
    }

  } catch (error) {
    logger.error('Generic webhook processing error:', error);
    throw error;
  }
}

// Webhook status endpoint
router.get('/status', (req, res) => {
  res.json({
    status: 'active',
    endpoints: {
      tradingview: '/webhook/tradingview',
      generic: '/webhook/generic'
    },
    security: {
      signatureVerification: !!config.tradingView.secret,
      rateLimiting: true
    },
    timestamp: new Date().toISOString()
  });
});

// Test webhook endpoint (for development)
if (config.server.env === 'development') {
  router.post('/test', catchAsync(async (req, res) => {
    const testData = req.body;
    
    logger.info('Test webhook received', testData);
    
    res.json({
      status: 'success',
      message: 'Test webhook received successfully',
      data: testData,
      timestamp: new Date().toISOString()
    });
  }));
}

export default router;
