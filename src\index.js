import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import { fileURLToPath } from 'url';

import config from './config/config.js';
import logger from './utils/logger.js';
import { connectDatabase } from './database/connection.js';
import { initializeRedis } from './database/redis.js';
import rateLimiter from './middleware/rateLimiter.js';
import errorHandler from './middleware/errorHandler.js';

// Import services
import TradingEngine from './services/TradingEngine.js';
import NotificationService from './services/NotificationService.js';
import DataService from './services/DataService.js';
import AIService from './services/AIService.js';

// Import routes
import apiRoutes from './api/routes.js';
import webhookRoutes from './api/webhook.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class AyoPFXTradingBot {
  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: config.server.corsOrigin,
        methods: ['GET', 'POST']
      }
    });
    
    this.services = {};
    this.isRunning = false;
  }

  async initialize() {
    try {
      logger.info('🚀 Initializing AyoPFX Trading Bot...');

      // Setup middleware
      this.setupMiddleware();

      // Connect to databases
      await this.connectDatabases();

      // Initialize services
      await this.initializeServices();

      // Setup routes
      this.setupRoutes();

      // Setup WebSocket handlers
      this.setupWebSocket();

      // Setup error handling
      this.setupErrorHandling();

      logger.info('✅ AyoPFX Trading Bot initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize trading bot:', error);
      throw error;
    }
  }

  setupMiddleware() {
    // Security middleware
    if (config.security.helmetEnabled) {
      this.app.use(helmet());
    }

    // CORS
    this.app.use(cors({
      origin: config.server.corsOrigin,
      credentials: true
    }));

    // Rate limiting
    if (config.security.rateLimitingEnabled) {
      this.app.use(rateLimiter);
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use(express.static(path.join(__dirname, '../public')));

    // Request logging
    this.app.use((req, res, next) => {
      logger.api(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  async connectDatabases() {
    try {
      // Connect to MongoDB
      await connectDatabase();
      logger.info('✅ Connected to MongoDB');

      // Connect to Redis
      await initializeRedis();
      logger.info('✅ Connected to Redis');
    } catch (error) {
      logger.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  async initializeServices() {
    try {
      // Initialize Data Service
      this.services.dataService = new DataService();
      await this.services.dataService.initialize();
      logger.info('✅ Data Service initialized');

      // Initialize AI Service
      this.services.aiService = new AIService();
      await this.services.aiService.initialize();
      logger.info('✅ AI Service initialized');

      // Initialize Notification Service
      this.services.notificationService = new NotificationService();
      await this.services.notificationService.initialize();
      logger.info('✅ Notification Service initialized');

      // Initialize Trading Engine
      this.services.tradingEngine = new TradingEngine({
        dataService: this.services.dataService,
        aiService: this.services.aiService,
        notificationService: this.services.notificationService,
        io: this.io
      });
      await this.services.tradingEngine.initialize();
      logger.info('✅ Trading Engine initialized');

    } catch (error) {
      logger.error('❌ Service initialization failed:', error);
      throw error;
    }
  }

  setupRoutes() {
    // API routes
    this.app.use('/api', apiRoutes);
    
    // Webhook routes
    this.app.use('/webhook', webhookRoutes);

    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        services: {
          tradingEngine: this.services.tradingEngine?.isRunning || false,
          dataService: this.services.dataService?.isConnected || false,
          aiService: this.services.aiService?.isReady || false
        }
      });
    });

    // Serve React app for all other routes
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });
  }

  setupWebSocket() {
    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);

      // Subscribe to real-time data
      socket.on('subscribe', (data) => {
        const { pairs, timeframes } = data;
        socket.join(`data:${pairs.join(',')}`);
        logger.info(`Client ${socket.id} subscribed to ${pairs.join(',')}`);
      });

      // Unsubscribe from data
      socket.on('unsubscribe', (data) => {
        const { pairs } = data;
        socket.leave(`data:${pairs.join(',')}`);
        logger.info(`Client ${socket.id} unsubscribed from ${pairs.join(',')}`);
      });

      socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });
    });
  }

  setupErrorHandling() {
    this.app.use(errorHandler);
  }

  async start() {
    try {
      await this.initialize();

      this.server.listen(config.server.port, () => {
        logger.info(`🌟 AyoPFX Trading Bot server running on port ${config.server.port}`);
        logger.info(`🌐 Environment: ${config.server.env}`);
        logger.info(`📊 Supported pairs: ${config.trading.supportedPairs.join(', ')}`);
        this.isRunning = true;
      });

      // Start trading engine
      await this.services.tradingEngine.start();
      logger.info('🤖 Trading Engine started');

    } catch (error) {
      logger.error('❌ Failed to start trading bot:', error);
      process.exit(1);
    }
  }

  async stop() {
    try {
      logger.info('🛑 Stopping AyoPFX Trading Bot...');

      if (this.services.tradingEngine) {
        await this.services.tradingEngine.stop();
      }

      this.server.close(() => {
        logger.info('✅ Server stopped');
        this.isRunning = false;
      });

    } catch (error) {
      logger.error('❌ Error stopping trading bot:', error);
    }
  }
}

// Create and start the trading bot
const tradingBot = new AyoPFXTradingBot();

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  await tradingBot.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  await tradingBot.stop();
  process.exit(0);
});

// Start the bot
tradingBot.start().catch((error) => {
  logger.error('Failed to start trading bot:', error);
  process.exit(1);
});

export default tradingBot;
