import express from 'express';
import { catchAsync, AppError } from '../middleware/errorHandler.js';
import { apiRateLimitMiddleware } from '../middleware/rateLimiter.js';
import logger from '../utils/logger.js';
import config from '../config/config.js';

const router = express.Router();

// Apply rate limiting to all API routes
router.use(apiRateLimitMiddleware);

// API Documentation endpoint
router.get('/', (req, res) => {
  res.json({
    name: 'AyoPFX Trading Bot API',
    version: '1.0.0',
    description: 'Professional AI-powered trading bot with advanced technical analysis',
    endpoints: {
      market: '/api/market',
      analysis: '/api/analysis',
      signals: '/api/signals',
      status: '/api/status',
      notifications: '/api/notifications'
    },
    documentation: '/api/docs'
  });
});

// Market data endpoints
router.get('/market/pairs', catchAsync(async (req, res) => {
  res.json({
    status: 'success',
    data: {
      supportedPairs: config.trading.supportedPairs,
      timeframes: config.trading.analysisTimeframes,
      defaultTimeframe: config.trading.defaultTimeframe
    }
  });
}));

router.get('/market/price/:symbol', catchAsync(async (req, res) => {
  const { symbol } = req.params;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }

  // Get data service from app locals (set in main app)
  const dataService = req.app.locals.dataService;
  
  try {
    const priceData = await dataService.getRealTimePrice(symbol);
    
    res.json({
      status: 'success',
      data: {
        symbol,
        price: priceData.price,
        change: priceData.change,
        changePercent: priceData.changePercent,
        timestamp: priceData.timestamp
      }
    });
  } catch (error) {
    throw new AppError(`Unable to fetch price for ${symbol}`, 500);
  }
}));

router.get('/market/history/:symbol/:timeframe', catchAsync(async (req, res) => {
  const { symbol, timeframe } = req.params;
  const { limit = 100 } = req.query;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }
  
  if (!config.trading.analysisTimeframes.includes(timeframe)) {
    throw new AppError(`Unsupported timeframe: ${timeframe}`, 400);
  }

  const dataService = req.app.locals.dataService;
  
  try {
    const historicalData = await dataService.getHistoricalData(symbol, timeframe, parseInt(limit));
    
    res.json({
      status: 'success',
      data: {
        symbol,
        timeframe,
        data: historicalData,
        count: historicalData.length
      }
    });
  } catch (error) {
    throw new AppError(`Unable to fetch historical data for ${symbol}`, 500);
  }
}));

// Analysis endpoints
router.get('/analysis/:symbol/:timeframe', catchAsync(async (req, res) => {
  const { symbol, timeframe } = req.params;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }
  
  if (!config.trading.analysisTimeframes.includes(timeframe)) {
    throw new AppError(`Unsupported timeframe: ${timeframe}`, 400);
  }

  const tradingEngine = req.app.locals.tradingEngine;
  
  try {
    // Try to get cached analysis first
    const cachedAnalysis = await tradingEngine.getCachedAnalysis(symbol, timeframe);
    
    if (cachedAnalysis) {
      return res.json({
        status: 'success',
        data: cachedAnalysis,
        cached: true
      });
    }

    // Perform fresh analysis
    const analysis = await tradingEngine.analyzeSymbol(symbol, timeframe);
    
    if (!analysis) {
      throw new AppError(`Unable to analyze ${symbol} ${timeframe}`, 500);
    }

    res.json({
      status: 'success',
      data: analysis,
      cached: false
    });
  } catch (error) {
    throw new AppError(`Analysis failed for ${symbol} ${timeframe}`, 500);
  }
}));

router.get('/analysis/market-state/:symbol', catchAsync(async (req, res) => {
  const { symbol } = req.params;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }

  const dataService = req.app.locals.dataService;
  const marketAnalyzer = req.app.locals.marketAnalyzer;
  
  try {
    // Get recent market data
    const historicalData = await dataService.getHistoricalData(symbol, '1h', 100);
    
    if (!historicalData || historicalData.length < 50) {
      throw new AppError(`Insufficient data for market state analysis`, 500);
    }

    // Prepare market data
    const marketData = {
      opens: historicalData.map(d => d.open),
      highs: historicalData.map(d => d.high),
      lows: historicalData.map(d => d.low),
      closes: historicalData.map(d => d.close),
      volumes: historicalData.map(d => d.volume),
      timestamps: historicalData.map(d => d.timestamp)
    };

    const marketState = marketAnalyzer.analyzeMarketState(marketData, symbol);
    
    res.json({
      status: 'success',
      data: marketState
    });
  } catch (error) {
    throw new AppError(`Market state analysis failed for ${symbol}`, 500);
  }
}));

// Trading signals endpoints
router.get('/signals', catchAsync(async (req, res) => {
  const { limit = 10, symbol } = req.query;
  
  const tradingEngine = req.app.locals.tradingEngine;
  
  try {
    let signals = tradingEngine.getActiveSignals();
    
    // Filter by symbol if provided
    if (symbol) {
      if (!config.trading.supportedPairs.includes(symbol)) {
        throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
      }
      signals = signals.filter(signal => signal.metadata.symbol === symbol);
    }
    
    // Limit results
    signals = signals.slice(0, parseInt(limit));
    
    res.json({
      status: 'success',
      data: {
        signals,
        count: signals.length,
        totalActive: tradingEngine.getActiveSignals().length
      }
    });
  } catch (error) {
    throw new AppError('Unable to fetch trading signals', 500);
  }
}));

router.post('/signals/generate/:symbol/:timeframe', catchAsync(async (req, res) => {
  const { symbol, timeframe } = req.params;
  const { accountBalance = 10000, riskPercentage } = req.body;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }
  
  if (!config.trading.analysisTimeframes.includes(timeframe)) {
    throw new AppError(`Unsupported timeframe: ${timeframe}`, 400);
  }

  const tradingEngine = req.app.locals.tradingEngine;
  
  try {
    // Perform analysis
    const analysis = await tradingEngine.analyzeSymbol(symbol, timeframe);
    
    if (!analysis) {
      throw new AppError(`Unable to analyze ${symbol} ${timeframe}`, 500);
    }

    // Generate trade setup
    const riskManager = tradingEngine.riskManager;
    const tradeSetup = riskManager.generateTradeSetup(analysis, accountBalance, riskPercentage);
    
    res.json({
      status: 'success',
      data: {
        analysis,
        tradeSetup
      }
    });
  } catch (error) {
    throw new AppError(`Signal generation failed for ${symbol} ${timeframe}`, 500);
  }
}));

// System status endpoints
router.get('/status', catchAsync(async (req, res) => {
  const tradingEngine = req.app.locals.tradingEngine;
  const dataService = req.app.locals.dataService;
  const notificationService = req.app.locals.notificationService;
  const aiService = req.app.locals.aiService;
  
  const status = {
    system: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    },
    services: {
      tradingEngine: {
        status: tradingEngine?.isRunning ? 'running' : 'stopped',
        ...tradingEngine?.getStatus()
      },
      dataService: {
        status: dataService?.isConnected ? 'connected' : 'disconnected'
      },
      notificationService: {
        status: notificationService?.isInitialized ? 'ready' : 'not_ready'
      },
      aiService: {
        status: aiService?.isReady ? 'ready' : 'not_ready'
      }
    }
  };
  
  res.json({
    status: 'success',
    data: status
  });
}));

router.get('/status/performance', catchAsync(async (req, res) => {
  const tradingEngine = req.app.locals.tradingEngine;
  
  const performanceMetrics = tradingEngine?.getPerformanceMetrics() || {
    totalSignals: 0,
    successfulSignals: 0,
    failedSignals: 0,
    successRate: 0,
    averageConfidence: 0
  };
  
  res.json({
    status: 'success',
    data: performanceMetrics
  });
}));

// Notification endpoints
router.get('/notifications/test', catchAsync(async (req, res) => {
  const notificationService = req.app.locals.notificationService;
  
  if (!notificationService?.isInitialized) {
    throw new AppError('Notification service not available', 503);
  }
  
  try {
    await notificationService.testNotifications();
    
    res.json({
      status: 'success',
      message: 'Test notifications sent successfully'
    });
  } catch (error) {
    throw new AppError('Failed to send test notifications', 500);
  }
}));

router.post('/notifications/alert', catchAsync(async (req, res) => {
  const { symbol, targetPrice, direction = 'both' } = req.body;
  
  if (!symbol || !targetPrice) {
    throw new AppError('Symbol and target price are required', 400);
  }
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }

  // Store price alert (this would typically be saved to database)
  // For now, we'll just acknowledge the request
  
  res.json({
    status: 'success',
    message: 'Price alert created successfully',
    data: {
      symbol,
      targetPrice,
      direction,
      created: new Date().toISOString()
    }
  });
}));

// AI endpoints
router.get('/ai/predict/:symbol', catchAsync(async (req, res) => {
  const { symbol } = req.params;
  const { timeframe = '1h' } = req.query;
  
  if (!config.trading.supportedPairs.includes(symbol)) {
    throw new AppError(`Unsupported trading pair: ${symbol}`, 400);
  }

  const aiService = req.app.locals.aiService;
  const tradingEngine = req.app.locals.tradingEngine;
  
  if (!aiService?.isReady) {
    throw new AppError('AI service not available', 503);
  }
  
  try {
    // Get analysis for AI prediction
    const analysis = await tradingEngine.analyzeSymbol(symbol, timeframe);
    
    if (!analysis) {
      throw new AppError(`Unable to analyze ${symbol} for AI prediction`, 500);
    }

    const prediction = await aiService.predictPrice(analysis);
    
    res.json({
      status: 'success',
      data: {
        symbol,
        timeframe,
        prediction,
        analysisTimestamp: analysis.timestamp
      }
    });
  } catch (error) {
    throw new AppError(`AI prediction failed for ${symbol}`, 500);
  }
}));

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Error handling for undefined routes
router.all('*', (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

export default router;
