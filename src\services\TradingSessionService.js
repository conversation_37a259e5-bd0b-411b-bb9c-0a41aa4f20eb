import moment from 'moment-timezone';
import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/config.js';

class TradingSessionService extends EventEmitter {
  constructor() {
    super();
    this.sessions = {
      sydney: {
        name: 'Sydney',
        nameAr: 'سيدني',
        timezone: 'Australia/Sydney',
        start: '22:00',
        end: '07:00',
        currency: 'AUD',
        volatility: 'low',
        pairs: ['AUDUSD', 'AUDNZD', 'AUDJPY']
      },
      tokyo: {
        name: 'Tokyo',
        nameAr: 'طوكيو',
        timezone: 'Asia/Tokyo',
        start: '00:00',
        end: '09:00',
        currency: 'JPY',
        volatility: 'medium',
        pairs: ['USDJPY', 'EURJPY', 'GBPJPY', 'AUDJPY']
      },
      london: {
        name: 'London',
        nameAr: 'لندن',
        timezone: 'Europe/London',
        start: '08:00',
        end: '17:00',
        currency: 'GBP',
        volatility: 'high',
        pairs: ['GBPUSD', 'EURGBP', 'GBPJPY', 'GBPAUD']
      },
      newyork: {
        name: 'New York',
        nameAr: 'نيويورك',
        timezone: 'America/New_York',
        start: '13:00',
        end: '22:00',
        currency: 'USD',
        volatility: 'high',
        pairs: ['EURUSD', 'GBPUSD', 'USDCAD', 'USDJPY']
      }
    };

    this.overlaps = {
      tokyoLondon: {
        name: 'Tokyo-London Overlap',
        nameAr: 'تداخل طوكيو-لندن',
        start: '08:00',
        end: '09:00',
        timezone: 'UTC',
        volatility: 'high',
        description: 'High volatility period with Asian and European markets active'
      },
      londonNewYork: {
        name: 'London-New York Overlap',
        nameAr: 'تداخل لندن-نيويورك',
        start: '13:00',
        end: '17:00',
        timezone: 'UTC',
        volatility: 'very_high',
        description: 'Highest volatility period with European and American markets active'
      }
    };
  }

  // Get current active trading sessions
  getCurrentActiveSessions() {
    const now = moment.utc();
    const activeSessions = [];

    Object.entries(this.sessions).forEach(([key, session]) => {
      if (this.isSessionActive(session, now)) {
        activeSessions.push({
          id: key,
          ...session,
          isActive: true,
          timeRemaining: this.getTimeRemaining(session, now)
        });
      }
    });

    return activeSessions;
  }

  // Check if a session is currently active
  isSessionActive(session, currentTime = moment.utc()) {
    const sessionStart = moment.tz(session.start, 'HH:mm', session.timezone).utc();
    const sessionEnd = moment.tz(session.end, 'HH:mm', session.timezone).utc();

    // Handle sessions that cross midnight
    if (sessionEnd.isBefore(sessionStart)) {
      sessionEnd.add(1, 'day');
    }

    const currentUTC = currentTime.clone();
    
    // Check if current time is within session hours
    if (sessionEnd.isBefore(sessionStart)) {
      // Session crosses midnight
      return currentUTC.isAfter(sessionStart) || currentUTC.isBefore(sessionEnd);
    } else {
      // Normal session
      return currentUTC.isBetween(sessionStart, sessionEnd);
    }
  }

  // Get time remaining in current session
  getTimeRemaining(session, currentTime = moment.utc()) {
    const sessionEnd = moment.tz(session.end, 'HH:mm', session.timezone).utc();
    
    if (sessionEnd.isBefore(currentTime)) {
      sessionEnd.add(1, 'day');
    }

    const duration = moment.duration(sessionEnd.diff(currentTime));
    
    return {
      hours: Math.floor(duration.asHours()),
      minutes: duration.minutes(),
      total: duration.asMinutes()
    };
  }

  // Get current market volatility
  getCurrentVolatility() {
    const activeSessions = this.getCurrentActiveSessions();
    const activeOverlaps = this.getActiveOverlaps();

    if (activeOverlaps.length > 0) {
      const highestVolatility = activeOverlaps.reduce((max, overlap) => {
        const volatilityLevels = { low: 1, medium: 2, high: 3, very_high: 4 };
        return volatilityLevels[overlap.volatility] > volatilityLevels[max.volatility] ? overlap : max;
      });
      return highestVolatility.volatility;
    }

    if (activeSessions.length === 0) {
      return 'very_low';
    }

    const highestVolatility = activeSessions.reduce((max, session) => {
      const volatilityLevels = { low: 1, medium: 2, high: 3 };
      return volatilityLevels[session.volatility] > volatilityLevels[max.volatility] ? session : max;
    });

    return highestVolatility.volatility;
  }

  // Get active session overlaps
  getActiveOverlaps() {
    const now = moment.utc();
    const activeOverlaps = [];

    Object.entries(this.overlaps).forEach(([key, overlap]) => {
      const overlapStart = moment.utc(overlap.start, 'HH:mm');
      const overlapEnd = moment.utc(overlap.end, 'HH:mm');

      if (now.isBetween(overlapStart, overlapEnd)) {
        activeOverlaps.push({
          id: key,
          ...overlap,
          isActive: true,
          timeRemaining: this.getOverlapTimeRemaining(overlap, now)
        });
      }
    });

    return activeOverlaps;
  }

  // Get time remaining in overlap
  getOverlapTimeRemaining(overlap, currentTime = moment.utc()) {
    const overlapEnd = moment.utc(overlap.end, 'HH:mm');
    const duration = moment.duration(overlapEnd.diff(currentTime));
    
    return {
      hours: Math.floor(duration.asHours()),
      minutes: duration.minutes(),
      total: duration.asMinutes()
    };
  }

  // Get best trading pairs for current session
  getBestTradingPairs() {
    const activeSessions = this.getCurrentActiveSessions();
    const bestPairs = new Set();

    activeSessions.forEach(session => {
      session.pairs.forEach(pair => bestPairs.add(pair));
    });

    return Array.from(bestPairs);
  }

  // Get session analysis for a specific pair
  getSessionAnalysis(symbol) {
    const activeSessions = this.getCurrentActiveSessions();
    const activeOverlaps = this.getActiveOverlaps();
    const currentVolatility = this.getCurrentVolatility();

    // Check if pair is relevant to active sessions
    const relevantSessions = activeSessions.filter(session => 
      session.pairs.includes(symbol)
    );

    // Calculate session score
    let sessionScore = 0;
    if (relevantSessions.length > 0) {
      sessionScore = relevantSessions.length * 25; // 25 points per relevant session
    }

    // Add overlap bonus
    if (activeOverlaps.length > 0) {
      sessionScore += activeOverlaps.length * 30; // 30 points per overlap
    }

    // Volatility multiplier
    const volatilityMultiplier = {
      very_low: 0.5,
      low: 0.7,
      medium: 1.0,
      high: 1.3,
      very_high: 1.5
    };

    sessionScore *= volatilityMultiplier[currentVolatility] || 1.0;

    return {
      symbol,
      sessionScore: Math.min(100, sessionScore), // Cap at 100
      currentVolatility,
      activeSessions: relevantSessions,
      activeOverlaps,
      recommendation: this.getSessionRecommendation(sessionScore, currentVolatility),
      timestamp: new Date().toISOString()
    };
  }

  // Get trading recommendation based on session analysis
  getSessionRecommendation(sessionScore, volatility) {
    let recommendation = 'avoid';
    let confidence = 0.3;

    if (sessionScore >= 80) {
      recommendation = 'excellent';
      confidence = 0.9;
    } else if (sessionScore >= 60) {
      recommendation = 'good';
      confidence = 0.75;
    } else if (sessionScore >= 40) {
      recommendation = 'moderate';
      confidence = 0.6;
    } else if (sessionScore >= 20) {
      recommendation = 'poor';
      confidence = 0.4;
    }

    return {
      action: recommendation,
      confidence,
      reason: `Session score: ${sessionScore.toFixed(1)}, Volatility: ${volatility}`
    };
  }

  // Get next session opening
  getNextSessionOpening() {
    const now = moment.utc();
    const nextSessions = [];

    Object.entries(this.sessions).forEach(([key, session]) => {
      if (!this.isSessionActive(session, now)) {
        const nextStart = moment.tz(session.start, 'HH:mm', session.timezone).utc();
        
        // If session start is before current time, it's tomorrow
        if (nextStart.isBefore(now)) {
          nextStart.add(1, 'day');
        }

        nextSessions.push({
          id: key,
          ...session,
          startsIn: moment.duration(nextStart.diff(now))
        });
      }
    });

    // Sort by time until start
    nextSessions.sort((a, b) => a.startsIn.asMinutes() - b.startsIn.asMinutes());

    return nextSessions[0] || null;
  }

  // Get comprehensive session status
  getSessionStatus() {
    const activeSessions = this.getCurrentActiveSessions();
    const activeOverlaps = this.getActiveOverlaps();
    const currentVolatility = this.getCurrentVolatility();
    const bestPairs = this.getBestTradingPairs();
    const nextSession = this.getNextSessionOpening();

    return {
      activeSessions,
      activeOverlaps,
      currentVolatility,
      bestPairs,
      nextSession,
      marketStatus: this.getMarketStatus(),
      timestamp: new Date().toISOString()
    };
  }

  // Get overall market status
  getMarketStatus() {
    const activeSessions = this.getCurrentActiveSessions();
    
    if (activeSessions.length === 0) {
      return {
        status: 'closed',
        statusAr: 'مغلق',
        description: 'All major markets are closed',
        descriptionAr: 'جميع الأسواق الرئيسية مغلقة'
      };
    } else if (activeSessions.length === 1) {
      return {
        status: 'low_activity',
        statusAr: 'نشاط منخفض',
        description: 'One major market is active',
        descriptionAr: 'سوق رئيسي واحد نشط'
      };
    } else {
      return {
        status: 'high_activity',
        statusAr: 'نشاط عالي',
        description: 'Multiple major markets are active',
        descriptionAr: 'عدة أسواق رئيسية نشطة'
      };
    }
  }
}

export default new TradingSessionService();
